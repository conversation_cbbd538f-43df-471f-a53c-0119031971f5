import { Request, Response } from 'express';
import { OrganizationModule } from '../../../modules/organization';
import { Organization } from '../../../types/organization';
import logger from '../../../config/logger';
export default async function GetOrganization (
  request: Request,
  response: Response,
): Promise<void> {
  const organization = new OrganizationModule();
  const { organization_id } = request.params;

  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }
  await organization
    .GetOrganization(organization_id)
    .then((organizations: Organization) => {
      if (organizations) {
        const orgData = {
          _id: organizations._id,
          name: organizations.name,
          thumbnail: organizations.thumbnail,
          theme: organizations.theme,
          font_type: organizations.font_type,
          font_url: organizations.font_url,
          primary: organizations.primary,
          primary_text: organizations.primary_text,
          secondary: organizations.secondary,
          secondary_text: organizations.secondary_text,
          baseCurrency: organizations.baseCurrency,
          exchangeRatio: organizations.exchangeRatio,
          measurement_id: organizations.measurement_id,
        };
        response.send({ status: 1, data: orgData });
      } else {
        response.send({ status: 0, error: 'no organisation found' });
      }
    })

    .catch((error) => {
      logger.error('Error in getOrganization', {message: error});
      response.send({ status: 0, error: 'error while getting organization' + error});
    });
  return;
}

import { ExtendedRequest } from '../../../types/extras';
import { UnitModule } from '../../../modules/units';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreUnit (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.params;
  const unit = new UnitModule(project_id);
  const trash_id = request.body.trash_id;
  await unit
    .restoreUnit(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Unit got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreUnit', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore Unit : '+ error });
    });
}

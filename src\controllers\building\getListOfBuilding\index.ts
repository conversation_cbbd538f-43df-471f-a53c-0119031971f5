import { Request, Response } from 'express';
import { buildingModule } from '../../../modules/building';
import logger from '../../../config/logger';

export async function GetListOfBuildings (request: Request, response: Response): Promise<void> {
  try {
    const { project_id } = request.params;
    const { organization_id } = request.params;
    const building = new buildingModule(project_id, organization_id);
    const buildingList = await building.GetListOfBuildings();

    if (buildingList) {
      response.status(200).json({ status: 1, data: buildingList });
    } else {
      response.status(404).json({ status: 0, error: 'Buildings not found' });
    }
  } catch (error) {
    logger.error('Internal Server Error', {message: error});
    response.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

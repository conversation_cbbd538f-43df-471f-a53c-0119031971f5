import {  Response } from 'express';
import mongoose from 'mongoose';
import { ExtendedRequest } from '../../../types/extras';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { MiniMapModule } from '../../../modules/miniMap';
import logger from '../../../config/logger';

export async function CreateMiniMap (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id;
  const organization_id = request.headers.organization as string;
  const miniMap = new MiniMapModule(project_id, organization_id);
  const id = new mongoose.Types.ObjectId();
  const requestFiles = request.files;

  if (requestFiles) {
    UploadUnitplanFiles(requestFiles, miniMap.storagepath+id)
      .then((urlObject:  { [key: string]: string }) => {
        const createMiniMapData = {
          _id: id,
          name: request.body.name,
          low_res: urlObject.low_res,
          high_res: urlObject.high_res,
          referenceId: request.body.referenceId,
          type: request.body.type,
        };
        miniMap
          .CreateMiniMap(createMiniMapData)
          .then((miniMapData) => {
            response.status(201).json({ status: 1, data: miniMapData });
          })
          .catch((error: Error) => {
            logger.error('Error while creating the minimap', {message: error});
            response
              .status(500)
              .json({ status: 0, error: 'Error while creating the minimap'+ error });
          });
      }).catch((error) => {
        logger.error('Error uploading lowRes or highRes', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error uploading lowRes or highRes'+ error });
      });
  } else {
    logger.error('Erro ocurring');
  }
}

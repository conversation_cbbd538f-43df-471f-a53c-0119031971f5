import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';

export const validateReportParams = [
  body('startDate').notEmpty().withMessage('Start date is required'),
  body('endDate').notEmpty().withMessage('End date is required'),
  body('orgId').notEmpty().withMessage('Organization ID is required'),
  body('projectId').notEmpty().withMessage('Project ID is required'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      res.status(400).json({ status: 0, error: errors.array() });
    } else {
      next();
    }
  },
];

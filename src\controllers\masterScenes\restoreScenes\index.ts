import { ExtendedRequest } from '../../../types/extras';
import { masterScene } from '../../../types/masterScene';
import { MasterSceneModule } from '../../../modules/masterScene';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreScenes (
  request: ExtendedRequest,
  response: Response,
): Promise<masterScene | void> {
  const organization_id = request.organization_id as string;
  const scene = new MasterSceneModule(organization_id);
  const trash_Id = request.body.trash_Id;
  const scene_Id = request.body.scene_Id;

  await scene
    .restoreScenes(organization_id, trash_Id, scene_Id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Scene got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error while deleting master scenes', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting master scenes  xxxx: '+ error });
    });
}

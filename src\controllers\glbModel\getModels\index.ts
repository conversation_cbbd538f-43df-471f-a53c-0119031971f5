
import { Request, Response } from 'express';
import { ModelModule } from '../../../modules/glbModel';

export async function getModels (
  request:Request,
  response:Response,
):Promise<void>{
  const { project_id} = request.params;
  const organization_id = request.headers.organization as string;
  const modelMod = new ModelModule(project_id, organization_id);

  const models = await modelMod.getModels();

  if (models) {
    response.status(200).json({ status: 1, data: models });
  } else {
    response.status(404).json({ status: 0, error: 'models not found' });
  }

}

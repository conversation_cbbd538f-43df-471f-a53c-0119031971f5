import { UserRecord } from 'firebase-admin/lib/auth/user-record';
import { Request } from 'express';
import { Role } from './organization';

export interface ExtendedRequest extends Request {
  IsAuthenticated?: UserRecord | null;
  UserRole?: Role;
  organization_id?: string | null;
  user_id?: string | null;
  hasRoleAccess?: boolean | null;
}
export interface OrganizationRequest extends Request {
  IsAuthenticated?: UserRecord | null;
  UserRole?: Role;
  organization_id: string;
}
export interface ControlerRequest extends Request {
  IsAuthenticated: UserRecord | null;
  UserRole: Role | null;
  organization_id: string | null;
}
export interface FileRequest extends ExtendedRequest {
  file?: Express.Multer.File;
}

export enum Models {
  _UNITS = '_units',
  _MASTER_SCENES = '_master_scenes',
  _PROJECT_SCENES = '_scenes',
  _LANDMARKS = '_landmarks',
  USERS = 'users',
  ORGANIZATIONS = 'organizations',
  _PROJECTS = '_projects',
  _AMENITIES = '_amenities',
  _BUILDINGS = '_buildings',
  _COMMUNITIES = '_communities',
  _UNITPLANS = '_unitplans',
  _ASSETS = '_assets',
  _LEADS = '_leads',
  _GALLERY = '_gallery',
  _MODELS = '_models',
  _MASTER_SVG = '_master_svg',
  _MINIMAP = '_minimap',
  _SVGS = '_svgs',
  _SIDEBAR = '_sidebar',
  _TRASH = '_trashes',
  _VIRTUAL_TOUR = '_virtual_tour',
  _CUSTOMUSERS = '_customusers',
  _CUSTOMTOUR = '_images',
  ICONLIBRARY = 'iconlibraries',
  JWTRECORDS = 'jwtRecords',
  SCALESET = 'scalesets',
  SESSION = 'sessions',
  SHORTURL = 'ShortUrl',
  _TRANSLATION = '_translations'
}

import { Types } from 'mongoose';
export enum LeadSource {
    WEBSITE = 'website',
    SALES_SESSION =  'sales_session',
    SDKK = 'sdk',
    SOCIAL = 'social',
    EVENTS = 'events',
    DASHBOARD = 'dashboard',
}

export enum LeadStatus {
    NEW = 'new',
    HOT = 'hot',
    WARM = 'warm',
    NOT_INTERESTED = 'not_interested',
    COLD = 'cold'
}

export enum LeadType {
    UNIT = 'unit',
    PROJECT = 'project'
}

export enum LeadProductInterest {
    INTERACTIVE_WEBSITE_TOUR = 'interactive_website_tour',
    DIGITAL_TWINS_METAVERSE_APPLICATIONS = 'digital_twins_metaverse_applications',
    REMOTE_SHOWCASE_SALES_TOOL = 'remote_showcase_sales_tool',
    WALKTHROUGH_RENDERS = 'walkthrough_renders',
    VIRTUAL_REALITY = 'virtual_reality',
    AUGMENTED_REALITY = 'augmented_reality',
    PROJECTION_MAPPING = 'projection_mapping',
    HOLOGRAM_DISPLAY = 'hologram_display',
    OTHERS = 'others'
}

export enum LeadIndustryType {
    REAL_ESTATE = 'real_estate',
    HOSPITALITY = 'hospitality',
    AVIATION = 'aviation',
    MARINE = 'marine',
    GOVERNMENT_ORG = 'government_org',
    OTHERS = 'others'
}

export type LeadInterest = {
    type: LeadType,
    project_id?: string,
    unit_id?: string
}

export type Leads = {
    _id: Types.ObjectId;
    session_id: Types.ObjectId,
    organization_id: string,
    name: string,
    phone_number: string,
    email: string,
    duration_minutes: number,
    joining_time: string,
    interested_in: Array<LeadInterest>,
    lead_product_interest:LeadProductInterest,
    lead_industry_type: LeadIndustryType,
    lead_source: LeadSource,
    start_time: string,
    user_id: string,
    next_interaction_time: string,
    last_interaction_time: string,
    end_time: string,
    status: LeadStatus,
    lead_creation_time: string,
    thread_id?: string
}

export type updateLeadDurationObj = {
    lead_id: string,
    session_id: string,
    duration_minutes: number
}
export type updateLeadInput = {
    lead_id: string,
    session_id: string,
}

export type createLeadInput = {
    name: string,
    email: string,
    phone_number: string,
    session_id: string | null,
    lead_source: LeadSource,
    organization_id: string,
    lead_product_interest:LeadProductInterest,
    lead_industry_type: LeadIndustryType,
    lead_status: LeadStatus,
    user_id : string,
    duration_minutes: number | null,
    start_time: string | null,
    end_time: string | null,
    joining_time: string | null,
    project_id?: string,
    type?: LeadType,
    unit_id?: string,
    next_interaction_time: string | null,
    last_interaction_time: string | null,
    lead_creation_time: string,

}

export type UpdateLeadInput = {
    name?: string,
    email?: string,
    phone_number?: string,
    lead_source?: LeadSource,
    lead_product_interest?:LeadProductInterest,
    lead_industry_type?: LeadIndustryType,
    lead_status?: LeadStatus,
    type?: LeadType,
    unit_id?: string,
}

export type leadAnalyticsQuery = {
    user_id?: string | string[];
    project_id?: string | string[];
    status?: string | string[];
    start_time?: string,
    lead_source?: string,
    organization_id?: string
}
export type interestedType = 'project' | 'unit';

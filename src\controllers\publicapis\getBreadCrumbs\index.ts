import { Response, Request} from 'express';
import { BreadCrumbModule } from '../../../modules/breadCrumb';
import { ProjectSceneModule } from '../../../modules/projectScene';
import logger from '../../../config/logger';
export async function getBreadCrumbs (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.params.project_id as string;
  const currentScene = request.query.currentScene as string;
  const organization_id = request.params.organization_id as string;
  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'organization_id not found'});
    return;
  }
  const masterScene = new BreadCrumbModule(organization_id, project_id);
  if (!project_id || project_id === 'undefined'){
    masterScene.findPreviousMasterScenes(currentScene, `${organization_id}_master_scenes`)
      .then((previousScenes) => {
        response.status(200).send({ status: 1, data: previousScenes });
      })
      .catch((error) => {
        console.log(error);
        response.status(400).send({ status: 0, message: error });
      });
  } else {
    const projectScene = new ProjectSceneModule(project_id, organization_id);
    const projectsceneData = await projectScene.getScene(currentScene);
    masterScene.findTopMostParentFromProjectScenes(currentScene,
      `${organization_id}_master_scenes`,
      `${project_id}_scenes`,
      projectsceneData?.root)
      .then((previousScenes) => {
        if (previousScenes[1]._id) {
          response.status(200).send({ status: 1, data: [previousScenes[1]] });
        } else {
          response.status(200).send({ status: 1, data: [] });
        }
      })
      .catch((error) => {
        logger.error('Error in getBreadCrumbs', {message: error});
        response.status(400).send({ status: 0, message: error });
      });
  }

}

import express from 'express';
import createSVG from '../controllers/projectSVG/createSVG';
import updateLayers from '../controllers/projectSVG/updateLayers';
import updateLayerValidate from '../controllers/projectSVG/updateLayers/updateLayerValidate';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import CreateSVGValidate from '../controllers/projectSVG/createSVG/CreateProjectSVGValidator';
import uploadHandler from '../helpers/uploadHandler';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { createLayers } from '../controllers/projectSVG/createLayers';
import createLayersValidate from '../controllers/projectSVG/createLayers/createLayersValidator';
import { updateSVGLayers } from '../controllers/projectSVG/updateSVGLayers';
import updateSVGLayerValidate from '../controllers/projectSVG/updateSVGLayers/updateSVGLayersValidate';
import { moveToTrash } from '../controllers/projectSVG/moveToTrash';
import moveToTrashValidate from '../controllers/projectSVG/moveToTrash/moveToTrashValidator';
import { restoreSVG } from '../controllers/projectSVG/restoreSVG';
import restoreSVGValidate from '../controllers/projectSVG/restoreSVG/restoreSVGValidator';
import updateBulkSVGValidate from '../controllers/projectSVG/bulkUpdateSVG/bulkUpdateSVGValidator';
import { updateBulkSVGs } from '../controllers/projectSVG/bulkUpdateSVG';
import updateLayersVideoValidate from '../controllers/projectSVG/updateLayersVideoTag/updatelayersVideoValidate';
import updateLayersVideoTag from '../controllers/projectSVG/updateLayersVideoTag';
import { deleteLayers } from '../controllers/projectSVG/deleteLayers';
import deleteLayersValidator from '../controllers/projectSVG/deleteLayers/deleteLayersValidator';
const router = express.Router();

router.post('/createSVG',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'svgFile' }], 'svg/')
  , CreateSVGValidate
  , createSVG);
router.post('/updateLayers', authMiddleware, organizationAccessMiddleware, accessControlMiddleware(['admin']),
  updateLayerValidate, updateLayers);
router.post('/updateLayersVideoTag', authMiddleware, uploadHandler([{name: 'video_tag'}], 'svg/'),
  organizationAccessMiddleware, accessControlMiddleware(['admin']),
  updateLayersVideoValidate, updateLayersVideoTag);
router.post('/createLayers',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'svgFile' }], 'svg/'),
  createLayersValidate,
  createLayers);
router.post('/updatesvgLayer',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'svgFile' }], 'svg/'),
  updateSVGLayerValidate,
  updateSVGLayers);
router.post(
  '/deleteLayer',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  deleteLayersValidator,
  deleteLayers,
);
router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreSVG/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreSVGValidate,
  restoreSVG,
);
router.post('/updateBulkSVG',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  updateBulkSVGValidate,
  updateBulkSVGs);
export default router;

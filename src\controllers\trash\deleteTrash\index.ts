import { Request, Response } from 'express';
import logger from '../../../config/logger';
import { trashModule } from '../../../modules/trash';

export async function deleteTrash (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  const trashId = request.body.trash_id as string[];
  const trash = new trashModule(organization_id);
  trash.deleteTrash(trashId).then(() => {
    response.status(200).json({
      status: 1,
      data: 'Trash Deleted Succesfully',
    });
  }).catch((err) => {
    logger.error('Error fetching trash data', { err });
    response.status(500).json({ status: 0, error: `Error in Delete Trash:${err}` });
  });
}

// Src/controllers/analytics/eventDetails.ts

import { Request, Response } from 'express';
import { AnalyticsModule } from '../../../modules/analytics';

export async function getEventDetails (req: Request, res: Response): Promise<void> {
  const { startDate, endDate, orgId, projectId, eventName, customFields } = req.body;
  const analyticsModule = new AnalyticsModule();
  if (!eventName || !Array.isArray(customFields)) {
    res.status(400).json({ status: 0, error: 'Invalid request body. eventName and customFields array are required.' });
    return;
  }

  try {
    const eventDetails = await analyticsModule.fetchEventDetails(
      { startDate, endDate, orgId, projectId, eventName, customFields });
    res.status(200).json({ status: 1, data: eventDetails });
  } catch (error) {
    console.error('Error fetching event details:', error);
    res.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

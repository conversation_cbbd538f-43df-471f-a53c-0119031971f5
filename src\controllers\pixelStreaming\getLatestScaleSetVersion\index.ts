import logger from '../../../config/logger';
import  { ScaleSetModule } from '../../../modules/pixelstreaming';
import { Request, Response } from 'express';
import { ProjectModule } from '../../../modules/projects';

export async function getLatestScaleSetVersion (req: Request, res: Response):Promise<void>{
  try {
    const project_id = req.body.project_id as string;
    const organization_id = req.body.organization_id as string;
    const projectModule = new ProjectModule(organization_id);
    const { resourceGroupName, vmScaleSetName } = await projectModule.getVMDetails(project_id) || {};
    if (!resourceGroupName || !vmScaleSetName) {
      res.status(500).send({message: 'Resource group name does not exist on project'});
      return;
    }
    const ScaleSetObj = new ScaleSetModule(resourceGroupName, vmScaleSetName);
    const lastScaleSet = await ScaleSetObj.getLastScaleSetVersion(project_id, organization_id);
    res.status(200).send({ message: 'Success', data: lastScaleSet });
  } catch (err){
    logger.error('Error getting latest version', {message: err});
    res.status(500).send({message: 'Error getting latest version'});
  }
}

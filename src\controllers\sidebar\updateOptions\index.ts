import logger from '../../../config/logger';
import { SidebarModule } from '../../../modules/sidebar';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
export async function updateOptions (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const sidebarMod = new SidebarModule(project_id);
  sidebarMod.updateOptions(request.body)
    .then((res) => {
      response.status(201).json({ status: 1, data: res });
    })
    .catch((error: Error) => {
      logger.error('Error while updating sidebar option', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while updating sidebar option'+ error });
    });
}

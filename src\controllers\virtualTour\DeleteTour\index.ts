import { Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function DeleteTour (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id;

  if (!organization_id) {
    response.status(500).json({ status: 0, error: 'Organization ID not found' });
    return;
  }

  const { project_id, tour_id } = request.body;

  if (!project_id || !tour_id) {
    response.status(400).json({ status: 0, error: 'Project ID or Tour ID is missing' });
    return;
  }

  try {
    const virtualTourModule = new VirtualTourModule(organization_id, project_id);

    const deletedTour = await virtualTourModule.DeleteTour(tour_id);

    if (!deletedTour) {
      response.status(404).json({ status: 0, error: 'Tour not found' });
    } else {
      response.status(200).json({ status: 1, message: 'Tour deleted successfully', data: deletedTour });
    }
  } catch (error) {
    logger.error('Error in DeleteTour controller', { message: error });
    response.status(500).json({ status: 0, error: `Error deleting tour: ${error}` });
  }
}

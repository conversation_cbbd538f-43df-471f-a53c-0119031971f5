import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { OrganizationModule } from '../../../modules/organization';
import {
  CreateOrganizationInput,
  Organization,
  User,
  UserRole,
} from '../../../types/organization';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
import { Theme } from '../../../types/projects';
export default async function CreateOrganization (req: ExtendedRequest, res: Response): Promise<void> {
  const IsAuthenticated = req.IsAuthenticated;
  if (!IsAuthenticated) {
    res.send({ status: 0, error: 'Not authorized' });
    return;
  }
  const organization = new OrganizationModule();
  const requestFiles = req.files;
  if (requestFiles === undefined) {
    res.status(400).json({ error: 'Image fields are required.' });
    return;
  }
  const organizationId = await organization.GenerateOrganizationId();
  const urlObject: { [key: string]: string } = await UploadUnitplanFiles(
    requestFiles,
    'CreationtoolAssets/' + organizationId,
  );
  const organization_data: CreateOrganizationInput = {
    name: req.body.name,
    founding_date: req.body.founding_date,
    contact_email: req.body.contact_email,
    phone_number: req.body.phone_number,
    address: req.body.address,
    website: req.body.website,
    max_users: 10,
    thumbnail: urlObject.thumbnail,
    organizationId: organizationId,
    unique_org_id: req.body.unique_org_id,
    theme: req.body.theme || Theme.DARK,
    primary: req.body.primary,
    secondary: req.body.secondary,
    primary_text: req.body.primary_text,
    secondary_text: req.body.secondary_text,
    font_type: req.body.font_type || 'Roboto',
    font_url: 'https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap',
    baseCurrency: req.body.baseCurrency || null,
  };
  organization
    .CreateOrganization(organization_data)
    .then((organizations: Organization) => {
      const organization_id = organizations._id;
      const user: User = {
        user_id: IsAuthenticated.uid,
        email: IsAuthenticated.email as string,
        first_name: IsAuthenticated.displayName || null,
        last_name: IsAuthenticated.displayName || null,
        organization_id: [organization_id],
      };
      organization
        .AddUserToOrganization(organization_id, user)
        .then(() => {
          const userRoleObj = {
            user_id: user.user_id,
            organizationId: organization_id,
            roleId: 'admin' as UserRole,
            email: user.email,
          };
          organization
            .AssignRoleToUser(userRoleObj)
            .then(() => {
              res.send({ status: 1, data: organizations });
            })
            .catch((err) => {
              logger.error('error assigning role to admin', { message: err });
              res.send({
                status: 0,
                error: 'error assigning role to admin' + err,
              });
            });
        })
        .catch((err) => {
          logger.error('error adding current user to the organization', {
            message: err,
          });
          res.send({
            status: 0,
            error: 'error adding current user to the organization' + err,
          });
        });
    })
    .catch((error) => {
      logger.error('error while creating', { message: error });
      res.send({ status: 0, error: 'error while creating' + error });
    });
}

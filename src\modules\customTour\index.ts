import mongoose from 'mongoose';
import {customTourSchema} from '../../schema/customTour/index';
import { customTourImage, createCustomTourImage, updateCustomTourImage,
  hotspot, updateHotspot } from '../../types/customTour';
import { UnknownObject, arrayToObject } from '../../helpers/dataFormatHelper';
import logger from '../../config/logger';
import { Models } from '../../types/extras';
export class customTourModule {
  private model: mongoose.Model<customTourImage>;
  public storagepath;
  constructor (organization_id: string, tour_id: string, project_id:string) {
    this.model = mongoose.model<customTourImage>(
      `${tour_id}${Models._CUSTOMTOUR}`,
      customTourSchema,
    );
    this.storagepath='CreationtoolAssets/'
      +organization_id+'/projects/'+project_id+
      '/custom_tours/'+tour_id+
      '/images';
  }
  public async createImage (
    createCusTourObj: createCustomTourImage,
  ): Promise<customTourImage | void> {
    logger.info('createImage Called', {createCusTourObj: createCusTourObj});
    return new Promise((resolve, reject) => {
      const newCustomTour = {
        _id: createCusTourObj.id,
        name: createCusTourObj.name,
        thumbnail: createCusTourObj.thumbnail,
        url: createCusTourObj.url,
      };

      const customTourModel = new this.model(newCustomTour);
      customTourModel
        .save()
        .then(() => {
          logger.info('createImage Successfull', {newCustomTour: newCustomTour});
          resolve(newCustomTour);
        })
        .catch((err) => {
          logger.error('Error in createImage', {message: err});
          reject(err);
        });
    });
  }
  public async updateImage (
    updateCusTourImgObj:updateCustomTourImage,
  )
  : Promise<customTourImage | void> {
    logger.info('updateImage Called', {updateCusTourImgObj: updateCusTourImgObj});
    try {
      const updatedTourImg = await this.model.findOneAndUpdate(
        { _id: updateCusTourImgObj.image_id },
        {
          $set: {
            'name': updateCusTourImgObj.name,
            'rotation': updateCusTourImgObj.rotation,
            'group_id': updateCusTourImgObj.group_id,
            'group_name': updateCusTourImgObj.group_name,
            'thumbnail': updateCusTourImgObj.thumbnail,
            'url': updateCusTourImgObj.url,

          },
        },
        { new: true },
      );

      if (updatedTourImg) {
        logger.info('updateImage Successfull', {updatedTourImg: updatedTourImg});
        return updatedTourImg;
      }
      logger.error('Error in updating tour image');
      throw new Error('Error in updating tour image');
    } catch (error) {
      logger.error('Internal Server Error', {message: error});
      throw new Error('Internal Server Error'+error);
    }
  }

  public async deleteImage
  (image_id:string,
  ): Promise<string> {
    logger.info('deleteImage Called', {image_id: image_id});
    return new Promise((resolve, reject) => {
      this.model.findOneAndDelete({_id: image_id}).then(() => {
        logger.info('deleteImage Successfull');
        resolve('Tour Image deleted sucessfully');
      }).catch((err) => {
        logger.error('Error in deleteImage', {message: err});
        reject(err);
      });
    });
  }
  public async getImages (): Promise<object | null> {
    logger.info('getImages Called');
    const buildingLists: Array<UnknownObject> = await this.model.find();
    const buildingListObj = arrayToObject(buildingLists) as Record<string, customTourImage>;
    console.log('%%%%%%%%%%%', buildingListObj, '%%%%%%%%%%%');
    logger.info('getImages Successfull', {buildingListObj: buildingListObj});
    return buildingListObj;
  }

  public async createHotspot (image_id: string, createHotspot: hotspot):Promise<customTourImage |null> {
    logger.info('createHotspot Called', {image_id: image_id, createHotspot: createHotspot});
    try {
      const existingImage = await this.model.findOne({ _id: image_id });

      if (existingImage) {
        if (!existingImage.hotspots) {
          existingImage.hotspots = []; // Initialize empty array if there are no hotspots
        }
        existingImage.hotspots.push(createHotspot);
        const updatedImage = await existingImage.save();
        if (updatedImage) {
          logger.info('createHotspot Successfull', {updatedImage: updatedImage});
          return updatedImage;
        }
        logger.error('Error in updating hotspot in image');
        throw new Error('Error in updating hotspot in image');

      } else {
        logger.error('Unable to find existing image');
        throw new Error('Unable to find existing image');
      }
    } catch (error) {
      logger.error('Internal Server Error', {message: error});
      throw new Error('Internal Server Error'+error);
    }
  }
  public async updateHotspot (image_id: string, hotspot_id: string, updateHotspotObj: updateHotspot)
  :Promise<customTourImage |null> {
    logger.info('updateHotspot Called',
      {image_id: image_id, hotspot_id: hotspot_id, updateHotspotObj: updateHotspotObj});
    try {
      const updatedImage = await this.model.findOneAndUpdate(
        { _id: image_id, 'hotspots': { $elemMatch: { '_id': hotspot_id } } },
        {
          $set: {
            'hotspots.$.position': updateHotspotObj.position,
            'hotspots.$.destination': updateHotspotObj.destination,
          },
        },
        { new: true },
      );
      if (updatedImage) {
        logger.info('updateHotspot Successfull', {updatedImage: updatedImage});
        return updatedImage;
      }
      logger.error('Error in updating hotspot media');
      throw new Error('Error in updating hotspot media');

    } catch (error) {
      logger.error('Internal Server Error', {message: error});
      throw new Error('Internal Server Error'+error);
    }
  }
  public async deleteHotspot (image_id: string, hotspot_id: string): Promise<customTourImage |null > {
    logger.info('deleteHotspot Called', {image_id: image_id, hotspot_id: hotspot_id});
    try {
      const updatedImage = await this.model.findOneAndUpdate(
        { _id: image_id },
        { $pull: { 'hotspots': { _id: hotspot_id } } },
        { new: true },
      );

      if (updatedImage) {
        logger.info('deleteHotspot Successfull', {updatedImage: updatedImage});
        return updatedImage;
      }
      logger.error('Error in deleting hotspot from image');
      throw new Error('Error in deleting hotspot from image');

    } catch (error) {
      logger.error('Internal Server Error', {message: error});
      throw new Error('Internal Server Error' + error);
    }
  }
}

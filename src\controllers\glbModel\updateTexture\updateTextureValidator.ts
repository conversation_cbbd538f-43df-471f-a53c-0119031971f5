import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const updateTextureValidate = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('project_id', 'Project ID is required').notEmpty(),
  body('model_id', 'Model ID is required').notEmpty(),
  body('mesh_id', 'Mesh ID is required').notEmpty(),
  body('texture_id', 'Texture ID is required').notEmpty(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default updateTextureValidate;

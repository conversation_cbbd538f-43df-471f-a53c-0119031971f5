
import { Request, Response } from 'express';
import { UnitModule } from '../../../modules/units';
import logger from '../../../config/logger';

export async function GetListofUnits (
  request:Request,
  response:Response,
):Promise<void>{
  const { project_id } = request.params;
  const unit = new UnitModule(project_id);
  const pageSize =  request.query.page ?parseInt(request.query.page as string) : -1;
  const limit =  request.query.count ? parseInt(request.query.count as string): -1;
  const searchText = request.query.search  ? request.query.search as string : '';

  const unitsList = await unit.getListOfUnits(project_id, limit, pageSize, searchText);

  if (unitsList) {
    response.status(200).json({ status: 1, data: unitsList.data });
  } else {
    logger.error('No Units found');
    response.status(404).json({ status: 0, error: 'Units not found' });
  }

}

import { NextFunction, RequestHandler, Response } from 'express';
import { admin } from '../config/firebase';
import { ExtendedRequest } from '../types/extras';

export const authMiddleware: RequestHandler = function (
  request: ExtendedRequest,
  response: Response,
  next: NextFunction,
):void{
  if (request.headers && request.headers.accesstoken) {
    const accesstoken: string | string[] = request.headers.accesstoken;
    if (accesstoken && typeof accesstoken === 'string') {
      admin
        .auth()
        .verifyIdToken(accesstoken)
        .then((decodedToken) => {
          admin
            .auth()
            .getUser(decodedToken.uid)
            .then((userRecord) => {
              request.IsAuthenticated = userRecord;
              next();
              return;
            })
            .catch((error) => {
              request.IsAuthenticated = null;
              response.status(400).send({status: 0, error: 'Not authorized' + error});
              return;
            });
        })
        .catch((error) => {
          request.IsAuthenticated = null;
          response.status(400).send({status: 0, error: 'Not authorized' + error});
          return;
        });
    } else {
      request.IsAuthenticated = null;
      response.status(400).send({status: 0, error: 'Not authorized'});
      // Next();
      return;
    }
  } else {
    request.IsAuthenticated = null;
    response.status(400).send({status: 0, error: 'Not authorized'});
    return;
  }
};

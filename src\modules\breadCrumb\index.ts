import {masterscenesSchema} from '../../schema/masterscenesSchema';
import { masterScene } from '../../types/masterScene';
import { projectScene } from '../../types/projectScene';
import { projectscenesSchema } from '../../schema/projectsceneSchema';
import mongoose, { PipelineStage } from 'mongoose';
import { Models } from '../../types/extras';
import logger from '../../config/logger';
export class BreadCrumbModule{
  private MasterModel: mongoose.Model<masterScene>;
  private ProjectModel: mongoose.Model<projectScene>;
  constructor (organization_id:string, project_id:string|boolean) {
    this.MasterModel= mongoose.model<masterScene>(`${organization_id}${Models._MASTER_SCENES}`, masterscenesSchema);
    this.ProjectModel = mongoose.model<projectScene>(`${project_id}${Models._PROJECT_SCENES}`, projectscenesSchema);
  }
  public async findAncestors (
    startSceneId: string,
    modelName: string,
  ): Promise<Array<{ _id: string; name: string }>> {
    logger.info('findAncestors Called', {id: Array});
    try {
      const aggregationPipeline: PipelineStage[] = [
        {
          $match: {
            _id: startSceneId,
          },
        },
        {
          $graphLookup: {
            from: modelName.toLowerCase(),
            startWith: '$parent',
            connectFromField: 'parent',
            connectToField: '_id',
            as: 'ancestors',
            depthField: 'depth',
          },
        },
        {
          $unwind: {
            path: '$ancestors',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $sort: {
            'ancestors.depth': -1,
          },
        },
        {
          $group: {
            _id: null,
            ancestors: {
              $push: {
                _id: '$ancestors._id',
                name: '$ancestors.name',
                type: modelName.includes('master_scenes')?'master':'project',
              },
            },
          },
        },
        {
          $project: {
            _id: 0,
            ancestors: 1,
          },
        },
      ];

      const result = await this.ProjectModel.aggregate(aggregationPipeline);
      if (result.length === 0 || !result[0].ancestors) {
        logger.info(`Scene with ID ${startSceneId} not found.`);
        throw new Error(`Scene with ID ${startSceneId} not found.`);
      }
      logger.info('findAncestors Found', {result: result[0].ancestors});
      return result[0].ancestors;
    } finally {
      // Any cleanup code if needed
    }
  }

  public async findTopMostParentFromProjectScenes (
    startSceneId: string,
    masterScenes: string,
    projectScenes: string,
    isRoute:boolean,
  ): Promise<Array<{ _id: string; name: string }>> {
    logger.info('findTopMostParentFromProjectScenes Called');
    try {
      if (isRoute){
        const masterAncestors = await this.findAncestors(startSceneId, masterScenes);

        if (masterAncestors.length === 0) {
          logger.error(`Scene with ID ${startSceneId} not found in masterScenes.`);
          throw new Error(`Scene with ID ${startSceneId} not found in masterScenes.`);
        }

        return masterAncestors;
      }

      const projectAncestors = await this.findAncestors(startSceneId, projectScenes);
      if (projectAncestors.length === 0) {
        logger.error(`Scene with ID ${startSceneId} not found in projectScenes.`);
        throw new Error(`Scene with ID ${startSceneId} not found in projectScenes.`);
      }
      const masterAncestors = await this.findAncestors(projectAncestors[0]._id, masterScenes);

      if (masterAncestors.length === 0) {
        logger.error(`Scene with ID ${projectAncestors[0]._id} not found in masterScenes.`);
        throw new Error(`Scene with ID ${projectAncestors[0]._id} not found in masterScenes.`);
      }
      logger.info('findTopMostParentFromProjectScenes Found',
        {masterAncestors: masterAncestors, projectAncestors: projectAncestors});
      return [...masterAncestors, ...projectAncestors];

    } finally {
      // Any cleanup code if needed
    }
  }

  public async findPreviousMasterScenes (sceneId: string, collectionName:string):
  Promise<Array<{ id: string; name: string }>> {
    logger.info('findPreviousMasterScenes Called');
    try {

      const aggregationPipeline:PipelineStage[] = [
        {
          $match: {
            _id: sceneId, // No need to convert to ObjectId if _id is stored as a string
          },
        },
        {
          $graphLookup: {
            from: `${collectionName.toLocaleLowerCase()}`, // Replace with your actual collection name
            startWith: '$parent',
            connectFromField: 'parent',
            connectToField: '_id',
            as: 'ancestors',
            depthField: 'depth',
          },
        },
        {
          $unwind: {
            path: '$ancestors',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $replaceRoot: {
            newRoot: '$ancestors',
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            type: 'master',
          },
        },
      ];

      const result = await this.MasterModel.aggregate(aggregationPipeline);

      if (result.length === 0) {
        logger.error(`Scene with ID ${sceneId} not found.`);
        throw new Error(`Scene with ID ${sceneId} not found.`);
      }
      logger.info('findPreviousMasterScenes Found', {result: result});
      return result.reverse();
    } finally {
      // Any cleanup code if needed
    }
  }
}

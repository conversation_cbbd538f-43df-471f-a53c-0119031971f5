import logger from '../../../config/logger';
import  { ScaleSetModule } from '../../../modules/pixelstreaming';
import { Request, Response } from 'express';
import { ProjectModule } from '../../../modules/projects';

export async function addScaleSet (req: Request, res: Response):Promise<void>{
  const project_id = req.body.project_id as string;
  const organization_id = req.headers.organization as string;
  const defaultScalesetObject = {
    'version': 0,
    'instancesPerNode': 1,
    'resolutionWidth': 1920,
    'resolutionHeight': 1080,
    'pixelstreamingApplicationName': '',
    'fps': 30,
    'unrealApplicationDownloadUri': req.body.unrealApplicationDownloadUri,
    'msImprovedWebserversDownloadUri': `https://afuekadmin.blob.core.windows.net/zips/
    msImprovedWebservers.zip?sv=2023-08-03&st=2023-10-10T06%3A21%3A05Z&se=2023-10-10T06%3A56%3A
    05Z&sr=b&sp=r&sig=wUKqVkc%2F7juktgEOW%2BuzNPY%2F7PrmPecU0bGMY%2BVelRc%3D`.replace(/\s+/g, ''),
    'msPrereqsDownloadUri': `https://afuekadmin.blob.core.windows.net/zips/msPrereqs.zip
    ?sv=2023-08-03&st=2023-10-10T06%3A21%3A05Z&se=2023-10-10T06%3A56%3A05Z&sr=b&sp=r&sig=N44DHdSGE
    US3dduw07L8OqioUB%2BVzESDHyd%2FpcEP5%2Fg%3D`.replace(/\s+/g, ''),
    'enableAutoScale': true,
    'instanceCountBuffer': 1,
    'percentBuffer': 100,
    'minMinutesBetweenScaledowns': 10,
    'scaleDownByAmount': 1,
    'minInstanceCount': 1,
    'maxInstanceCount': 2,
    'stunServerAddress': 'stun:stun.l.google.com:19302',
    'turnServerAddress': 'turn:************:19303',
    'turnUsername': 'propvr',
    'turnPassword': 'revsmart@123',
  };
  const projectModule = new ProjectModule(organization_id);
  const { resourceGroupName, vmScaleSetName } = await projectModule.getVMDetails(project_id) || {};
  if (!resourceGroupName || !vmScaleSetName) {
    res.status(500).send({message: 'Resource group name does not exist on project'});
    return;
  }
  const ScaleSetObj = new ScaleSetModule(resourceGroupName, vmScaleSetName);
  ScaleSetObj.addScaleSet(defaultScalesetObject, project_id, organization_id).then((scalesetData) => {
    res.status(200).send({message: 'Success', data: scalesetData});
  }).catch((err) => {
    logger.error('Error in addScaleSet', {message: err});
    res.status(500).send({message: err.toString()});
  });
}

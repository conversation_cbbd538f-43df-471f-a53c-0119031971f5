import { ExtendedRequest } from './../../../types/extras';
import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { EnvVar } from '../../../types/virtualTour';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';

export async function UpdateTourImage (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const organization_id = request.organization_id as string;
    const { project_id, tour_id, image_id } = request.body;
    const tourModule = new VirtualTourModule(organization_id, project_id);

    if (request.files) {
      // Convert to urls
      const urlObject = await UploadUnitplanFiles(
        request.files,
        `${tourModule.storagepath}/${tour_id}`,
      );

      const updateFields = {
        ...request.body,
        thumbnail: urlObject.thumbnail,
        url: urlObject.url,
        tile_rendering_status: 'processing',
        tile_rendering_failed_info: null,
      };

      const updatedTour = await tourModule.UpdateTourImage(tour_id, image_id, updateFields);

      if (!updatedTour) {
        throw new Error(`Failed to UpdateTourImage. Payload: ${JSON.stringify(updateFields)}, Response: ${JSON.stringify(updatedTour)}`);
      }

      const storagePath = (tourModule.storagepath +'/'+tour_id) as string;
      const imageId = image_id as string;

      const envVars : EnvVar[]  = [
        { name: 'project_id', value: project_id },
        { name: 'organization_id', value: organization_id },
        { name: 'image_id', value: imageId },
        { name: 'tour_id', value: tour_id },
        { name: 'storagepath', value: storagePath },
        { name: 'url', value: urlObject.url },
      ];

      const jobId = 'tile-generator';
      const runJob = new cloudRunJobModule;
      runJob.runJobExecution(jobId, envVars); // Run the job

      response.status(200).json({ status: 1, data: updatedTour });
    } else {
      const updateFields = { ...request.body };
      const tourData = await tourModule.UpdateTourImage(tour_id, image_id, updateFields);
      response.status(200).json({ status: 1, data: tourData });
    }
  } catch (error) {
    logger.error('Error Updating tour Image', { message: error });
    response.status(404).json({ status: 0, error: 'Error Updating tour Image' + error });
  }
}

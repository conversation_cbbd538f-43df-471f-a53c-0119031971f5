import dotenv from 'dotenv';
dotenv.config();

import mongoose from 'mongoose';
import { organizationSchema } from '../../schema/organizationsSchema';
import logger from '../../config/logger';
import { connectToDatabase } from '../../mongoose';
import { getAllExpiredTrash } from '../../controllers/trash/getAllExpiredTrash';

const OrganizationModel = mongoose.model('Organization', organizationSchema);

// Connect to MongoDB before starting the server
connectToDatabase(process.env.MONGO_CONNECTION_STRING as string)
  .then(() => {
    console.log('MongoDB successfully');
  })
  .catch((error) => {
    console.error('Server startup failed:', error);
  });

async function fetchUserOrganizations () {
  try {
    if (!OrganizationModel) {
      console.log('OrganizationModel is not defined. Check your imports.');
    }
    const organizationList = await OrganizationModel.find({});

    for (const org of organizationList) {
      await getAllExpiredTrash(org._id);
    }
    console.log('All organizations processed successfully.');
  } catch (error) {
    logger.error('Error calling OrganizationModel', { error });
    throw new Error('Error fetching organizations');
  }
}

// Call the function properly with async/await
(async () => {
  try {
    await fetchUserOrganizations();
    process.exit(0);

  } catch (error) {
    console.error('Error fetching user organizations:', error);
  }
})();

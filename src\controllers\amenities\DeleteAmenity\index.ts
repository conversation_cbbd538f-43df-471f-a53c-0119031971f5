import logger from '../../../config/logger';
import { AmenityModule } from '../../../modules/amenity';
import { Amenity } from '../../../types/amenity';
import { Request, Response } from 'express';
export async function DeleteAmenity (
  request: Request,
  response: Response,
): Promise<Amenity | void> {
  const project_id = request.body.project_id;
  const organization_id = request.body.organization;
  const amenity = new AmenityModule(project_id, organization_id);
  const amenity_id = request.body.amenity_id;
  await amenity
    .DeleteAmenity(amenity_id)
    .then((amenityData) => {
      response.status(201).json({ status: 1, data: amenityData });
    })
    .catch((error: Error) => {
      logger.error('Error while deleting the amenity:', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting the amenity'+ error });
    });
}

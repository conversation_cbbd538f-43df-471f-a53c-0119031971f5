import { Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';
import { BuildingType } from '../../../types/building';
import logger from '../../../config/logger';
import { ExtendedRequest } from '../../../types/extras';

interface UploadedFiles {
  thumbnail: Express.Multer.File;
}

const createBuildingValidate = async  (
  req: ExtendedRequest,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  const files = req.files as UploadedFiles | undefined;
  console.log('111111', req.files);

  if (!files || !files.thumbnail) {
    res
      .status(400)
      .json({ error: 'Tower Image File is required.' });
  } else {
    const requiredTextFields = [
      'project_id',
      'name',
      'type',
      'total_floors',
    ];

    const missingTextFields = requiredTextFields.filter(
      (field) => !(field in req.body),
    );

    if (missingTextFields.length > 0) {
      res.status(400).json({
        error: `Missing text fields: ${missingTextFields.join(', ')}`,
      });
    } else {
      await body(
        'type',
        'Invalid type value. Please ensure that you are using a valid type value',
      )
        .isIn(Object.values(BuildingType))
        .run(req);
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        console.log(errors);
        logger.error(errors);

        res
          .status(400)
          .json({ error: errors });
      } else {
        next();
      }
    }
  }
};

export default createBuildingValidate;

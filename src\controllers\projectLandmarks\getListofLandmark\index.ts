import { Request, Response } from 'express';
import { ProjectLandmarkModule } from '../../../modules/projectLandmark';
import logger from '../../../config/logger';

export async function getListofLandmark (
  request: Request,
  response: Response,
): Promise<void> {
  const {  project_id } = request.params;
  const organization_id = request.headers.organization as string;
  const projectLandmark = new ProjectLandmarkModule(project_id, organization_id);
  const projectLandmarkData = await projectLandmark.getListofLandmark();
  if (projectLandmarkData) {
    response.status(200).json({ status: 1, data: projectLandmarkData});
  } else {
    logger.error('landmark not found:');
    response.status(404).json({ status: 0, error: 'landmark not found' });
  }
}

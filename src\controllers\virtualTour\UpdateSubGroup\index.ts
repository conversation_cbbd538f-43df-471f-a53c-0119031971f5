import { Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import mongoose from 'mongoose';
import { SubGroup } from '../../../types/virtualTour';

export async function UpdateSubGroup (req: ExtendedRequest, res: Response): Promise<void> {
  try {
    const organization_id = req.organization_id as string;
    const project_id = req.body.project_id as string;
    const tour_id = req.body.tour_id as string;
    const group_id = req.body.group_id as string;
    const subgroup_id = req.body.subgroup_id as string;

    const groupData: SubGroup = {
      _id: new mongoose.Types.ObjectId(subgroup_id),
      ...(req.body.name && {  name: req.body.name }),
      ...(req.body.icon && { icon: req.body.icon }),
      ...(req.body.order !== undefined && { order: req.body.order }),
    };

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updatedTour = await tourModule.UpdateSubGroup(tour_id, group_id, subgroup_id, groupData);

    res.status(201).json({
      status: 1,
      message: 'SubGroup updated successfully',
      data: updatedTour,
    });
  } catch (error) {
    logger.error('Error in UpdateSubGroup', { message: error });
    res.status(500).json({
      status: 0,
      error: `Error updating Subgroup: ${error}`,
    });
  }
}

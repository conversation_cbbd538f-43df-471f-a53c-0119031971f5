import { LeadsModule } from '../../../modules/leads/index';
import { MailerModule } from '../../../modules/mailer';
import { ProjectModule } from '../../../modules/projects';
import { UserModule } from '../../../modules/user';
import { Request, Response } from 'express';
import { mailUserData } from '../../../types/mailer';
import { SessionModule } from '../../../modules/sessions';
export default async function sendInviteGuest (
  request: Request,
  response: Response,
):Promise<void> {
  try {
    const mailer = new MailerModule();
    const session_id = request.body.session_id;
    const leadData = request.body.leadData;
    if (!leadData) {
      response.status(404).send({ status: 0, message: 'Lead Data not found' });
      return;
    }
    const sessionModule = new SessionModule();
    const sessionData = await sessionModule.getSessionById(session_id);
    if (!sessionData) {
      response.status(404).send({ status: 0, message: 'Session Data not found' });
      return;
    }
    const leadModule = new LeadsModule(sessionData.organization_id);
    const projectModule = new ProjectModule(sessionData.organization_id);
    const project = await projectModule.getProjectById(sessionData.project_id as string);
    if (!project) {
      response.status(404).send({ status: 0, message: 'Project Data not found' });
      return;
    }

    const userModule = new UserModule();
    const username = await userModule.getUsername(sessionData.user_id);
    const userEmail = await userModule.getUserEmail(sessionData.user_id);
    if (!username || !userEmail) {
      response.status(404).send({ status: 0, message: 'User Data not found' });
      return;
    }
    const userData: mailUserData = {
      username: username,
      useremail: userEmail,
    };
    const thread_id = await mailer.SendGuestInviteMail(sessionData, leadData, project, userData);
    if (!leadData.thread_id && thread_id) {
      await leadModule.UpdateThreadId(leadData._id, thread_id);
    }
    response.status(200).send({ status: 1, data: 'Invite Sent Successfully' });
  } catch (error) {
    response.status(500).send({ status: 0, message: error });
  }

}

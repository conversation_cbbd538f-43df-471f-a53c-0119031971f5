import mongoose from 'mongoose';
import { Translations } from '../../types/translation';
// Define an interface for translation fields

// Define the Mongoose schema
export const translationSchema = new mongoose.Schema<Translations>({
  _id: { type: String, required: true },
  en: { type: String, required: true },
  ab: { type: String }, // Abkhaz
  ace: { type: String }, // Acehnese
  ach: { type: String }, // Acholi
  af: { type: String }, // Afrikaans
  sq: { type: String }, // Albanian
  alz: { type: String }, // Alur
  am: { type: String }, // Amharic
  ar: { type: String }, // Arabic
  hy: { type: String }, // Armenian
  as: { type: String }, // Assamese
  awa: { type: String }, // Awadhi
  ay: { type: String }, // Aymara
  az: { type: String }, // Azerbaijani
  ban: { type: String }, // Balinese
  bm: { type: String }, // Bambara
  ba: { type: String }, // Bashkir
  eu: { type: String }, // Basque
  btx: { type: String }, // Batak Karo
  bts: { type: String }, // Batak Simalung<PERSON>
  bbc: { type: String }, // Batak Toba
  be: { type: String }, // Belarusian
  bem: { type: String }, // Bemba
  bn: { type: String }, // Bengali
  bew: { type: String }, // Betawi
  bho: { type: String }, // Bhojpuri
  bik: { type: String }, // Bikol
  bs: { type: String }, // Bosnian
  br: { type: String }, // Breton
  bg: { type: String }, // Bulgarian
  bua: { type: String }, // Buryat
  yue: { type: String }, // Cantonese
  ca: { type: String }, // Catalan
  ceb: { type: String }, // Cebuano
  ny: { type: String }, // Chichewa (Nyanja)
  zh_CN: { type: String }, // Chinese (Simplified)
  zh_TW: { type: String }, // Chinese (Traditional)
  cv: { type: String }, // Chuvash
  co: { type: String }, // Corsican
  crh: { type: String }, // Crimean Tatar
  hr: { type: String }, // Croatian
  cs: { type: String }, // Czech
  da: { type: String }, // Danish
  din: { type: String }, // Dinka
  dv: { type: String }, // Divehi
  doi: { type: String }, // Dogri
  dov: { type: String }, // Dombe
  nl: { type: String }, // Dutch
  dz: { type: String }, // Dzongkha
  eo: { type: String }, // Esperanto
  et: { type: String }, // Estonian
  ee: { type: String }, // Ewe
  fj: { type: String }, // Fijian
  fil: { type: String }, // Filipino (Tagalog)
  fi: { type: String }, // Finnish
  fr: { type: String }, // French
  fr_FR: { type: String }, // French (French)
  fr_CA: { type: String }, // French (Canadian)
  fy: { type: String }, // Frisian
  ff: { type: String }, // Fulfulde
  gaa: { type: String }, // Ga
  gl: { type: String }, // Galician
  lg: { type: String }, // Ganda (Luganda)
  ka: { type: String }, // Georgian
  de: { type: String }, // German
  el: { type: String }, // Greek
  gn: { type: String }, // Guarani
  gu: { type: String }, // Gujarati
  ht: { type: String }, // Haitian Creole
  cnh: { type: String }, // Hakha Chin
  ha: { type: String }, // Hausa
  haw: { type: String }, // Hawaiian
  he: { type: String }, // Hebrew
  hil: { type: String }, // Hiligaynon
  hi: { type: String }, // Hindi
  hmn: { type: String }, // Hmong
  hu: { type: String }, // Hungarian
  hrx: { type: String }, // Hunsrik
  is: { type: String }, // Icelandic
  ig: { type: String }, // Igbo
  ilo: { type: String }, // Iloko
  id: { type: String }, // Indonesian
  ga: { type: String }, // Irish
  it: { type: String }, // Italian
  ja: { type: String }, // Japanese
  jw: { type: String }, // Javanese
  kn: { type: String }, // Kannada
  pam: { type: String }, // Kapampangan
  kk: { type: String }, // Kazakh
  km: { type: String }, // Khmer
  cgg: { type: String }, // Kiga
  rw: { type: String }, // Kinyarwanda
  ktu: { type: String }, // Kituba
  gom: { type: String }, // Konkani
  ko: { type: String }, // Korean
  kri: { type: String }, // Krio
  ku: { type: String }, // Kurdish (Kurmanji)
  ckb: { type: String }, // Kurdish (Sorani)
  ky: { type: String }, // Kyrgyz
  lo: { type: String }, // Lao
  ltg: { type: String }, // Latgalian
  la: { type: String }, // Latin
  lv: { type: String }, // Latvian
  lij: { type: String }, // Ligurian
  li: { type: String }, // Limburgan
  ln: { type: String }, // Lingala
  lt: { type: String }, // Lithuanian
  lmo: { type: String }, // Lombard
  luo: { type: String }, // Luo
  lb: { type: String }, // Luxembourgish
  mk: { type: String }, // Macedonian
  mai: { type: String }, // Maithili
  mak: { type: String }, // Makassar
  mg: { type: String }, // Malagasy
  ms: { type: String }, // Malay
  ms_Arab: { type: String }, // Malay (Jawi)
  ml: { type: String }, // Malayalam
  mt: { type: String }, // Maltese
  mi: { type: String }, // Maori
  mr: { type: String }, // Marathi
  chm: { type: String }, // Meadow Mari
  mni_Mtei: { type: String }, // Meiteilon (Manipuri)
  min: { type: String }, // Minang
  lus: { type: String }, // Mizo
  mn: { type: String }, // Mongolian
  my: { type: String }, // Myanmar (Burmese)
  nr: { type: String }, // Ndebele (South)
  new: { type: String }, // Nepalbhasa (Newari)
  ne: { type: String }, // Nepali
  nso: { type: String }, // Northern Sotho (Sepedi)
  no: { type: String }, // Norwegian
  nus: { type: String }, // Nuer
  oc: { type: String }, // Occitan
  or: { type: String }, // Odia (Oriya)
  om: { type: String }, // Oromo
  pag: { type: String }, // Pangasinan
  pap: { type: String }, // Papiamento
  ps: { type: String }, // Pashto
  fa: { type: String }, // Persian
  pl: { type: String }, // Polish
  pt: { type: String }, // Portuguese
  pt_PT: { type: String }, // Portuguese (Portugal)
  pt_BR: { type: String }, // Portuguese (Brazil)
  pa: { type: String }, // Punjabi
  pa_Arab: { type: String }, // Punjabi (Shahmukhi)
  qu: { type: String }, // Quechua
  rom: { type: String }, // Romani
  ro: { type: String }, // Romanian
  rn: { type: String }, // Rundi
  ru: { type: String }, // Russian
  sm: { type: String }, // Samoan
  sg: { type: String }, // Sango
  sa: { type: String }, // Sanskrit
  gd: { type: String }, // Scots Gaelic
  sr: { type: String }, // Serbian
  st: { type: String }, // Sesotho
  crs: { type: String }, // Seychellois Creole
  shn: { type: String }, // Shan
  sn: { type: String }, // Shona
  scn: { type: String }, // Sicilian
  szl: { type: String }, // Silesian
  sd: { type: String }, // Sindhi
  si: { type: String }, // Sinhala (Sinhalese)
  sk: { type: String }, // Slovak
  sl: { type: String }, // Slovenian
  so: { type: String }, // Somali
  es: { type: String }, // Spanish
  su: { type: String }, // Sundanese
  sw: { type: String }, // Swahili
  ss: { type: String }, // Swati
  sv: { type: String }, // Swedish
  tg: { type: String }, // Tajik
  ta: { type: String }, // Tamil
  tt: { type: String }, // Tatar
  te: { type: String }, // Telugu
  tet: { type: String }, // Tetum
  th: { type: String }, // Thai
  ti: { type: String }, // Tigrinya
  ts: { type: String }, // Tsonga
  tn: { type: String }, // Tswana
  tr: { type: String }, // Turkish
  tk: { type: String }, // Turkmen
  ak: { type: String }, // Twi (Akan)
  uk: { type: String }, // Ukrainian
  ur: { type: String }, // Urdu
  ug: { type: String }, // Uyghur
  uz: { type: String }, // Uzbek
  vi: { type: String }, // Vietnamese
  cy: { type: String }, // Welsh
  xh: { type: String }, // Xhosa
  yi: { type: String }, // Yiddish
  yo: { type: String }, // Yoruba
  yua: { type: String }, // Yucatec Maya
  zu: { type: String }, // Zulu
});

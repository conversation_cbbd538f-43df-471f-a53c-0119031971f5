import { ExtendedRequest } from '../../../types/extras';
import { ProjectSVGModule } from '../../../modules/projectSVG';

import { Response } from 'express';
import logger from '../../../config/logger';
export async function deleteLayers (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const layer_id = request.body.layer_id;
  const svg_id = request.body.svg_id;
  const project_id = request.body.project_id;
  const svg = new ProjectSVGModule(project_id, organization_id);

  await svg
    .deleteLayers(svg_id, layer_id, organization_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Layer Deleted' });
    })
    .catch((error: Error) => {
      logger.error('Error in deleteLayers', {message: error});
      response
        .status(400)
        .json({ status: 0, error: 'Error in deleteLayers'+ error });
    });
}

import { leadsSchema } from '../../schema/leadsSchema';
import { Leads, updateLeadDurationObj, updateLeadInput, createLeadInput,
  leadAnalyticsQuery, LeadSource,
  UpdateLeadInput} from '../../types/leads';
import mongoose, { PipelineStage, Types } from 'mongoose';
import { sessionSchema } from '../../schema/sessionSchema';
import logger from '../../config/logger';
import { Models } from '../../types/extras';
export class LeadsModule {
  private model: mongoose.Model<Leads>;

  constructor (organization_id: string) {
    console.log(organization_id);
    this.model = mongoose.model<Leads>(
      `${organization_id}${Models._LEADS}`,
      leadsSchema,
    );
  }
  public async CreateLead (createLeadData: createLeadInput):Promise<Leads | void> {
    logger.info('CreateLead Called', {createLeadData: createLeadData});
    try {
      if (!createLeadData.lead_source) {
        createLeadData.lead_source=LeadSource.SALES_SESSION;
      }

      const newLead = {
        _id: new Types.ObjectId(),
        session_id: createLeadData.session_id,
        organization_id: createLeadData.organization_id,
        name: createLeadData.name,
        phone_number: createLeadData.phone_number,
        email: createLeadData.email,
        duration_minutes: createLeadData.duration_minutes,
        joining_time: createLeadData.joining_time,
        interested_in: {
          type: createLeadData.type,
          project_id: createLeadData.project_id || undefined,
          unit_id: createLeadData.type !== 'project' ? createLeadData.unit_id : undefined,
        },
        lead_source: createLeadData.lead_source,
        lead_product_interest: createLeadData.lead_product_interest,
        lead_industry_type: createLeadData.lead_industry_type,
        start_time: createLeadData.start_time,
        user_id: createLeadData.user_id,
        lead_status: createLeadData.lead_status,
        next_interaction_time: new Date().toISOString(),
        last_interaction_time: new Date().toISOString(),
        end_time: createLeadData.end_time,
        lead_creation_time: new Date().toISOString(),
      };

      const leads = new this.model(newLead);
      const newlead = await leads.save();
      logger.info('CreateLead Successfully', {newlead: newlead});
      return newlead as Leads;
    } catch (error) {
      logger.error('Unable to create lead for this session', {message: error});
      throw new Error('Unable to create lead for this session'+error);
    }

  }

  public async UpdateLead (
    lead_id: string,
    updatedData: UpdateLeadInput,
  ): Promise<Leads | null> {
    return new Promise<Leads | null>((resolve, reject) => {
      this.model
        .findOneAndUpdate({ _id: lead_id },
          {
            $set: updatedData,
          }, { new: true })
        .then((updatedLead) => {
          if (!updatedLead) {
            resolve(updatedLead);
          } else {
            resolve(updatedLead);
          }
        })
        .catch((err) => {
          console.log(err);
          reject(err);
        });
    });
  }

  public async GetLeads (
    organization_id: string,
    query: leadAnalyticsQuery,
  ): Promise<object | void> {
    logger.info('GetLeads Called', { organization_id, query });

    try {
      const mongoQuery: any = {};

      const projectIds = query.project_id;
      const statuses = query.status ;
      const userIds = query.user_id;

      logger.info('Received Query Arrays', { projectIds, statuses, userIds });
      if (projectIds) {
        mongoQuery['interested_in.project_id'] = { $in: projectIds };
      }

      if (statuses) {
        mongoQuery.lead_status = { $in: statuses };
      }

      if (userIds) {
        mongoQuery.user_id = { $in: userIds };
      }

      const pipeline: PipelineStage[] = [];

      pipeline.push(
        ...(Object.keys(mongoQuery).length ? [{ $match: mongoQuery }] : []),
        { $sort: { lead_creation_time: -1 } },
        {
          $group: {
            _id: null,
            leads: { $push: { k: '$_id', v: '$$ROOT' } },
          },
        },
        { $replaceRoot: { newRoot: { $arrayToObject: '$leads' } } },
      );

      const result = await this.model.aggregate(pipeline);

      if (result.length > 0) {
        logger.info('GetLeads Successfully', { result: result[0] });
        return result[0];
      }

      logger.warn('No leads found');
      return {};
    } catch (error) {
      logger.error('Unable to fetch leads for this organization', { message: error });
      throw new Error(`Unable to fetch leads for this organization - ${organization_id}`);
    }
  }

  public async DeleteLead
  (lead_id: string,
  ): Promise<Leads | null> {
    return new Promise((resolve, reject) => {
      this.model.findOneAndDelete({ _id: lead_id }).then((deletedLead: unknown) => {
        resolve(deletedLead as Leads);
      }).catch((err) => {
        reject(err);
      });
    });
  }

  public async UpdateDuration (updateLeadData:updateLeadDurationObj):Promise<Leads|void> {
    logger.info('UpdateDuration Called', {updateLeadData: updateLeadData});
    try {
      const existingLead = await this.model.findById(updateLeadData.lead_id);

      if (!existingLead) {
        logger.error('Unable to find existing lead');
        throw new Error('Unable to find existing lead');
      }
      const newLastInteractionTime = new Date().toISOString();
      const newDuration = existingLead.duration_minutes + updateLeadData.duration_minutes;
      const lead = await this.model.findOneAndUpdate(
        { _id: updateLeadData.lead_id, session_id: updateLeadData.session_id },
        {
          $set: {
            last_interaction_time: newLastInteractionTime,
            duration_minutes: newDuration,
          },
        },
        { new: true }, // Return the updated document
      );

      if (lead) {
        logger.info('UpdateDuration Successfully', {lead: lead});
        return lead;
      }
      logger.error('Lead with this session ID does not exist');
      throw new Error('Lead with this session ID does not exist');
      logger.error('Error in updating lead');
      throw new Error('Error in updating lead');

    } catch (error){
      logger.error('Unexpected error occured while updating lead duration', {message: error});
      throw new Error('Unexpected error occured while updating lead duration');
    }

  }
  // Join Session for client
  public async JoinSession ( joinSessionObj: updateLeadInput ): Promise<Leads | void> {
    logger.info('JoinSession Called', {joinSessionObj: joinSessionObj});
    try {
      const SessionModel = mongoose.model('Session', sessionSchema);
      const session = await SessionModel.findById(joinSessionObj.session_id);
      if (!session) {
        logger.error('Session not found');
        throw new Error('Session not found');
      }
      const lead = await this.model.findOneAndUpdate(
        { _id: joinSessionObj.lead_id },
        { $set: {
          joining_time: new Date().toISOString(),
          start: session.start,
        },
        },
      );
      logger.info('JoinSession Successfully', {lead: lead});
      return lead as Leads;
    } catch (error) {
      logger.error('An unexpected error occurred while joining the session', {message: error});
      throw new Error('An unexpected error occurred while joining the session'+error);
    }
  }
  // Leave Session API for client
  public async LeaveSession (joinSessionObj: updateLeadInput):Promise<Leads|void> {
    logger.info('LeaveSession Called', {joinSessionObj: joinSessionObj});
    try {
      const SessionModel = mongoose.model('Session', sessionSchema);
      const session = await SessionModel.findById(joinSessionObj.session_id);
      if (!session) {
        logger.error('Session not found');
        throw new Error('Session not found');
      }
      const lead = await this.model.findOneAndUpdate(
        { _id: joinSessionObj.lead_id },
        { $set: {
          last_interaction_time: new Date().toISOString(),
          end_time: new Date().toISOString(),
        },
        },
      );
      logger.info('LeaveSession Successfully', {lead: lead});
      return lead as Leads;
    } catch (error) {
      logger.error('An unexpected error occurred while leaving the session', {message: error});
      throw new Error('An unexpected error occurred while leaving the session');
    }
  }
  public async getAnalytics (query: leadAnalyticsQuery, organizationId: string): Promise<object | null> {
    logger.info('getAnalytics Called', {query: query, organizationId: organizationId});
    let pipeline: PipelineStage[] = [];
    const mongoQuery : leadAnalyticsQuery = {
      ...(query.project_id && { 'interested_in.project_id': query.project_id }),
      ...(query.user_id && { user_id: query.user_id }),
      ...(query.status && { status: query.status }),
      ...(query.lead_source && { lead_source: query.lead_source }),
      ...(query.start_time && {
        start: { $gte: new Date(query.start_time) },
      }),
    };
    if (Object.keys(query).length === 0) {
      // Execute MongoDB query for all leads (without filters)
      pipeline = [
        {
          $match: {
            organization_id: organizationId,
          },
          $group: {
            _id: null,
            total_duration_all: { $sum: '$duration_minutes' },
            num_leads_all: { $sum: 1 },
            leads: { $push: '$$ROOT' },
          },
        },
        {
          $project: {
            _id: 0,
            analytics: {
              total_duration_all: '$total_duration_all',
              num_leads_all: '$num_leads_all',
            },
            leads: '$leads',
          },
        },
      ];
    } else {
      pipeline = [
        {
          $match: {
            $and: [
              mongoQuery,
            ].filter(Boolean),
          },
        },
        {
          $group: {
            _id: null,
            total_duration: { $sum: '$duration_minutes' },
            num_leads: { $sum: 1 },
            leads: { $push: '$$ROOT' },
          },
        },
        {
          $project: {
            _id: 0,
            analytics: {
              total_duration: '$total_duration',
              num_leads: '$num_leads',
            },
            leads: '$leads',
          },
        },
      ];
    }

    const result = await this.model.aggregate(pipeline);

    if (result.length > 0) {
      logger.info('getAnalytics Successfully', {result: result[0]});
      return result[0];
    }
    logger.error('An unexpected error in getAnalytics');
    return null;
  }
  public async GetLeadById (leadId: string): Promise<Leads |null>{
    logger.info('GetLeadById Called', {leadId: leadId});
    try {
      const lead = await this.model.findOne({ _id: leadId });
      logger.info('GetLeadById Successfully', {lead: lead});
      return lead as Leads;
    } catch (error) {
      logger.error('An unexpected error in getAnalytics', {message: error});
      throw new Error('Error in GetLeadById');
    }
  }
  public async GetLeadsBySessionId (session_id: string): Promise<Leads[] |null>{
    logger.info('GetLeadsBySessionId Called', {session_id: session_id});
    try {
      const leadsArray = await this.model.find({ session_id: session_id });
      logger.info('GetLeadsBySessionId Successfully', {leadsArray: leadsArray});
      return leadsArray as Leads[];
    } catch (error) {
      logger.error('An unexpected error in GetLeadsBySessionId', {message: error});
      throw new Error('An unexpected error occurred while leaving the session');
    }
  }
  public async UpdateThreadId (lead_id: string, threadId:string):Promise<Leads|void> {
    const existingSession = await this.model.findOne({ _id: lead_id }); // Fetch existing session doc
    if (existingSession) {
      const session = await this.model.findOneAndUpdate(
        { _id: lead_id },
        {
          $set: {
            thread_id: threadId,
          },
        },
        { new: true }, // Return the updated document
      );
      if (session) {
        return session;
      }
      throw new Error('Error in updating threadId on lead');

    } else {
      throw new Error('Unable to find existing lead');
    }
  }
}

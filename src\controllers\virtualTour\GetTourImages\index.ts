import { VirtualTourModule } from '../../../modules/virtualTour';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function GetTourImages (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id;

  if (!organization_id) {
    logger.error('Organization ID not found in request', { headers: request.headers });
    response.status(500).json({ status: 0, error: 'Organization ID not found' });
    return;
  }

  const { project_id, tour_id } = request.params;

  try {
    const tourModule = new VirtualTourModule(organization_id, project_id);
    const imagesData = await tourModule.GetTourImages(tour_id);

    if (!imagesData) {
      logger.warn('Tour Images not found', { project_id, tour_id });
      response.status(404).json({ status: 0, error: 'Tour Images not found' });
      return;
    }

    response.status(200).json({ status: 1, data: imagesData });
  } catch (error) {
    logger.error('Error fetching Tour Images', { error });
    response.status(500).json({ status: 0, error: `Error fetching tour images: ${error}` });
  }
}

import logger from '../../../config/logger';
import { SidebarModule } from '../../../modules/sidebar';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
export async function updateBulkOptions (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const sidebarMod = new SidebarModule(project_id);
  sidebarMod.updateBulkOptions(request.body)
    .then((res) => {
      response.status(201).json({ status: 1, message: res });
    })
    .catch((error: Error) => {
      logger.error('Error in updateBulkOptions', {message: error});
      response
        .status(500)
        .json({ status: 0, error: error});
    });
}

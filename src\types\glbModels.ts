import { Types } from 'mongoose';

export enum GlbType {
  DEFAULT = 'default',
  LUMA = 'luma',
}

export type meshes = {
  id: string;
  textures?: object;
};
export type glbmodel = {
  _id: Types.ObjectId;
  name: string;
  description: string;
  thumbnail: string;
  url: string;
  meshes: object;
  type: GlbType.DEFAULT;
  status?: string;
  luma_id?: string;
  progress?: number;
};
export type createModel = {
  name: string;
  description: string;
  meshes: object;
  thumbnail: string;
  url: string;
};
export type createTexture = {
  texture_thumbnail: string;
  actual_texture_thumbnail: string;
  name: string;
  mesh_id: string;
  model_id: string;
  id: string;
};
export type updateModel = {
  model_id: string;
  name?: string;
  description?: string;
  thumbnail?: string;
  url?: string;
};
export type updateTexture = {
  model_id: string;
  mesh_id?: string;
  name?: string;
  texture_id?: string;
  actual_texture_thumbnail?: string;
  texture_thumbnail?: string;
};

export type updateLumaModel = {
  model_id: string;
  progress: number;
  url: string;
  status?: string;
  thumbnail?: string;
};

export type updateDownloadUrl = {
  model_id: string;
  download_url: string;
  status:string
};

export type blenderResponse = {
  status:number,
  message:string,
  output:string
}

import { Request, Response } from 'express';
import { assets } from '../../../types/asset';
import { AssetsModule } from '../../../modules/asset';
import logger from '../../../config/logger';

export async function DeleteAsset (request: Request, response: Response): Promise<assets | void> {
  const {project_id} = request.body;
  const organization_id = request.headers.organization as string;
  const {asset_id}  = request.body;
  const asset = new AssetsModule(project_id, organization_id);

  asset.DeleteAsset(asset_id).then((res) => {
    response.send({status: 1, data: res});
  })
    .catch((err) => {
      logger.error('Error:', {message: err});
      response.send({status: 0, message: err});
    });
  return;
}

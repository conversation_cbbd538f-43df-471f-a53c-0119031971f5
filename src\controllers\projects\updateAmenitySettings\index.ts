import { Request, Response } from 'express';
import { ProjectModule } from '../../../modules/projects';
import { galleryPayload } from '../../../types/projects';
import mongoose from 'mongoose';
import logger from '../../../config/logger';
export async function updateAmenitySettings (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  //   Pass organization_id in this ProjectModule
  const project = new ProjectModule(organization_id);
  const payload = request.body;
  payload.query.forEach((item:galleryPayload, index:number) => {
    if (!item.id){
      const id = new mongoose.Types.ObjectId();
      payload.query[index].id = id;
    }
  });
  project.updateAmenitySettings(payload).then((res) => {
    response.status(200).json({ status: 1, message: res });
  })
    .catch((error: Error) => {
      logger.error('Error in updateAmenitySettings', { message: error });
      response
        .status(500)
        .json({ status: 0, error: error});
    });
}

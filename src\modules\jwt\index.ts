import mongoose from 'mongoose';
import { JWTRecordSchema } from '../../schema/jwtSchema';
import { ExtedendOrganizationTokens, JWTRecord } from '../../types/jwt';
import jwt from 'jsonwebtoken';
import logger from '../../config/logger';
import { JWTAccessRecordSchema } from '../../schema/jwtAccessRecordsSchema';
import { Models } from '../../types/extras';

export class JWTModule{
  private model:mongoose.Model<JWTRecord>;
  private secretModel:mongoose.Model<JWTRecord>;
  constructor (){
    this.model = mongoose.model<JWTRecord>(`${Models.JWTRECORDS}`, JWTAccessRecordSchema);
    this.secretModel = mongoose.model<JWTRecord>('secretRecords', JWTRecordSchema);
  }

  setExpiryDate (daysToAdd : string):Date {
    const days = parseInt(daysToAdd.replace('d', ''), 10);
    const now = new Date();
    const addExpiry = new Date();
    addExpiry.setTime(now.getTime() +  (days * 24 * 60 * 60 * 1000)); // GetTime() +  days * 24 * 60 * 60 * 1000);
    return addExpiry;
  }

  public async CreateJWT (token:string, organization_id:string, userRole:string):Promise<boolean|void>{
    logger.info('CreateJWT Called', {token: token, organization_id: organization_id, userRole: userRole});
    console.log('1111', organization_id);

    return new Promise((resolve, reject) => {
      const addExpiry = this.setExpiryDate(process.env.JWT_TOKEN_EXP as string);
      const jwtRecord: JWTRecord = {
        _id: new mongoose.Types.ObjectId(),
        key: token,
        organization: organization_id,
        role: userRole,
        created_at: new Date(),
        expiry: addExpiry,
      };
      const newJWTRecord = new this.model(jwtRecord);
      newJWTRecord
        .save()
        .then((data) => {
          logger.info('CreateJWT Successfully', {data: data});
          resolve();
        })
        .catch((err) => {
          logger.error('Error in CreateJWT', {message: err});
          reject(err);
        });
    });
  }

  public async GetJWTRecord (token:string):Promise<JWTRecord|null>{
    logger.info('GetJWTRecord Called', {token: token});
    const jwtRecord = await this.model.findOne().and([{key: token}, {role: 'admin'}]);
    if (jwtRecord){
      logger.info('GetJWTRecord Successfully', {jwtRecord: jwtRecord});
      return jwtRecord as JWTRecord;
    }
    logger.error('Error in GetJWTRecord');
    return null;
  }
  public async updateJWT (token: string): Promise<JWTRecord | null> {
    logger.info('updateJWT Called', { token: token });

    const jwtRecord = await this.model.findOne().and([{ key: token }, { role: 'admin' }]);
    if (jwtRecord) {
      if (
        jwtRecord.organization in ExtedendOrganizationTokens
      ) {
        try {
          const updatedExpiryJWT = await this.updateJWTYears(jwtRecord.key);
          logger.info('updateJWT Successfull', { updatedExpiryJWT: updatedExpiryJWT });
          return updatedExpiryJWT;
        } catch (err) {
          logger.error('Error in updateJWTYears', { err: err });
          return null;
        }
      } else {
        // Const secretKey = process.env.JWT_TOKEN as string;
        // Const expTime = process.env.JWT_TOKEN_EXP;
        // Const key = jwt.sign({} as object, secretKey, { expiresIn: expTime });

        const secretKey = process.env.JWT_TOKEN as string;

        const expTime = process.env.JWT_TOKEN_EXP as any;

        // Fix 1: Properly type the payload and options
        const payload = {} as jwt.JwtPayload;
        const options: jwt.SignOptions = {
          expiresIn: expTime,
        };

        const key = jwt.sign(payload, secretKey, options);

        const expiryDate = this.setExpiryDate(expTime as string);
        const updatedJWT = await this.model.findOneAndUpdate(
          { key: token },
          {
            $set: {
              key: key,
              created_at: new Date(),
              expiry: expiryDate,
            },
          },
          { new: true },
        );

        if (updatedJWT) {
          logger.info('updateJWT Successfully', { updatedJWT: updatedJWT });
          return updatedJWT as JWTRecord;
        }
      }
    } else {
      logger.error('No Records Found For Given Token');
      return null;
    }

    return null;
  }

  async updateJWTYears (token: string): Promise<JWTRecord | null> {
    logger.info('updateJWTYears Called', { token: token });

    // Const secretKey = process.env.JWT_TOKEN as string;
    // Const expTime = process.env.JWT_TOKEN_EXP_YRS;
    // Const key = jwt.sign({} as object, secretKey, { expiresIn: expTime });

    const secretKey = process.env.JWT_TOKEN as string;

    const expTime = process.env.JWT_TOKEN_EXP_YRS as any;

    // Fix 1: Properly type the payload and options
    const payload = {} as jwt.JwtPayload;
    const options: jwt.SignOptions = {
      expiresIn: expTime,
    };

    const key = jwt.sign(payload, secretKey, options);

    const expiryDate = this.setExpiryDate(expTime as string);
    const updatedExpiryJWT = await this.model.findOneAndUpdate(
      { key: token },
      {
        $set: {
          key: key,
          created_at: new Date(),
          expiry: expiryDate,
        },
      },
      { new: true },
    );

    updatedExpiryJWT ?
      logger.info('updateJWTYears Successfully', { updatedExpiryJWT: updatedExpiryJWT }) :
      logger.info('updateJWTYears Failed');
    return updatedExpiryJWT ? updatedExpiryJWT: null;
  }

  public async CreateAccessToken (refToken: string, organization_id: string):Promise<JWTRecord|object|string|Error> {
    try {
      logger.info('CreateAccessToken Called', { refToken: refToken, organization_id: organization_id });
      const verifyToken = await this.secretModel.findOne({
        key: refToken,
        role: 'admin',
      });

      if (!verifyToken) {
        logger.error('No Org Found on Given Token');
        throw new Error('No Org Found on Given Token');
      }
      if (verifyToken.organization !== organization_id){
        logger.error('Invalid organizationID Given');
        throw new Error('Invalid organizationID Given');
      }
      const GeneratedRefreshtoken = jwt.sign({} as object, verifyToken.key);
      const expiryDate = this.setExpiryDate(process.env.JWT_TOKEN_EXP as string);
      const jwtRecord: JWTRecord = {
        _id: new mongoose.Types.ObjectId(),
        key: GeneratedRefreshtoken,
        organization: organization_id,
        role: verifyToken.role,
        created_at: new Date(),
        expiry: expiryDate,
      };

      const newJWTRecord = new this.model(jwtRecord);
      const savedRecord = await newJWTRecord.save();

      logger.info('getAccessToken Successfull', { data: savedRecord });
      return savedRecord;
    } catch (err) {
      logger.error('Error in getAccessToken', { error: err instanceof Error ? err.message :''});
      throw new Error(`Error in CreateAccessToken: ${err instanceof Error ? err.message : ''}`);
    }
  }

  public async getRefreshTokens (organization_id: string):Promise<JWTRecord|object|string>{
    try {
      logger.info('getRefreshTokens Called', { organization_id: organization_id });

      const refrehTokens = await this.secretModel.find({
        organization: organization_id,
        role: 'admin',
      });

      console.log('result', refrehTokens);

      if (refrehTokens.length === 0){
        logger.error('No RefreshTokens for Given Org');
        throw new Error('No RefreshTokens for Given Org');
      }
      return refrehTokens;

    } catch (err){
      logger.error('Error in getRefreshTokens', {error: err instanceof Error ? err.message :''});
      throw new Error(`Error in getRefreshTokens:${err instanceof Error ? err.message as string : ''}`);
    }
  }

  async generateRandomKey ():Promise<string>{

    const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
    const generateSegment = (length:number) => {
      let segment = '';
      for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        segment += characters[randomIndex];
      }
      return segment;
    };
    const key = [
      generateSegment(8),
      generateSegment(4),
      generateSegment(4),
      generateSegment(4),
      generateSegment(12),
    ].join('-');

    return key;

  }

  public async createRefreshToken (organization_id:string):Promise<JWTRecord|string>{
    try {
      logger.info('createRefreshToken Called', { organization_id: organization_id });

      const randomKey = await this.generateRandomKey() as unknown as string;

      console.log('1111', randomKey);

      const jwtRecord: JWTRecord = {
        _id: new mongoose.Types.ObjectId(),
        key: randomKey,
        organization: organization_id,
        role: 'admin',
        created_at: new Date(),
      };

      const newJWTRecord = new this.secretModel(jwtRecord);
      const savedRecord = await newJWTRecord.save();

      logger.info('createRefreshToken Successfull', { data: savedRecord });
      return savedRecord;
    } catch (err){
      logger.info('Error in createRefreshToken', { error: err });
      throw new Error(`Error in createRefreshToken:${err instanceof Error ? err.message as string : ''}`);
    }
  }
}

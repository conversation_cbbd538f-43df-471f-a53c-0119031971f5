import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const deleteHotspotValidator = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('project_id', 'Project ID is required').notEmpty(),
  body('unitplan_id', 'Unitplan ID is required').notEmpty(),
  body('hotspot_id', 'Hotspot ID is required').notEmpty(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default deleteHotspotValidator;

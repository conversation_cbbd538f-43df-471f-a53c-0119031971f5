import { ExtendedRequest } from '../../../types/extras';
import { ProjectSVGModule } from '../../../modules/projectSVG';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreSVG (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.params;
  const svg = new ProjectSVGModule(project_id, organization_id);
  const trash_id = request.body.trash_id;
  await svg
    .restoreSVG(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'SVG got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreSVG', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore SVG : '+ error });
    });
}

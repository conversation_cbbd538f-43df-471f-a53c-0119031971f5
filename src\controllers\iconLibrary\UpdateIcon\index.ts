
import { IconLibraryModule } from '../../../modules/iconLibrary';
import { Request, Response } from 'express';
import { updateIconLibrary } from '../../../types/iconLibrary';
import { ToLowerCase } from '../../../helpers/dataFormatHelper';

export async function UpdateIconLibrary (
  request: Request,
  response: Response,
):Promise<void>{
  try {
    const reqBody = request.body;
    const updatedataObj : updateIconLibrary = {};

    // Const organization_id = request.headers.organization as string;
    const iconLibrary = new IconLibraryModule();

    if (reqBody.name !== undefined){
      updatedataObj.name = ToLowerCase(reqBody.name);
    }
    if (reqBody.type !== undefined){
      updatedataObj.type = reqBody.type;
    }
    if (reqBody.category !== undefined){
      updatedataObj.category = reqBody.category;
    }

    iconLibrary.UpdateIcon(reqBody.icon_id, updatedataObj)
      .then((res) => {
        response.send({
          status: 1,
          message: 'UpdateIcon Succesfully',
          data: res,
        });
      });
  } catch (error){
    if (error instanceof Error){
      response
        .status(400)
        .send({ status: 0, error: 'Error in UpdateIconLibrary', message: error.message });
    }
  }

}

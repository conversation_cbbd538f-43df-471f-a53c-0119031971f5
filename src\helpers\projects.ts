import { FontType } from '../types/projects';

export async function generateProjectId (): Promise<string> {
  const alphabet =
      '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  let organizationId = 'P';

  for (let i = 0; i < 5; i++) {
    const randomIndex = Math.floor(Math.random() * alphabet.length);
    organizationId += alphabet[randomIndex];
  }

  return organizationId;
}

export async function generateFontLink (fontName: FontType): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      if (typeof fontName !== 'string' || !fontName.trim()) {
        reject('Invalid type: fontName must be a non-empty string');
      }

      const formattedFontName = fontName
        .split(' ')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join('+');
      const fontLink = 'https://fonts.googleapis.com/css2?family='+
      formattedFontName
      +':ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap'.trim();
      resolve(fontLink);
    } catch (error) {
      reject(error);
    }
  });
}

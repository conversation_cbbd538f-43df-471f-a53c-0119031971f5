import app  from '../../../app';
import request from 'supertest';
describe('Get Scene id which includes given floor_id Endpoint', () => {
  it('should return a scene_id', async () => {
    const res = await request(app)
      .get('/publicapis/getProjectSceneId/65af4e50f48cdb1916292798?organization_id=HIPUat&floor_id=5')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('scene_id');
    expect(res.body.scene_id).not.toEqual('');
  });

  it('should return an error message saying organization not found', async () => {
    const res = await request(app)
      .get(
        '/publicapis/getProjectSceneId/65af4e50f48cdb1916292798?organization_id=HUat&floor_id=5',
      )   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(400);
    expect(res.body).toHaveProperty('error');
    expect(res.body.error).toEqual('organization_id not found');
  });
  it('should return an error message saying floor not found', async () => {
    const res = await request(app)
      .get(
        '/publicapis/getProjectSceneId/65af4e50f48cdb1916292798?organization_id=HIPUat&floor_id=101',
      )   // Invalid floor Id
      .send();
    expect(res.statusCode).toEqual(500);
    expect(res.body).toHaveProperty('error');
    expect(res.body.error).toEqual('Floor not present in scenes');
  });
});

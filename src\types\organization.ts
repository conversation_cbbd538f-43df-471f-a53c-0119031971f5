import { FontType, Theme } from './projects';

export enum UserRole {
  ADMIN = 'admin',
  READER = 'reader',
  EDITOR = 'editor',
  SUPPORTER = 'supporter',
}
export type User = {
  profilePicture ?: string;
  user_id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  organization_id?: string[];
};
export type Role = {
  user_id: string;
  created_time: string;
  role: UserRole;
  email: string | undefined;
};
export type Organization = {
  _id: string;
  name: string;
  founding_date: Date;
  contact_email: string;
  phone_number: string;
  address: string;
  website: string;
  max_users: number;
  roles: Role[];
  thumbnail: string;
  unique_org_id: string;
  primary:string,
  secondary:string,
  primary_text:string,
  secondary_text:string,
  font_type:FontType,
  font_url?:string,
  theme: Theme,
  measurement_id?:string,
  baseCurrency?: string,
  exchangeRatio?:object,
};
export type OrgThumbnail = {
  _id: string;
  thumbnail: string;
};
export type CreateOrganizationInput = {
  name: string;
  founding_date: Date;
  contact_email: string;
  phone_number: string;
  address: string;
  website: string;
  max_users: number;
  thumbnail: string;
  organizationId: string;
  primary:string,
  secondary:string,
  primary_text:string,
  secondary_text:string,
  font_type:FontType,
  font_url?:string,
  theme: Theme,
  unique_org_id: string,
  baseCurrency?: string,
  measurement_id?:string,
};

export type FormattedRate = {
  currency: string;
  rate: number | null;
};

export type UpdateOrganizationInput = {
  name?: string;
  founding_date?: Date;
  contact_email?: string;
  contact_phone_number?: string;
  address?: string;
  website?: string;
  thumbnail? : string;
  max_users?: number;
  unique_org_id?: string;
  theme?: Theme,
  primary?:string,
  secondary?:string,
  primary_text?:string,
  secondary_text?:string,
  font_type?:FontType,
  font_url?: string,
  measurement_id?:string,
  baseCurrency?:string,
  exchangeRatio?:FormattedRate[],
};

// Define other function inputs and outputs as needed

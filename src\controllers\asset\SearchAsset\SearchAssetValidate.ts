import { Request, Response, NextFunction } from 'express';
import { validationResult, header, param, query } from 'express-validator';

const searchAssetValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  param('project_id', 'Project ID is required').notEmpty(),
  query('searchQuery', 'Search parameter must be a string').notEmpty().isString(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default searchAssetValidate;

import { Amenity, CreateAmenityInput, UpdateAmenityInput, Media, CreateMediaObj,
  getCategories,
  bulkUpdateType} from '../../types/amenity';
import { amenitySchema } from '../../schema/amenitiesSchema';
import mongoose from 'mongoose';
// Import { storageUpload } from '../../helpers/storageUpload';
import { UnknownObject, arrayToObject } from '../../helpers/dataFormatHelper';
import logger from '../../config/logger';
import { trashModule } from '../trash/index';
import { trashType } from '../../types/trash';
import { Models } from '../../types/extras';
// Import { messaging } from 'firebase-admin';
export class AmenityModule {
  private model: mongoose.Model<Amenity>;
  public storagepath;
  constructor (project_id: string, organization_id: string) {
    this.model = mongoose.model<Amenity>(
      `${project_id}${Models._AMENITIES}`,
      amenitySchema,
    );
    this.storagepath='CreationtoolAssets/'+organization_id+'/projects/'+project_id+'/amenities/';
  }
  public async CreateAmenity (createAmenityData: CreateAmenityInput):Promise<Amenity | void> {
    logger.info('CreateAmenity called', { createAmenityData: createAmenityData });
    try {
      const newAmenity: Amenity = {
        _id: createAmenityData.id,
        project_id: createAmenityData.project_id,
        name: createAmenityData.name,
        category: createAmenityData.category,
        community_id: createAmenityData.community_id,
        thumbnail: createAmenityData.thumbnail,
        description: createAmenityData.description,
        media_type: createAmenityData.media_type,
        file: createAmenityData.file,
        embed_link: createAmenityData.embed_link,
        tour_id: createAmenityData.tour_id,
        modified: new Date().toISOString(),
      };
      logger.info('CreateAmenity Successfull', { newAminity: newAmenity });
      const amenities = new this.model(newAmenity);
      const amenity = await amenities.save();
      logger.info('CreateAmenity Saved', { newAminity: newAmenity });
      return amenity;
    } catch (error) {
      throw new Error('Unable to create amenity for this project'+error);
    }
  }
  public async UpdateAmenity (amenity_id: string, updateAmenity: UpdateAmenityInput): Promise<Amenity | void> {
    logger.info('UpdateAmenity called', { amenity_id: amenity_id, updateAmenity: updateAmenity});
    try {
      const existingAmenity = await this.model.findOne({ _id: amenity_id });

      if (existingAmenity) {
        console.log(existingAmenity);
        const updatedAmenity = await this.model.findOneAndUpdate(
          { _id: amenity_id },
          {
            $set: {
              name: updateAmenity.name,
              category: updateAmenity.category,
              thumbnail: updateAmenity.thumbnail,
              description: updateAmenity.description,
              community_id: updateAmenity.community_id,
              embed_link: updateAmenity.embed_link,
              tour_id: updateAmenity.tour_id,
              file: updateAmenity.file,
              modified: new Date().toISOString(),
            },
          },
          { new: true },
        );

        if (updatedAmenity) {
          logger.info('UpdateAmenity Succesfull', { updatedAmenity: updatedAmenity });
          return updatedAmenity;
        }
        logger.error('Error in updating amenity');
        throw new Error('Error in updating amenity');

      } else {
        logger.error('Unable to find existing amenity');
        throw new Error('Unable to find existing amenity');
      }
    } catch (error) {
      logger.error('Internal Server Error', {message: error});
      throw new Error('Internal Server Error'+error);
    }
  }
  public async DeleteAmenity (amenity_id: string): Promise<Amenity> {
    logger.info('DeleteAmenity called', {amenity_id: amenity_id});
    try {
      const deletedAmenity: unknown = await this.model.findOneAndDelete({_id: amenity_id});
      logger.info('DeleteAmenity Succesfull', { DeleteAmenity: deletedAmenity });

      return deletedAmenity as Amenity;
    } catch (error) {
      logger.error('Cannot delete Aminity', {message: error});
      throw error; // Rethrow the error to reject the promise
    }
  }
  public async CreateMedia (amenity_id: string, mediaObj: Media): Promise<Amenity| void> {
    logger.info('CreateMedia called', {amenity_id: amenity_id, mediaObj: mediaObj});
    try {
      const existingAmenity = await this.model.findOne({ _id: amenity_id });

      console.log(mediaObj);
      if (existingAmenity) {
        // ExistingAmenity.media.push(mediaObj);
        const updatedAmenity = await existingAmenity.save();
        if (updatedAmenity) {
          logger.info('Media Created Succesfully', {updatedAmenity: updatedAmenity});
          return updatedAmenity;
        }
        logger.error('Error in updating amenity');
        throw new Error('Error in updating amenity');

      } else {
        logger.error('Unable to find existing amenity');
        throw new Error('Unable to find existing amenity');
      }
    } catch (error) {
      logger.error('Internal Server Error', {message: error});
      throw new Error('Internal Server Error'+error);
    }
  }
  public async UpdateMedia (amenity_id: string, media_id: string, mediaObj: CreateMediaObj): Promise<Amenity| void> {
    try {
      logger.info('UpdateMedia Called', {mediaObj: mediaObj, amenity_id: amenity_id, media_id: media_id});
      const updatedAmenity = await this.model.findOneAndUpdate(
        { _id: amenity_id, 'media': { $elemMatch: { '_id': media_id } } },
        {
          $set: {
            'media.$.media_type': mediaObj.media_type,
            'media.$.link': mediaObj.link,
            'media.$.media_file': mediaObj.media_file,
            'media.$.tour_id': mediaObj.tour_id,
          },
        },
        { new: true },
      );
      logger.info('UpdateMedia Succesfull', {updatedAmenity: updatedAmenity});
      if (updatedAmenity) {
        return updatedAmenity;
      }
      throw new Error('Error in updating amenity media');

    } catch (error) {
      throw new Error('Internal Server Error'+error);
    }
  }
  public async DeleteMedia (amenity_id: string, media_id: string): Promise<Amenity | void> {
    logger.info('DeleteMedia Called', {amenity_id: amenity_id, media_id: media_id});
    try {
      const updatedAmenity = await this.model.findOneAndUpdate(
        { _id: amenity_id },
        { $pull: { 'media': { _id: media_id } } },
        { new: true },
      );

      if (updatedAmenity) {
        logger.info('DeleteMedia Successfull', {updatedAmenity: updatedAmenity});
        return updatedAmenity;
      }
      logger.error('Error in deleting media from amenity');
      throw new Error('Error in deleting media from amenity');

    } catch (error) {
      logger.error('Internal Server Error', {message: error});
      throw new Error('Internal Server Error' + error);
    }
  }
  // Public async UploadFiles (amenity_id: string, file:Express.Multer.File):Promise<string> {
  //   Console.log(file);
  //   Return new Promise((resolve, reject) => {
  //     Const uploadOptions = {
  //       Destination:
  //               This.storagepath+amenity_id+'/'
  //               +file.originalname,
  //     };

  //     StorageUpload(uploadOptions, file.path).then((thumbnailUrl) => {
  //       Resolve(thumbnailUrl);
  //     })
  //       .catch((err) => {
  //         Reject(err);
  //       });
  //   });
  // }
  public async GetAmenities (category:string, search?: string): Promise<object | null> {
    logger.info('GetAmenities Called', {category: category}, {search: search});
    try {
      const categoryName = category ? { category } : {};
      const pipeline = [
        // Match stage to filter by category if a category is specified
        ...(category ? [{ $match: categoryName }] : []),
        ...(search ? [{
          $match: {
            $or: [
              { name: { $regex: search, $options: 'i' } },
              { description: { $regex: search, $options: 'i' } },
            ],
          },
        }] : []),
        // Add a temporary field to determine if 'order' is missing
        {
          $addFields: {
            hasOrder: {
              $cond: {
                if: { $eq: [{ $type: '$order' }, 'missing'] },
                then: 0,
                else: 1,
              },
            },
          },
        },
        // Group items by category
        {
          $group: {
            _id: '$category',
            items: {
              $push: {
                item: '$$ROOT',
                hasOrder: '$hasOrder',
              },
            },
          },
        },
        // Sort items within each category by 'hasOrder' and 'order'
        {
          $project: {
            items: {
              $sortArray: {
                input: '$items',
                sortBy: {
                  'hasOrder': -1,
                  'item.order': 1,
                },
              },
            },
          },
        },
        // Unwind sorted items
        {
          $unwind: '$items',
        },
        // Replace root and unset 'hasOrder'
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: ['$items.item', { hasOrder: '$items.hasOrder' }],
            },
          },
        },
        {
          $unset: 'hasOrder',
        },
      ];
      const amenities:Array<UnknownObject> = await this.model.aggregate(pipeline);

      // Const removeMediaField = async (amenityId: mongoose.Types.ObjectId): Promise<Amenity | null> => {
      //   Try {
      //     Const documentBeforeUpdate = await this.model.findById(amenityId);
      //     Console.log('Before update:', documentBeforeUpdate);

      //     Const result : Amenity | null= await this.model.findByIdAndUpdate(
      //       AmenityId,
      //       { $unset: { media: 1 } },
      //       { new: true },
      //     );

      //     Console.log('Update result:', result);

      //     If (!result) {
      //       Console.log('No document was updated. Check if the _id is correct.');
      //       Return null;
      //     }
      //     Return result;
      //   } catch (error) {
      //     Console.error('Error updating document:', error);
      //     Return null;
      //   }
      // };

      const changeSchema = async (amenity: Array<UnknownObject>): Promise<UnknownObject[]> => {
        const updatedAmenities: UnknownObject[] = [];

        for (const item of amenity) {
          if (Array.isArray(item.media) && item.media.length > 0) {
            if (!item.media_type){
              console.log('before editing*****', item);
              if (item.media[0]?.file) {
                item.file = item.media[0].file;
              }
              if (!item.file){
                item.file = item.thumbnail;
              }
              if (item.media[0]?.media_type) {
                item.media_type = item.media[0].media_type;
              }
              if (item.media[0]?.link) {
                item.embed_link = item.media[0].link;
              }
              if (item.media[0]?.tour_id) {
                item.tour_id = item.media[0].tour_id;
              }

              console.log('111111111111', item.media);
              // Delete item.media;                               //Temporarly media array will not be deleted
            } else {
              if (!item.file){
                item.file = item.thumbnail;
              }
            }
            updatedAmenities.push(item);
          } else {
            if (!item.file){
              item.file = item.thumbnail;
            }
            // Delete item.media;                                  //Temporarly media array will not be deleted
            updatedAmenities.push(item);
          }
          await this.model.findOneAndUpdate(
            { _id: item._id },
            {
              $set: {
                name: item.name,
                category: item.category,
                thumbnail: item.thumbnail,
                description: item.description,
                community_id: item.community_id,
                embed_link: item.embed_link,
                tour_id: item.tour_id,
                file: item.file,
                media_type: item.media_type,
              },
            },
            { new: true }, // Return the updated document
          );
        }
        return updatedAmenities;
      };
      const updatedAmn = await changeSchema(amenities);
      const amenitiesObj = arrayToObject(updatedAmn) as Record<string, Amenity>;
      logger.info('GetAmenities Successfull', {amenitiesObj: amenitiesObj});
      return amenitiesObj;

    } catch (error) {
      logger.error('Error in GetAmenities', {message: error});
      return null;
    }
  }
  public async getAmenitiesCount (community_id:string):Promise <number | null>{
    logger.info('GetAmenities Called', {community_id: community_id});
    try {
      const amenitiesCount = await this.model.countDocuments({ community_id: community_id });
      logger.info('GetAmenities Successfull', {amenitiesCount: amenitiesCount});
      return amenitiesCount;
    } catch (error) {
      logger.error('Error in GetAmenities', {message: error});
      return null;
    }
  }
  public async getCategories (): Promise<Array<getCategories> | null> {
    logger.info('getCategories Called');
    try {
      const categoriesWithCountAndName = await this.model.aggregate([
        {
          $group: {
            _id: '$category',
            count: { $sum: 1 },
          },
        },
        {
          $group: {
            _id: null,
            categories: {
              $push: {
                category: '$_id',
                count: '$count',
              },
            },
          },
        },
        {
          $project: {
            _id: 0,
            categories: 1,
          },
        },
      ]);
      logger.info('getCategories Successfull', {categoriesWithCountAndName: categoriesWithCountAndName});
      return categoriesWithCountAndName[0].categories;
    } catch (error) {
      logger.error('Error in getCategories Successfull', {message: error});
      return null;
    }
  }
  public async updateBulkAmenities (payload: bulkUpdateType): Promise<string> {
    logger.info('updateBulkAmenities Called', {payload: payload});
    return new Promise<string>((resolve, reject) => {
      const promises: Promise<Amenity | null>[] = payload.query.map(async (item) => {
        return this.model.findOneAndUpdate(
          { _id: item.id },
          {
            $set: {
              order: item.order,
              category: item.category,
            },
          },
          { new: true }, // Return the updated document
        )
          .then((res) => {
            return res;
          })
          .catch(() => {
            return null;
          });
      });

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('updateBulkAmenities successfully', {results: results});
            resolve('Documents updated successfully');
          } else {
            logger.error('Error while updating sidebar option');
            reject('Error while updating sidebar option');
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }
  public async moveToTrash (
    amenityIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<Amenity | void> {
    logger.info('moveToTrash Successfull',
      {amenityIds: amenityIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: amenityIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('AMENITYs corresponding to AMENITY IDs provided not found');
      throw 'AMENITYs corresponding to AMENITY IDs provided not found';
    }
    const AMENITYdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._AMENITIES}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(AMENITYdataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: amenityIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moceToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
  public async restoreAmenity (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreAMENITY Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);
    if (restoredData) {
      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        const amenities = new this.model(item);
        amenities.modified = new Date().toISOString(),
        await amenities.save();
      });

      await Promise.all([
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreAMENITY is Successfull');
        return 'AMENITY got restored';
      });
    } else {
      logger.error('Error in restoreAMENITY');
      throw new Error('Failed to restore amenity data from trash');
    }
  }
}

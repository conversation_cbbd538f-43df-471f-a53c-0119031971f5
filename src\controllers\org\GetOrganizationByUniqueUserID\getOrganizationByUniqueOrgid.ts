import { Response } from 'express';
import { OrganizationModule } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import { Organization } from '../../../types/organization';

export default async function getOrganizationByUniqueOrgid (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organizationModule = new OrganizationModule();

  const unique_org_id = request.params.unique_org_id;

  if (!unique_org_id) {
    response.send({ status: 0, error: 'No unique user  ID provided' });
    return;
  }

  try {
    const organization: Organization | null =
      await organizationModule.FindOrganizationByunique_org_id(unique_org_id);

    if (organization) {
      response.send({ status: 1, data: organization });
    } else {
      response.send({ status: 0, error: 'Organization not found' });
    }
  } catch (error) {
    response.send({
      status: 0,
      error: `Error while finding organization: ${error}`,
    });
  }
}

import { VirtualTour, createVirtualTourData, addImageType, updateImageInp, Image, Group, SubGroup, coordinate, Link, labels } from './../../types/virtualTour';
import { virtualTourSchema } from '../../schema/virtualTourSchema';
import mongoose, { Error, Types } from 'mongoose';
import logger from '../../config/logger';
import { trashModule } from '../trash';
import { trashType } from '../../types/trash';
import { Models } from '../../types/extras';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';

export class VirtualTourModule {
  private model: mongoose.Model<VirtualTour>;
  public storagepath;
  constructor (organization_id: string, project_id: string) {
    this.model = mongoose.model<VirtualTour>(
      organization_id + '_' + project_id + `${Models._VIRTUAL_TOUR}`,
      virtualTourSchema,
    );
    this.storagepath =
      'CreationtoolAssets/' +
      organization_id +
      '/projects/' +
      project_id +
      '/tours';
  } public async CreateVirtualTour (
    CreateVirtualTourData: createVirtualTourData,
  ): Promise<VirtualTour | null> {
    logger.info('CreateVirtualTour Called', { CreateVirtualTourData });

    try {
      const virtualTourData = {
        ...CreateVirtualTourData,
        images: {},
        groups: {},
      };

      const virtualTour = new this.model(virtualTourData);
      const newVirtualTour = await virtualTour.save();

      if (!newVirtualTour) {
        logger.error('Error creating virtual tour');
        return null;
      }

      logger.info('CreateVirtualTour Successful', { newVirtualTour });
      return newVirtualTour as VirtualTour;
    } catch (error) {
      logger.error('Error in CreateVirtualTour', { message: error });
      throw new Error(`${error}`);
    }
  }

  public async GetTourById (tour_id: string): Promise<VirtualTour | null> {
    try {
      // Validate if the `tour_id` is a valid ObjectId
      if (!tour_id || !Types.ObjectId.isValid(tour_id)) {
        throw new Error('Invalid Tour ID');
      }

      const query = { _id: new Types.ObjectId(tour_id) };
      const tourData: VirtualTour | null = await this.model.findOne(query);

      if (!tourData) {
        throw new Error('Document not found');
      }

      return tourData;
    } catch (error) {
      throw new Error(`Error fetching tour by ID: ${error}`);
    }
  }

  public async GetAllTours (): Promise<Record<string, VirtualTour> | void> {
    try {
      const data: Array<UnknownObject> = await this.model.find();
      const transformedResult = arrayToObject(data) as Record<string, VirtualTour>;

      // Process each tour to order images and groups
      Object.keys(transformedResult).forEach((tourKey) => {
        const tour = transformedResult[tourKey];

        // Handle images ordering if exists
        if (tour.images && Object.keys(tour.images).length > 0) {
          const orderedImages: Record<string, Image> = {};
          Object.values(tour.images)
            .sort((a, b) => (a.order || 0) - (b.order || 0))
            .forEach((image) => {
              orderedImages[image.id] = image;
            });
          tour.images = orderedImages;
        }

        if (tour.groups && Object.keys(tour.groups).length > 0) {
          const orderedGroups: Record<string, Group> = {};
          Object.values(tour.groups)
            .sort((a, b) => (a.order || 0) - (b.order || 0))
            .forEach((group) => {
              if (group.subgroups && Object.keys(group.subgroups).length > 0) {
                const orderedSubgroups: Record<string, SubGroup> = {};
                Object.values(group.subgroups)
                  .sort((a, b) => (a.order || 0) - (b.order || 0))
                  .forEach((subgroup) => {
                    orderedSubgroups[subgroup._id] = subgroup;
                  });
                group.subgroups = orderedSubgroups;
              }
              orderedGroups[group._id] = group;
            });
          tour.groups = orderedGroups;
        }
      });

      return transformedResult;
    } catch (error) {
      throw new Error(`Error fetching tours: ${error}`);
    }
  }

  public async UpdateTour (
    tour_id: string,
    payload: Partial<VirtualTour>,
  ): Promise<VirtualTour | null> {
    try {
      if (!tour_id) {
        throw new Error('Invalid Tour ID');
      }

      const { ...updatePayload } = payload;

      const query = { _id: tour_id };

      const updateData = {
        ...updatePayload,
      };

      if (updatePayload.images) {
        updateData.images = {
          ...(await this.model.findOne(query))?.images || {},
          ...updatePayload.images,
        };
      }

      if (updatePayload.groups) {
        updateData.groups = {
          ...(await this.model.findOne(query))?.groups || {},
          ...updatePayload.groups,
        };
      }
      if (updatePayload.initial_rotation){
        updateData.initial_rotation = JSON.parse(updatePayload.initial_rotation);
      }

      const updatedTour = await this.model.findOneAndUpdate(
        query,
        updateData,
        {
          new: true,
          lean: true,
          runValidators: true,
        },
      );

      if (!updatedTour) {
        logger.error('Tour not found for update', { tour_id });
        return null;
      }

      return updatedTour;
    } catch (error) {
      logger.error('Error in UpdateTour method', { message: error });
      throw new Error(`Error updating tour: ${error}`);
    }
  }

  public async DeleteTour (tour_id: string): Promise<VirtualTour | null> {
    try {
      if (!tour_id || !Types.ObjectId.isValid(tour_id)) {
        throw new Error('Invalid Tour ID');
      }

      const query = { _id: new Types.ObjectId(tour_id) };
      const deletedTour = await this.model.findOneAndDelete(query).lean() as VirtualTour | null;

      if (!deletedTour) {
        throw new Error('Tour not found');
      }

      return deletedTour;
    } catch (error) {
      logger.error('Error in DeleteTour method', { message: error });
      throw new Error(`Error deleting tour: ${error}`);
    }
  }
  public async RemoveField (
    tour_id: string,
    fieldName: string,
  ): Promise<VirtualTour | null> {
    try {
      const unsetObj: any = {};
      unsetObj[fieldName] = '';

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $unset: unsetObj },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour not found for removing field', { tour_id, fieldName });
        return null;
      }

      logger.info(`Field ${fieldName} removed successfully from tour`, { tour_id });
      return updatedTour;
    } catch (error) {
      logger.error(`Error removing field ${fieldName} from tour`, { error });
      throw new Error(`Failed to remove field: ${error}`);
    }
  }
  public async moveToTrash (
    virtualtour_id: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<VirtualTour | void> {
    logger.info('moveToTrash Successfull',
      {virtualtour_id: virtualtour_id, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    console.log('********', this.model.find());

    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: virtualtour_id.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('virtualtour_id corresponding to virtualTour IDs provided not found');
      throw 'virtualtour_id corresponding to virtualTour IDs  provided not found';
    }
    const virtualTourdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._VIRTUAL_TOUR}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(virtualTourdataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: virtualtour_id.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moveToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', { message: err });
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', { message: err });
        throw err;
      });
  }
  public async restoreVirtualTour (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreUnit Called',
      { organization_id: organization_id, project_id: project_id, trash_Id: trash_Id });
    const trash = new trashModule(organization_id);
    const trash_ids: Array<string> = [];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        await this.CreateVirtualTour(item);
      });

      await Promise.all([
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restorevirtualTour is Successfull');
        return 'VirtualTour got restored';
      });
    } else {
      logger.error('Error in restoreVirtualTour');
      throw new Error('Failed to restore Virtual Tour data from trash');
    }
  }

  public async AddImagesToTour (tour_id: string, imagePayload: addImageType): Promise<VirtualTour> {
    try {
      const tour = await this.model.findById(tour_id);
      if (!tour) {
        logger.error('Tour not found for adding images', { imagePayload });
        throw new Error('Tour not found');
      }

      const updateData = {
        [`images.${imagePayload.id}`]: {
          ...imagePayload,
        },
      };

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: updateData },
        { new: true },
      );

      if (!updatedTour) {
        throw new Error('Failed to update tour with new image');
      }

      logger.info('Image added successfully to tour', { tour: updatedTour });
      return updatedTour;
    } catch (error) {
      logger.error('Error in AddImagesToTour', { message: error });
      throw new Error(`Failed to add image: ${error}`);
    }
  }

  public async UpdateTourImage (
    tour_id: string,
    image_id: string,
    updateImage: updateImageInp,
  ): Promise<VirtualTour | void> {
    logger.info('UpdateTourImage called', { tour_id, image_id, updateImage });

    try {
      const tour = await this.model.findById(tour_id);
      if (!tour) {
        logger.error('Tour not found', { tour_id });
        throw new Error('Tour not found');
      }

      if (!tour.images?.[image_id]) {
        logger.error('Image not found in tour', { tour_id, image_id });
        throw new Error('Image not found in tour');
      }

      const updateData = {
        [`images.${image_id}.name`]: updateImage.name,
        [`images.${image_id}.url`]: updateImage.url,
        [`images.${image_id}.thumbnail`]: updateImage.thumbnail,
        [`images.${image_id}.rotation`]: updateImage.rotation,
        [`images.${image_id}.order`]: updateImage.order,
        [`images.${image_id}.groupId`]: (updateImage.groupId),
        [`images.${image_id}.subGroupId`]: (updateImage.subGroupId),
        [`images.${image_id}.tile_rendering_status`]: (updateImage.tile_rendering_status),
        [`images.${image_id}.tile_rendering_failed_info`]: (updateImage.tile_rendering_failed_info),

      };

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: updateData },
        { new: true },
      );

      if (!updatedTour) {
        throw new Error('Failed to update tour');
      }

      logger.info('Image updated successfully in the tour', { updatedTour });
      return updatedTour;
    } catch (error) {
      logger.error('Error updating image in tour', { message: error });
      throw new Error(`Failed to update image: ${error}`);
    }
  }

  public async DeleteImageFromTour (tour_id: string, image_id: string): Promise<VirtualTour | void> {
    logger.info('DeleteImageFromTour Called', { tour_id, image_id });
    try {
      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $unset: { [`images.${image_id}`]: '' } },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Error in deleting image from tour');
        throw new Error('Error in deleting image from tour');
      }

      logger.info('DeleteImageFromTour Successful', { updatedTour });
      return updatedTour;
    } catch (error) {
      logger.error('Internal Server Error', { message: error });
      throw new Error('Internal Server Error' + error);
    }
  }

  public async DuplicateTourImage (tour_id: string, image_id: string, targetorder: number): Promise<VirtualTour> {
    logger.info('DuplicateTourImage Called', { tour_id, image_id });
    try {
      const tour = await this.model.findById(tour_id);

      if (!tour || !tour.images?.[image_id]) {
        logger.error('Tour or Image not found in the tour', { tour_id, image_id });
        throw new Error('Tour or Image not found in the tour');
      }

      const image = tour?.images[image_id];
      logger.info('Image found Successful', { image });

      const id = new mongoose.Types.ObjectId().toString();

      const duplicateData = {
        [`images.${id}`]: {
          ...image,
          id,
          order: targetorder,
        },
      };

      const duplicateTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: duplicateData },
        { new: true },
      );

      if (!duplicateTour) {
        throw new Error('Failed to duplicate tour');
      }

      logger.info('Image added successfully to tour', { tour: duplicateTour });

      return duplicateTour;
    } catch (error) {
      logger.error('Internal Server Error', { message: error });
      throw new Error('Internal Server Error' + error);
    }
  }

  public async GetTourImageById (tour_id: string, image_id: string): Promise<Image | null> {
    logger.info('GetTourImageById Called', { tour_id, image_id });

    try {
      const tour = await this.model.findById(tour_id);

      if (!tour || !tour.images?.[image_id]) {
        logger.error('Image not found in the tour', { tour_id, image_id });
        return null;
      }

      const image = tour.images[image_id];
      logger.info('GetTourImageById Successful', { image });
      return image;
    } catch (error) {
      logger.error('Error in GetTourImageById', { message: error });
      throw new Error(`Failed to fetch image: ${error}`);
    }
  }

  public async GetTourImages (tour_id: string): Promise<{ [key: string]: Image } | null> {
    logger.info('GetTourImages Called', { tour_id });

    try {
      const tour = await this.model.findById(tour_id);

      if (!tour || !tour.images) {
        logger.error('Images not found in the tour', { tour_id });
        return null;
      }

      logger.info('GetTourImages Successful', { images: tour.images });
      return tour.images;
    } catch (error) {
      logger.error('Error in GetTourImages', { message: error });
      throw new Error(`Failed to fetch images: ${error}`);
    }
  }
  public async AddGroupToTour (
    tour_id: string,
    groupData: Partial<Group>,
  ): Promise<VirtualTour | null> {
    try {
      const newGroup = {
        ...groupData,
      };

      if (typeof newGroup.order === 'string') {
        newGroup.order = parseInt(newGroup.order, 10);
      }

      const updateData = {
        [`groups.${newGroup._id}`]: newGroup,
      };

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: updateData },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour not found for creating group', { tour_id });
        throw new Error('Tour not found');
      }

      logger.info('Group created successfully', {
        tour_id,
        groupId: newGroup._id,
      });

      return updatedTour;
    } catch (error) {
      logger.error('Error in AddGroupToTour method', { message: error });
      throw new Error(`Failed to create group: ${error}`);
    }
  }

  public async GetGroups (tour_id: string): Promise<{ [key: string]: Group } | null> {
    try {
      const tour = await this.model.findById(tour_id, 'groups');

      if (!tour) {
        logger.error('Tour not found', { tour_id });
        throw new Error('Tour not found');
      }

      logger.info('Groups retrieved successfully', {
        tour_id,
        groupCount: Object.keys(tour.groups || {}).length,
      });

      return tour.groups || null;
    } catch (error) {
      logger.error('Error in GetGroups method', { message: error });
      throw new Error(`Failed to retrieve groups: ${error}`);
    }
  }

  public async GetGroupById (
    tour_id: string,
    group_id: string,
  ): Promise<Group | null> {
    try {
      const tour = await this.model.findById(tour_id);

      if (!tour || !tour.groups?.[group_id]) {
        logger.error('Group not found', { tour_id, group_id });
        return null;
      }

      const group = tour.groups[group_id];
      logger.info('Group retrieved successfully', {
        tour_id,
        group_id,
      });

      return group;
    } catch (error) {
      logger.error('Error in GetGroupById method', { message: error });
      throw new Error(`Failed to retrieve group: ${error}`);
    }
  }

  public async UpdateGroup (
    tour_id: string,
    group_id: string,
    updateData: Partial<Group>,
  ): Promise<VirtualTour | null> {
    try {
      const newUpdateData = { ...updateData };

      if (typeof newUpdateData.order === 'string') {
        newUpdateData.order = parseInt(newUpdateData.order, 10);
      }

      const updateFields = {
        [`groups.${group_id}.name`]: newUpdateData.name,
        [`groups.${group_id}.icon`]: newUpdateData.icon,
        [`groups.${group_id}.order`]: newUpdateData.order,
      };

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: updateFields },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour or Group not found', { tour_id, group_id });
        throw new Error('Tour or Group not found');
      }

      logger.info('Group updated successfully', {
        tour_id,
        group_id,
      });

      return updatedTour;
    } catch (error) {
      logger.error('Error in UpdateGroup method', { message: error });
      throw new Error(`Failed to update group: ${error}`);
    }
  }

  public async DeleteGroup (
    tour_id: string,
    group_id: string,
  ): Promise<VirtualTour | null> {
    try {
      if (!tour_id) {
        throw new Error('Invalid Tour ID');
      }
      if (!group_id) {
        throw new Error('Invalid Group ID');
      }

      // First, find all images that have this groupId
      const tour = await this.model.findOne({ _id: tour_id });
      if (!tour) {
        throw new Error('Tour not found');
      }

      // Define more specific types for MongoDB update operations
      interface MongoUnsetOperation {
        $unset: Record<string, string>;
      }
      interface MongoSetOperation {
        $set: Record<string, string | number>;
      }

      type MongoUpdateOperation = MongoUnsetOperation & Partial<MongoSetOperation>;

      // Prepare the update operations
      const updateOperations: MongoUpdateOperation = {
        $unset: { [`groups.${group_id}`]: '' },
      };

      if (Object.keys(tour.images).length > 0){
        // Find the length of uncategory images
        let totalUnCategoryImages: number = Object.keys(tour.images || {}).filter(
          (item) => (!('groupId' in tour.images[item]) || tour.images[item].groupId === '') && (!('subGroupId' in tour.images[item]) || tour.images[item].subGroupId === ''),
        ).length;

        // For each image that has this groupId, set it to empty string
        const imageUpdates: Record<string, string | number> = {};

        Object.keys(tour.images || {}).forEach((imageId) => {
          const image = tour.images[imageId];
          if (image.groupId === group_id) {
            totalUnCategoryImages++; // Increment
            imageUpdates[`images.${imageId}.groupId`] = '';
            if (image.subGroupId) { // If there's a subGroupId, clear that too since the parent group is being deleted
              imageUpdates[`images.${imageId}.subGroupId`] = '';
            }
            imageUpdates[`images.${imageId}.order`] = totalUnCategoryImages;
          }
        });

        // Add the image updates to the update operation if there are any
        if (Object.keys(imageUpdates).length > 0) {
          updateOperations.$set = imageUpdates;
        }
      }

      // Perform the update
      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        updateOperations,
        { new: true },
      );

      if (!updatedTour) {
        throw new Error('Tour not found');
      }

      logger.info('Group deleted successfully', {
        tour_id,
        group_id,
      });

      return updatedTour;
    } catch (error) {
      logger.error('Error in DeleteGroup method', { message: error });
      throw new Error(`Failed to delete group: ${error}`);
    }
  }

  public async CreateSubGroup (
    tour_id: string,
    group_id: string,
    subGroupData: SubGroup,
  ): Promise<VirtualTour | null> {
    try {
      const newSubGroupData = { ...subGroupData };
      if (typeof newSubGroupData.order === 'string') {
        newSubGroupData.order = parseInt(newSubGroupData.order, 10);
      }

      const updateData = {
        [`groups.${group_id}.subgroups.${newSubGroupData._id}`]: newSubGroupData,
      };

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: updateData },
        { new: true, runValidators: true },
      );

      if (!updatedTour) {
        logger.error('Group not found for creating Subgroup', { tour_id });
        throw new Error('Failed to update tour or group not found');
      }

      logger.info('Subgroup created successfully', {
        tour_id,
        group_id,
        subgroup_id: subGroupData._id,
      });

      return updatedTour;
    } catch (error) {
      logger.error('Error in CreateSubGroup method', {
        message: error instanceof Error ? error.message : error,
        tour_id,
        group_id,
        subGroupData,
      });
      throw error;
    }
  }

  public async UpdateSubGroup (
    tour_id: string,
    group_id: string,
    subgroup_id: string,
    updateData: Partial<SubGroup>,
  ): Promise<VirtualTour | null> {
    try {

      const tour = await this.model.findOne({ _id: tour_id });
      if (!tour) {
        throw new Error('Tour not found');
      }

      // Define more specific types for MongoDB update operations
      interface MongoUpdateOperation {
        $set: Record<string, any>;
        $unset: Record<string, any>;
      }
      const updatePayload: MongoUpdateOperation = { $set: {}, $unset: {} };

      let currentGroupId: string = '';
      let subGroupObject: object = {};
      for (const [gId, group] of Object.entries(tour.groups)) {
        if (group.subgroups && group.subgroups[subgroup_id]) {
          currentGroupId = gId;
          subGroupObject = group.subgroups[subgroup_id];
          break;
        }
      }

      const newUpdateData = { ...updateData };
      const isMoving = currentGroupId !== group_id; // If true considered as subgroup is moving to another group
      const targetGroupId = isMoving ? group_id : currentGroupId;

      if (isMoving) {
        updatePayload.$unset[`groups.${currentGroupId}.subgroups.${subgroup_id}`] = '';
        updatePayload.$set[`groups.${targetGroupId}.subgroups.${subgroup_id}`] = subGroupObject;
      }

      if (newUpdateData.order && typeof newUpdateData.order === 'string') { // Check the order is string then convert to number
        newUpdateData.order = parseInt(newUpdateData.order, 10);
      }

      if (newUpdateData.name !== undefined) {
        updatePayload.$set[`groups.${targetGroupId}.subgroups.${subgroup_id}.name`] = newUpdateData.name;
      }
      if (newUpdateData.icon !== undefined) {
        updatePayload.$set[`groups.${targetGroupId}.subgroups.${subgroup_id}.icon`] = newUpdateData.icon;
      }
      if (newUpdateData.order !== undefined) {
        updatePayload.$set[`groups.${targetGroupId}.subgroups.${subgroup_id}.order`] = newUpdateData.order;
      }

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        updatePayload,
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour, group, or subgroup not found', { tour_id, group_id, subgroup_id });
        throw new Error('Tour, group, or subgroup not found');
      }

      logger.info('Subgroup updated successfully', { tour_id, group_id, subgroup_id });
      return updatedTour;
    } catch (error) {
      logger.error('Error in UpdateSubGroup method', { error });
      throw new Error(`Failed to update subgroup: ${error instanceof Error ? error.message : error}`);
    }
  }

  public async DeleteSubGroup (
    tour_id: string,
    group_id: string,
    subgroup_id: string,
  ): Promise<VirtualTour | null> {
    try {
      if (!tour_id) {
        throw new Error('Invalid Tour ID');
      }
      if (!group_id) {
        throw new Error('Invalid Group ID');
      }
      if (!subgroup_id) {
        throw new Error('Invalid Subg Group ID');
      }
      // First, find the tour
      const tour = await this.model.findOne({ _id: tour_id });
      if (!tour) {
        throw new Error('Tour not found');
      }

      // Define more specific types for MongoDB update operations
      interface MongoUnsetOperation {
        $unset: Record<string, string>;
      }
      interface MongoSetOperation {
        $set: Record<string, string | number>;
      }

      type MongoUpdateOperation = MongoUnsetOperation & Partial<MongoSetOperation>;

      // Prepare the update operations
      const updateOperations: MongoUpdateOperation = {
        $unset: { [`groups.${group_id}.subgroups.${subgroup_id}`]: '' },
      };

      if (Object.keys(tour.images).length > 0){
        // Find the length of groups images + subgroups in the group
        let totalGroupsItem: number = [...Object.keys(tour.groups[group_id]?.subgroups || {}), ...Object.keys(tour.images || {}).filter(
          (item) => tour.images[item].groupId === group_id && (!('subGroupId' in tour.images[item]) || tour.images[item].subGroupId === ''),
        )].length - 1;

        // For each image that has this subgroup_id, set it to empty string
        const imageUpdates: Record<string, string | number> = {};
        Object.keys(tour.images || {}).forEach((imageId) => {
          const image = tour.images[imageId];
          if (image.subGroupId && image.subGroupId === subgroup_id) {
            totalGroupsItem++; // Increament
            imageUpdates[`images.${imageId}.subGroupId`] = '';
            imageUpdates[`images.${imageId}.order`] = totalGroupsItem;
          }
        });

        // Add the image updates to the update operation if there are any
        if (Object.keys(imageUpdates).length > 0) {
          updateOperations.$set = imageUpdates;
        }
      }

      // Perform the update
      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        updateOperations,
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour not found', { tour_id });
        throw new Error('Tour not found');
      }

      logger.info('Subgroup deleted successfully', { tour_id, group_id, subgroup_id });
      return updatedTour;
    } catch (error) {
      logger.error('Error in DeleteSubGroup method', { error });
      throw new Error(`Error deleting subgroup: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  public async UpdateGroupOrder (
    tour_id: string,
    group_id: string,
    newOrder: number,
  ): Promise<VirtualTour | null> {
    try {
      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: { [`groups.${group_id}.order`]: newOrder } },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour or group not found', { tour_id, group_id });
        throw new Error('Tour or group not found');
      }

      logger.info('Group order updated successfully', { tour_id, group_id, newOrder });
      return updatedTour;
    } catch (error) {
      logger.error('Error in UpdateGroupOrder method', { error });
      throw new Error(`Failed to update group order: ${error instanceof Error ? error.message : error}`);
    }
  }

  public async UpdateSubGroupOrder (
    tour_id: string,
    group_id: string,
    subgroup_id: string,
    newOrder: number,
  ): Promise<VirtualTour | null> {
    try {
      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: { [`groups.${group_id}.subgroups.${subgroup_id}.order`]: newOrder } },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour, group, or subgroup not found', { tour_id, group_id, subgroup_id });
        throw new Error('Tour, group, or subgroup not found');
      }

      logger.info('Subgroup order updated successfully', {
        tour_id,
        group_id,
        subgroup_id,
        newOrder,
      });

      return updatedTour;
    } catch (error) {
      logger.error('Error in UpdateSubGroupOrder method', { error });
      throw new Error(`Failed to update subgroup order: ${error instanceof Error ? error.message : error}`);
    }
  }

  public async UpdateGroupIdImg (
    tour_id: string,
    group_id: string,
    image_id: string,
  ): Promise<VirtualTour | null> {
    try {
      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: { [`images.${image_id}.groupId`]: group_id } },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour, image, or group not found', { tour_id, group_id, image_id });
        throw new Error('Tour, image, or group not found');
      }

      logger.info('Image linked to group successfully', { tour_id, group_id, image_id });
      return updatedTour;
    } catch (error) {
      logger.error('Error in UpdateGroupIdImg method', { error });
      throw new Error(`Failed to link image to group: ${error instanceof Error ? error.message : error}`);
    }
  }

  public async UpdateSubGroupIdImg (
    tour_id: string,
    group_id: string,
    subgroup_id: string,
    image_id: string,
  ): Promise<VirtualTour | null> {
    try {
      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        {
          $set: {
            [`images.${image_id}.groupId`]: group_id,
            [`images.${image_id}.subGroupId`]: subgroup_id,
          },
        },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour, image, group, or subgroup not found', {
          tour_id,
          group_id,
          subgroup_id,
          image_id,
        });
        throw new Error('Tour, image, group, or subgroup not found');
      }

      logger.info('Image linked to subgroup successfully', {
        tour_id,
        group_id,
        subgroup_id,
        image_id,
      });

      return updatedTour;
    } catch (error) {
      logger.error('Error in UpdateSubGroupIdImg method', { error });
      throw new Error(`Failed to link image to subgroup: ${error instanceof Error ? error.message : error}`);
    }
  }
  public async AddLink (
    tour_id: string,
    image_id: string,
    linkData: {
      position: coordinate,
      text: string,
      destination_img_id: string
    },
  ): Promise<VirtualTour | null> {
    try {
      const link_id =new mongoose.Types.ObjectId().toString();
      const newLink: Link = {
        _id: link_id,
        position: linkData.position,
        text: linkData.text,
        destination_img_id: linkData.destination_img_id,
      };

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: { [`images.${image_id}.links.${link_id}`]: newLink } },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour or image not found', { tour_id, image_id });
        throw new Error('Tour or image not found');
      }

      logger.info('Link added successfully', {
        tour_id,
        image_id,
        link_id,
      });

      return updatedTour;
    } catch (error) {
      logger.error('Error in AddLink method', { error });
      throw new Error(`Failed to add link: ${error instanceof Error ? error.message : error}`);
    }
  }

  public async UpdateLink (
    tour_id: string,
    image_id: string,
    link_id: string,
    updateData: Partial<{
      position: coordinate,
      text: string,
      destination_img_id: string
    }>,
  ): Promise<VirtualTour | null> {
    try {
      const updateFields: { [key: string]: any } = {};

      if (updateData.position !== undefined) {
        updateFields[`images.${image_id}.links.${link_id}.position`] = updateData.position;
      }
      if (updateData.text !== undefined) {
        updateFields[`images.${image_id}.links.${link_id}.text`] = updateData.text;
      }
      if (updateData.destination_img_id !== undefined) {
        updateFields[`images.${image_id}.links.${link_id}.destination_img_id`] = updateData.destination_img_id;
      }

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: updateFields },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour, image, or link not found', { tour_id, image_id, link_id });
        throw new Error('Tour, image, or link not found');
      }

      logger.info('Link updated successfully', { tour_id, image_id, link_id });
      return updatedTour;
    } catch (error) {
      logger.error('Error in UpdateLink method', { error });
      throw new Error(`Failed to update link: ${error instanceof Error ? error.message : error}`);
    }
  }

  public async DeleteLink (
    tour_id: string,
    image_id: string,
    link_id: string,
  ): Promise<VirtualTour | null> {
    try {
      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $unset: { [`images.${image_id}.links.${link_id}`]: '' } },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour, image, or link not found', { tour_id, image_id, link_id });
        throw new Error('Tour, image, or link not found');
      }

      logger.info('Link deleted successfully', { tour_id, image_id, link_id });
      return updatedTour;
    } catch (error) {
      logger.error('Error in DeleteLink method', { error });
      throw new Error(`Failed to delete link: ${error instanceof Error ? error.message : error}`);
    }
  }

  public async MoveToUnCategory (
    tour_id: string,
    subgroup_id: string,
  ): Promise<VirtualTour | null> {
    try {
      if (!tour_id) {
        throw new Error('Invalid Tour ID');
      }
      if (!subgroup_id) {
        throw new Error('Invalid SubGroup ID');
      }

      const tour = await this.model.findOne({ _id: tour_id });
      if (!tour) {
        throw new Error('Tour not found');
      }

      // Find the length of uncategory images
      let totalUnCategoryImages: number = Object.keys(tour.images || {}).filter(
        (item) => (!('groupId' in tour.images[item]) || tour.images[item].groupId === '') && (!('subGroupId' in tour.images[item]) || tour.images[item].subGroupId === ''),
      ).length;

      for (const key in tour.images) {
        const img = tour.images[key];
        if (img.subGroupId === subgroup_id) {
          totalUnCategoryImages++; // Increament
          img.groupId = '';
          img.subGroupId = '';
          img.order = totalUnCategoryImages;
        }
      }

      // Peform the update
      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: { images: tour.images } },
        { new: true},
      );

      if (!updatedTour) {
        throw new Error('Tour not found');
      }

      logger.info('Moved Image to UnCategory successfully', {
        tour_id,
        subgroup_id,
      });

      return updatedTour;
    } catch (error) {
      logger.error('Error in MoveToCategory method', { message: error });
      throw new Error(`Failed to Move the images to UnCategory: ${error}`);
    }
  }

  public async AddTourLabel (
    tour_id: string,
    addLabelPayload: labels,
  ): Promise<VirtualTour | void> {
    logger.info('AddTourLabel called', { tour_id, addLabelPayload });

    try {
      const tour = await this.model.findById(tour_id);
      if (!tour) {
        logger.error('Tour not found', { tour_id });
        throw new Error('Tour not found');
      }
      const label_id = new mongoose.Types.ObjectId();

      const updateData = {
        [`labels.${label_id}.name`]: addLabelPayload.name,
        [`labels.${label_id}.camera_name`]: addLabelPayload.camera_name,
        [`labels.${label_id}.camera_position`]: addLabelPayload.camera_position,
        [`labels.${label_id}.controls_target`]: addLabelPayload.controls_target,
        [`labels.${label_id}.order`]: addLabelPayload.order,
      };

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $set: updateData },
        { new: true },
      );

      if (!updatedTour) {
        throw new Error('Failed to add label');
      }

      logger.info('Label added successfully in the tour', { updatedTour });
      return updatedTour;
    } catch (error) {
      logger.error('Error adding label in tour', { message: error });
      throw new Error(`Failed to add label: ${error}`);
    }
  }
  public async updateBulkTourLabel (
    tour_id: string,
    addLabelPayload: { query: labels[]  },
  ): Promise<string> {
    logger.info('updateBulkTourLabel Called', {payload: addLabelPayload});
    return new Promise<string>((resolve, reject) => {
      this.model.findById(tour_id).then((tour) => {
        if (!tour) {
          logger.error('Tour not found', { tour_id });
          reject('Tour not found');
        }
      });
      const promises: Promise<VirtualTour | null>[] = addLabelPayload.query.map(async (payload) => {
        const updateData = {
          [`labels.${payload.label_id}.name`]: payload.name,
          [`labels.${payload.label_id}.camera_name`]: payload.camera_name,
          [`labels.${payload.label_id}.camera_position`]: payload.camera_position,
          [`labels.${payload.label_id}.controls_target`]: payload.controls_target,
          [`labels.${payload.label_id}.order`]: payload.order,
        };
        return this.model.findOneAndUpdate(
          { _id: tour_id },
          { $set: updateData },
          { new: true },
        )
          .then((res) => {
            return res;
          })
          .catch(() => {
            return null;
          });
      });

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('updateBulkTourLabel successfully', {results: results});
            resolve('Documents updated successfully');
          } else {
            logger.error('Error while updating sidebar option');
            reject('Error while updating sidebar option');
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  public async DeleteLabel (
    tour_id: string,
    label_id: string,
  ): Promise<VirtualTour | null> {
    logger.info('DeleteLabel Called', { tour_id, label_id });
    try {
      const tour = await this.model.findById(tour_id);
      if (!tour) {
        logger.error('Tour not found', { tour_id });
        throw new Error('Tour not found');
      }

      const updatedTour = await this.model.findOneAndUpdate(
        { _id: tour_id },
        { $unset: { [`labels.${label_id}`]: '' } },
        { new: true },
      );

      if (!updatedTour) {
        logger.error('Tour Label not found', { tour_id, label_id });
        throw new Error('Tour Label not found');
      }

      logger.info('Label deleted successfully', { tour_id, label_id });
      return updatedTour;
    } catch (error) {
      logger.error('Error in DeleteLabel method', { error });
      throw new Error(`Failed to delete label: ${error instanceof Error ? error.message : error}`);
    }
  }
}

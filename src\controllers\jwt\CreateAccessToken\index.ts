import { JWTModule } from '../../../modules/jwt';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';

export async function accessToken (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const refreshToken = request.body.token;
    const organization_id = request.body.organization_id;

    console.log('**', refreshToken);
    console.log('**', organization_id);

    const JWT = new JWTModule();

    const result = await JWT.CreateAccessToken(refreshToken, organization_id);

    console.log('accessToken', result, typeof result);

    response.status(200).send({
      status: 1,
      accessToken: result,
    });
  } catch (err) {
    response.status(400).send({
      status: 0,
      message: (err instanceof Error) ? err.message : 'An unknown error occurred',
    });
  }
}

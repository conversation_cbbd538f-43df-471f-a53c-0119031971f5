import mongoose from 'mongoose';
import logger from '../../config/logger';
import { AddHotspotData, MiniMap, MiniMapData, MiniMapMedia } from '../../types/miniMap';
import { amenityMiniMapSchema } from '../../schema/miniMapSchema';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';
import { Models } from '../../types/extras';

export class MiniMapModule {
  private model: mongoose.Model<MiniMap>;
  public storagepath;
  constructor (project_id: string, organization_id: string) {
    this.model = mongoose.model<MiniMap>(
      `${project_id}${Models._MINIMAP}`,
      amenityMiniMapSchema,
    );
    this.storagepath='CreationtoolAssets/'+organization_id+'/projects/'+project_id+'/minimaps/';
  }

  public async CreateMiniMap (createMiniMapData: MiniMap):Promise<MiniMap | void> {
    logger.info('CreateMiniMap called', { createMiniMapData: createMiniMapData });
    try {
      const newMinimap: MiniMap = {
        _id: createMiniMapData._id,
        name: createMiniMapData.name,
        low_res: createMiniMapData.low_res,
        high_res: createMiniMapData.high_res,
        referenceId: createMiniMapData.referenceId,
        type: createMiniMapData.type,
      };
      logger.info('CreateMiniMap Successfull', { newMinimap: newMinimap });
      const minimaps = new this.model(newMinimap);
      const minimap = await minimaps.save();
      logger.info('CreateMiniMap Saved', { newMinimap: newMinimap });
      return minimap;
    } catch (error) {
      throw new Error('Unable to create minimap for this project'+error);
    }
  }

  public async AddHotspot (addHotspot: AddHotspotData, minimap_id: string)
    :Promise<MiniMap | void> {
    logger.info('AddHotspot called', { addHotspot: addHotspot });
    try {
      const addHotspotData = {
        hotspot_id: addHotspot.hotspot_id,
        text: addHotspot.text,
        x: addHotspot.x,
        y: addHotspot.y,
        destination: addHotspot.destination,
      };
      const minimap = await this.model.findOneAndUpdate(
        { _id: minimap_id },
        {
          $set: {
            [`hotspots.${addHotspot.hotspot_id}`]: addHotspotData,
          },
        },
        { new: true, upsert: true },
      );

      if (!minimap) {
        throw new Error(`MiniMap with ID ${minimap_id} not found.`);
      }

      logger.info('AddHotspot Successful', { minimap: minimap });

      return minimap as MiniMap;
    } catch (error) {
      throw new Error('Unable to add hotspot for this project'+error);
    }
  }

  public async GetListOfMiniMap (): Promise<object> {
    logger.info('GetListOfMiniMap Called');
    const minimap:Array<UnknownObject> = await this.model.find();
    const minimapObj = arrayToObject(minimap) as Record<string, MiniMap>;
    logger.info('GetListOfMiniMap Successfull', {minimapObj: minimapObj});
    return minimapObj;
  }

  public async GetHotspots (minimap_id: string): Promise<AddHotspotData[] | null> {
    logger.info('GetHotspots Called', {minimap_id: minimap_id});
    const query = {
      _id: minimap_id,
    };
    const minimap = await this.model.findOne(query, { hotspots: 1, _id: 0 });

    if (!minimap || !minimap.hotspots) {
      logger.info('No hotspots found', { minimap: minimap });
      return null;
    }
    const hotspotArray = Object.values(minimap.hotspots);

    logger.info('GetHotspots Successful', { hotspots: hotspotArray });
    return hotspotArray;
  }

  public async GetMiniMap (minimap_id: string): Promise<MiniMap | null> {
    logger.info('GetMiniMap Called', {minimap_id: minimap_id});
    const query = {
      _id: minimap_id,
    };
    const minimap = await this.model.findOne(query);

    if (!minimap) {
      logger.info('No mini map found', { minimap: minimap });
      return null;
    }
    logger.info('GetMiniMap Successful', { minimap: minimap });
    return minimap as MiniMap;
  }

  public async DeleteMiniMap (minimap_id: string): Promise<MiniMap | null> {
    logger.info('DeleteMiniMap called', {minimap_id: minimap_id});
    try {
      const deletedMiniMap: unknown = await this.model.findOneAndDelete({_id: minimap_id});

      logger.info('DeleteMiniMap Succesfull', { DeleteMiniMap: deletedMiniMap });
      return deletedMiniMap as MiniMap;
    } catch (error) {
      logger.error('Cannot delete mini map', {message: error});
      throw error;
    }
  }

  public async DeleteHotSpot (minimap_id: string, hotspot_id: string): Promise<MiniMap | void> {
    logger.info('DeleteHotSpot called', {minimap_id: minimap_id});

    try {
      const miniMap = await this.model.findOne(
        { _id: minimap_id },
      );
      if (!miniMap) {
        logger.error('Minimap not found', { minimap_id });
        throw new Error(`Minimap with ID ${minimap_id} not found`);
      }
      if (!miniMap.hotspots || !miniMap.hotspots[hotspot_id]) {
        logger.error('Hotspot not found in the minimap', { hotspot_id });
        throw new Error(`Hotspot with ID ${hotspot_id} not found in minimap`);
      }

      const updatedMiniMap = await this.model.findOneAndUpdate(
        { _id: minimap_id },
        { $unset: { [`hotspots.${hotspot_id}`]: '' } },
        { new: true },
      );
      if (!updatedMiniMap) {
        logger.error('Failed to update minimap after deleting hotspot');
        throw new Error('Failed to update minimap after deleting hotspot');
      }

      logger.info('Hotspot deleted successfully', { updatedMiniMap });
      return updatedMiniMap;

    } catch (error) {
      logger.error('Cannot delete hotspot', {message: error});
      throw error;
    }
  }

  public async UpdateMiniMap (minimap_id: string, hotspot_id: string, updateMiniMap: MiniMapData): Promise<MiniMap | void> {
    logger.info('updateMiniMap called', { minimap_id: minimap_id, hotspot_id: hotspot_id, updateMiniMap: updateMiniMap});
    try {
      const updatedMiniMap = await this.model.findOneAndUpdate(
        { _id: minimap_id},
        {
          $set: {
            name: updateMiniMap.name,
            referenceId: updateMiniMap.referenceId,
            type: updateMiniMap.type,
            [`hotspots.${hotspot_id}.text`]: updateMiniMap.hotspots?.text,
            [`hotspots.${hotspot_id}.x`]: updateMiniMap.hotspots?.x,
            [`hotspots.${hotspot_id}.y`]: updateMiniMap.hotspots?.y,
            [`hotspots.${hotspot_id}.destination`]: updateMiniMap.hotspots?.destination,
          },
        },
        { new: true },
      );
      if (updatedMiniMap) {
        logger.info('UpdateAmenity Succesfull', { updatedMiniMap: updatedMiniMap });
        return updatedMiniMap;
      }
      logger.error('Error in updating amenity');
      throw new Error('Error in updating amenity');
    } catch (error) {
      logger.error('Internal Server Error', {message: error});
      throw new Error('Internal Server Error'+error);
    }
  }

  public async UpdateMiniMapMedia (minimap_id: string, updateMiniMap: MiniMapMedia): Promise<MiniMap | void> {
    logger.info('updateMiniMapMedia called', { minimap_id: minimap_id, updateMiniMap: updateMiniMap});
    try {
      if (!updateMiniMap) {
        throw new Error('Missing low_res or high_res in updateMiniMapMedia');
      }
      const updatedMiniMapMedia = await this.model.findOneAndUpdate(
        { _id: minimap_id },
        {
          $set: {
            low_res: updateMiniMap.low_res,
            high_res: updateMiniMap.high_res,
          },
        },
        { new: true },
      );
      if (!updatedMiniMapMedia) {
        logger.error('Unable to find minimap with the given ID');
        throw new Error(`Minimap with ID ${minimap_id} not found`);
      }

      logger.info('Successfully updated minimap media', { updatedMiniMapMedia });
      return updatedMiniMapMedia;
    } catch (error) {
      logger.error('Internal Server Error', {message: error});
      throw new Error('Internal Server Error'+error);
    }
  }
}

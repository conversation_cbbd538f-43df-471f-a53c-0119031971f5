import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import uploadHandler from '../helpers/uploadHandler';
import { CreateModel } from '../controllers/glbModel/createModel';
import { createTexture } from '../controllers/glbModel/createTexture';
import { getModels } from '../controllers/glbModel/getModels';
import getModelsValidate from '../controllers/glbModel/getModels/getModelsValidator';
import createTextureValidate from '../controllers/glbModel/createTexture/createTextureValidator';
import createModelValidate from '../controllers/glbModel/createModel/createModelValidator';
import { updateModel } from '../controllers/glbModel/updateModel';
import updateModelValidate from '../controllers/glbModel/updateModel/updateModelValidator';
import { updateTexture } from '../controllers/glbModel/updateTexture';
import updateTextureValidate from '../controllers/glbModel/updateTexture/updateTextureValidator';
import { CreateLuma } from '../controllers/glbModel/createLuma';
import { GetLuma } from '../controllers/glbModel/getLuma';
import { UpdateLuma } from '../controllers/glbModel/updateLuma';
import { getLumaCaptures } from '../controllers/glbModel/getLumaCaptures';
import { GenerateSignedUrl } from '../controllers/glbModel/generateSignedUrl';
import { CreateVideo } from '../controllers/glbModel/createVideo';
import { OptimizeBlender } from '../controllers/glbModel/optimizeBlender';

const router = express.Router();
router.post(
  '/createModel',
  authMiddleware,
  organizationAccessMiddleware,
  createModelValidate,
  uploadHandler([{ name: 'thumbnail' }, { name: 'file' }], 'output'),
  CreateModel,
);
router.post(
  '/updateModel',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'thumbnail' }, { name: 'file' }], 'output'),
  updateModelValidate,
  updateModel,
);
router.post(
  '/createTexture',
  authMiddleware,
  organizationAccessMiddleware,
  createTextureValidate,
  uploadHandler(
    [{ name: 'texture_thumbnail' }, { name: 'actual_texture_thumbnail' }],
    'output',
  ),
  createTexture,
);
router.post(
  '/updateTexture',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler(
    [{ name: 'texture_thumbnail' }, { name: 'actual_texture_thumbnail' }],
    'output',
  ),
  updateTextureValidate,
  updateTexture,
);
router.get(
  '/getModels/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  getModelsValidate,
  getModels,
);
router.post(
  '/luma/create',
  CreateLuma,
);
router.post('/luma/getLuma', GetLuma);
router.post('/luma/generateurl', GenerateSignedUrl);
router.post(
  '/luma/updateLuma',
  uploadHandler([{ name: 'file' }], 'output'),
  UpdateLuma,
);
router.post('/luma/createvideo', CreateVideo);
router.post('/luma/optblend', OptimizeBlender);
router.post('/luma/GetLumaCaptures', getLumaCaptures);

export default router;

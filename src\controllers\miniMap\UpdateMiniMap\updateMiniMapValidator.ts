import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';
import { TypeEnum } from '../../../types/miniMap';

const UpdateMiniMapValidator = [
  body('minimap_id', 'minimap_id is required').notEmpty(),
  body('project_id', 'project_id is required').notEmpty(),
  body('type')
    .optional()
    .custom((value) => Object.values(TypeEnum).includes(value))
    .withMessage(`Type must be one of: ${Object.values(TypeEnum).join(', ')}`),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    const allowedFields = ['name', 'type', 'referenceId', 'hotspots'];
    const hasAdditionalFields = allowedFields.some((field) => req.body[field]);
    if (!hasAdditionalFields) {
      res.status(400).json({
        error: 'At least one of the fields (name, hotspots, type, referenceId) must be provided for updating.',
      });
      return;
    }
    next();
  },
];

export default UpdateMiniMapValidator;

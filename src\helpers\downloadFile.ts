
import fs from 'fs';
import axios from 'axios';

interface chunk{
    length:number
}

export async function downloadFile (fileUrl:string, outputLocationPath:string):Promise<string> {
  const writer = fs.createWriteStream(outputLocationPath);

  try {
    // Get the content length from the headers
    const headResponse = await axios.head(fileUrl);
    const totalLength = parseInt(headResponse.headers['content-length'], 10);

    const response = await axios({
      url: fileUrl,
      method: 'GET',
      responseType: 'stream',
    });

    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      let downloadedLength = 0;

      response.data.on('data', (chunk:chunk) => {
        downloadedLength += chunk.length;
      });

      writer.on('finish', () => {
        if (downloadedLength === totalLength) {
          console.log(outputLocationPath);
          resolve(outputLocationPath);
        } else {
          reject(new Error('Downloaded file size does not match expected size.'));
        }
      });

      writer.on('error', reject);
    });
  } catch (error) {
    console.error(`Error downloading the file: ${error}`);
    throw error; // Rethrow the error to ensure the promise is rejected
  }
}

import { ExtendedRequest } from './../../../types/extras';
import logger from '../../../config/logger';
import { Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';

export async function UpdateTourImageAPI (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const { project_id, tour_id, image_id, organization_id } = request.body;
    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updateFields = { ...request.body };
    const tourData = await tourModule.UpdateTourImage(tour_id, image_id, updateFields);
    response.status(200).json({ status: 1, data: tourData });
  } catch (error) {
    logger.error('Error Updating tour Image', { message: error });
    response.status(404).json({ status: 0, error: 'Error Updating tour Image' + error });
  }
}

import { v2beta3 } from '@google-cloud/tasks';
export class TaskModule {
  private client: v2beta3.CloudTasksClient;
  private project: string;
  private location: string;
  constructor () {
    this.client = new v2beta3.CloudTasksClient({
      credentials: {
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
        private_key: (process.env.FIREBASE_PRIVATE_KEY || '').replace(/\\n/g, '\n'),
      },
    });
    this.project = process.env.FIREBASE_PROJECT_ID || '';
    this.location = 'us-central1';
  }
  public async createTask (queueName: string, url: string,
    payload: object, time: string, taskId: string, customheaders: object):Promise<void> {
    console.log(url, payload, time, taskId);
    const serviceAccountEmail = process.env.FIREBASE_CLIENT_EMAIL;
    if (!this.project || !serviceAccountEmail) {
      throw new Error('Missing required environment variables');
    }

    const formattedParent = this.client.queuePath(this.project, this.location, queueName);
    const taskName = this.client.taskPath(this.project, this.location, queueName, taskId);
    console.log(taskName);
    // Default headers
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };
    const customHeaders = customheaders || {};
    // Merge default headers with custom headers
    const headers = { ...defaultHeaders, ...customHeaders };
    const task = {
      name: taskName,
      httpRequest: {
        httpMethod: 'POST',
        url: url,
        body: Buffer.from(JSON.stringify(payload)).toString('base64'),
        headers: headers,
        oidcToken: {
          serviceAccountEmail,
        },
      },
      scheduleTime: {
        seconds: new Date(time).getTime() / 1000,
      },
    };
    console.log('Sending task:');
    console.log(task);
    interface taskReq {
      parent: string,
      task: object
    }
    const request:taskReq = {
      parent: formattedParent,
      task: task,
    };

    try {
      const response = await this.client.createTask(request);
      console.log(response);
    // Console.log(`Created task ${response.name}`);
    } catch (error) {
      console.error(`Error creating task: ${error}`);
    }
  }
  public async cancelTask (queueName: string, taskId?: string): Promise<void> {
    if (!taskId) {
      console.error('Task ID is required');
      return;
    }

    try {
      const taskName = this.client.taskPath(this.project, this.location, queueName, taskId);
      await this.client.deleteTask({ name: taskName });
      console.log(`Task ${taskName} deleted successfully.`);
    } catch (error) {
      console.error(`Error deleting task: ${error}`);
    }
  }
  public async checkIfTaskExists (queueName: string, taskName: string): Promise<boolean> {
    const taskPath = this.client.taskPath(this.project, this.location, queueName, taskName);
    try {
      await this.client.getTask({ name: taskPath });
      return true;
    } catch (error: any) {
      if (error.code === 5) { // 'NOT_FOUND' error
        return false;
      }
      throw error;
    }
  }
}

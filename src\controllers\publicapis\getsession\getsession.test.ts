import app  from '../../../app';
import request from 'supertest';
describe('Get List of Project Landmarks Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/getSession?sessionId=6572e85347683ae7724cc2d0')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual({});
  });

//   It('should return an empty data object with no data', async () => {
//     Const res = await request(app)
//       .get('/publicapis/getSession?sessionId=6572e85347683ae7724cc2')   //invalid session Id
//       .send();
//     Expect(res.statusCode).toEqual(200);
//     Expect(res.body).toHaveProperty('data');
//     Expect(res.body.data).toEqual({});
//   });
});

import mongoose from 'mongoose';
import { placementSVG, projectSVGType } from '../../types/projectSVG';

export const layersSchema = new mongoose.Schema({
  layer_id: String,
  building_id: String,
  scene_id: String,
  g: String,
  x: Number,
  y: Number,
  placement: {
    type: String,
    enum: placementSVG,
  },
  reSize: Boolean,
  type: {
    type: String,
    enum: projectSVGType,
    default: projectSVGType.LANDMARK,
  },
  zIndex: Number,
  maxZoomLevel: Number,
  video_tag: String,
  showLabel: {
    type: Boolean,
    default: false,
  },
  group_name: String,
  bedrooms: String,
});

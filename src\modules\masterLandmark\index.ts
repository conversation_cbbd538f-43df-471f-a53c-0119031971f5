import logger from '../../config/logger';
import {masterLandmarksSchema} from '../../schema/masterLandmarksSchema';
import { Models } from '../../types/extras';
import {masterLandmark} from '../../types/masterLandmark';
import mongoose from 'mongoose';
// Import {storageUpload} from '../../helpers/storageUpload';
export class masterLandmarkModule{
  private model: mongoose.Model<masterLandmark>;
  public storagepath;
  constructor (organization_id:string) {
    this.model=mongoose.model<masterLandmark>(`${organization_id}${Models._LANDMARKS}`, masterLandmarksSchema);
    this.storagepath='CreationtoolAssets/'+organization_id+'/master_landmarks/';
  }
  // Public async UploadFiles (file:Express.Multer.File):Promise<string> {
  //   Return new Promise((resolve, reject) => {
  //     Const uploadOptions = {
  //       Destination:
  //               This.storagepath
  //               +file.originalname,
  //     };
  //     StorageUpload(uploadOptions, file.path).then((thumbnailUrl) => {
  //       Resolve(thumbnailUrl);
  //     })
  //       .catch((err) => {
  //         Reject(err);
  //       });
  //   });
  // }
  public async createLandmark (payload:object):Promise<masterLandmark|string>{
    logger.info('createLandmark Called', {payload: payload});
    return new Promise((resolve, reject) => {
      const landmark = new this.model(payload);
      landmark.save().then((res) => {
        logger.info('createLandmark Successfully', {response: res});
        resolve(res);
      }).catch((err:string) => {
        logger.error('Error in createLandmark', {message: err});
        reject(err);
      });
    });
  }

  public async getListofLandmark (): Promise<{[key: string]: masterLandmark}|null> {
    logger.info('getListofLandmark Called');
    const masterLandmarks = await this.model.find();
    const transformedResult: {[key: string]: masterLandmark} = {};
    for (const landmark of masterLandmarks) {
      transformedResult[landmark._id] = landmark;
    }
    logger.info('getListofLandmark Successfully', {transformedResult: transformedResult});
    return transformedResult;
  }
}

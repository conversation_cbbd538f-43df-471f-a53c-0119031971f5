import { Types } from 'mongoose';
export type position = {
    x: number,
    y: number,
    z: number
}
export type hotspot = {
    _id: Types.ObjectId,
    position: position,
    destination: string
}
export type customTourImage = {
    _id:Types.ObjectId,
    name:string,
    rotation?:object,
    thumbnail:string,
    url:string,
    group_id?:string,
    group_name?:string,
    hotspots?:Array<hotspot>
}
export type createCustomTourImage = {
    id:Types.ObjectId,
    name:string,
    thumbnail:string,
    url:string
}
type rotation={
    x:number,
    y:number,
    z:number
}
export type updateCustomTourImage = {
    image_id:string,
    name?:string,
    rotation?:rotation,
    group_id?:string,
    group_name?:string,
    thumbnail?:string,
    url:string
}

export type updateHotspot = {
    position?: position,
    destination?: string
}

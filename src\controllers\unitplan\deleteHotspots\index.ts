import { Request, Response } from 'express';
import { unitplanModule } from '../../../modules/unitplan';
import logger from '../../../config/logger';

export default async function DeleteHotspots (
  request: Request,
  response: Response,
): Promise<void> {
  const { project_id, unitplan_id, hotspot_id } = request.body;
  const organization_id = request.headers.organization as string;

  const unitplan = new unitplanModule(project_id, organization_id);
  unitplan.deleteHotspots(unitplan_id, hotspot_id).then((res) => {
    response.status(200).json({status: 1, data: res});
  }).catch((error: Error) => {
    logger.error('Error in delete hotspot', {message: error});
    response.status(500).json({status: 0, error: ''+ error});
  });
}

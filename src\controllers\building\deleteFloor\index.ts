import {Response } from 'express';
import { buildingModule } from '../../../modules/building';
import logger from '../../../config/logger';
import { ExtendedRequest } from '../../../types/extras';

export async function DeleteFloor (
  request:ExtendedRequest,
  response:Response,
):Promise<void>{
  // Const {building_id,floor_id,project_id} = request.body;
  const building_id = request.body.building_id;
  console.log('building_id', building_id);

  const floor_id = request.body.floor_id;
  const project_id= request.body.project_id;

  const organization_id = request.headers.organization as string;
  const building = new buildingModule(project_id, organization_id);
  building.DeleteFloor(building_id, floor_id).then((res) => {
    response.send({status: 1, data: res});
  })
    .catch((err) => {
      logger.error('Error', {message: err});
      response.send({status: 0, message: err});
    });
}

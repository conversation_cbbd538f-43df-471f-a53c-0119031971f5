import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { TranslationModule } from '../../../modules/translation';
import logger from '../../../config/logger';
export async function GetAllTranslation (request: ExtendedRequest, response: Response):Promise<void>{
  logger.info('GetAllTranslation Controller Called');
  const organization_id = request.organization_id as string;
  const translations = new TranslationModule(organization_id);
  try {
    const translatedDocuments = await translations.GetAllTranslation();
    response.status(200).send({ status: 1, data: translatedDocuments });
  } catch (error) {
    logger.error('Error getting translated documents');
    response.status(500).json({ message: 'Error getting translated documents', error: error });
  }
}

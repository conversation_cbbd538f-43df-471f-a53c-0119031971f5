import { ScaleSetModule } from '../../../modules/pixelstreaming';
import { SessionModule } from '../../../modules/sessions';
import { ProjectModule } from './../../../modules/projects/index';
import { Request, Response } from 'express';

export async function handleScaleDown (request: Request, response: Response): Promise<void> {
  const organization_id = request.headers.organization as string;
  const { project_id } = request.body;

  if (!organization_id || !project_id) {
    response.status(400).send({ status: 0, error: 'Missing organization or project_id' });
    return;
  }

  const projectModule = new ProjectModule(organization_id);
  const sessionModule = new SessionModule();

  try {
    const project = await projectModule.getProjectById(project_id);
    if (!project) {
      response.status(404).send({ status: 0, error: 'Project not found' });
      return;
    }

    const { resourceGroupName, vmScaleSetName, minInstanceCount } = await projectModule.getVMDetails(project_id) || {};
    if (!resourceGroupName || !vmScaleSetName || minInstanceCount === undefined) {
      response.status(400).send({ status: 0, error: 'Invalid VM details or min instance count missing' });
      return;
    }

    const vm_accesstoken = await new ScaleSetModule(resourceGroupName, vmScaleSetName).getAccessToken();
    if (!vm_accesstoken) {
      response.status(400).send({ status: 0, error: 'Unable to get VM Access Token' });
      return;
    }
    let currentCapacity;
    try {
      currentCapacity = await new ScaleSetModule(resourceGroupName, vmScaleSetName)
        .getCurrentCapacity(vm_accesstoken);
      // Use currentCapacity as needed
      console.log('Current capacity:', currentCapacity);
    } catch (error) {
      response.status(400).send({ status: 0, error: 'Unable to get VM current capacity' });
      return;
    }
    if (currentCapacity === null || currentCapacity === undefined) {
      response.status(400).send({ status: 0, error: 'Unable to get VM current capacity' });
      return;
    }

    // Define time windows and availability check
    const now = new Date();
    const next30Minutes = new Date(now.getTime() + (30 * 60000));
    const next60Minutes = new Date(next30Minutes.getTime() + (30 * 60000));

    const availabilityConfig = [
      { start_time: now, end_time: next30Minutes },
      { start_time: next30Minutes, end_time: next60Minutes },
    ].map(({ start_time, end_time }) => ({
      organization_id, project_id, start_time, end_time, maxConcurrentSessions:
      project.projectSettings?.pixelstreaming?.max_concurrent_sessions || 10,
    }));

    // Fetch active sessions in parallel
    const [activeNow, activeNext] = await Promise.all(
      availabilityConfig.map((config) => sessionModule.checkAvailability(config)),
    );

    const maxActiveSessions = Math.max(activeNow.activeSessions, activeNext.activeSessions);
    const requiredCapacity = Math.max(maxActiveSessions, minInstanceCount);
    // Add getRunningVMsWithNoConnectedDevices logic if required
    // Scale down logic
    if (currentCapacity > requiredCapacity) {
      const newCapacity = maxActiveSessions === 0 ?
        minInstanceCount : Math.max(minInstanceCount, currentCapacity - (currentCapacity - maxActiveSessions));
      await new ScaleSetModule(resourceGroupName, vmScaleSetName).updateVmScaleSetCapacity(vm_accesstoken, newCapacity);
      response.status(200).send({ status: 1, message: `Scaled down to ${newCapacity} instances` });
      return;
    }

    response.status(200).send({ status: 1,
      message: 'Minimum instance count already reached. No scaling down required' });
  } catch (error) {
    console.error('Error during scaling operation:', error);
    response.status(500).send({ status: 0, error: 'Internal server error' });
  }
}

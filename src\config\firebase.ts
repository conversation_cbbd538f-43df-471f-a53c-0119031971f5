import firebaseAdmin from 'firebase-admin';
// Import serviceAccount from './firebase-config.json';

import { initializeApp } from 'firebase/app';
const serviceAccount = {
  apiKey: process.env.FIREBASE_API_KEY,
  authDomain: process.env.FIREBASE_AUTH_DOMAIN,
  databaseURL: process.env.FIREBASE_DATABASE_URL,
  project_id: process.env.FIREBASE_PROJECT_ID,
  projectId: process.env.FIREBASE_PROJECT_ID,
  appId: process.env.FIREBASE_APP_ID,
  private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  client_email: process.env.FIREBASE_CLIENT_EMAIL,
};

export const admin = firebaseAdmin.initializeApp({
  credential: firebaseAdmin.credential.cert(serviceAccount),
  databaseURL: serviceAccount.databaseURL,
});

export const bucketName = process.env.FIREBASE_STORAGE_BUCKET;
export const client = initializeApp(serviceAccount);

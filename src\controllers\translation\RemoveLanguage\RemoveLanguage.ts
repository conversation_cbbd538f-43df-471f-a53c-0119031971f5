import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { TranslationModule } from '../../../modules/translation';
import { SupportedLanguages } from '../../../types/translation';
import logger from '../../../config/logger';
export async function RemoveLanguage (request: ExtendedRequest, response: Response): Promise<void> {
  const { targetLanguageCode } = request.query;
  const organization_id = request.organization_id as string;
  const translations = new TranslationModule(organization_id);
  logger.info('RemoveLanguage Controller Called', {targetLanguageCode});

  try {
    await translations.RemoveLanguage(targetLanguageCode as SupportedLanguages);
    response.status(200).send({ status: 1,
      data: `Language '${targetLanguageCode}' removed successfully from all documents.` });
  } catch (error) {
    logger.error('Error removing language', error);
    response.status(500).json({ message: 'Error removing language', error: error });
  }
}

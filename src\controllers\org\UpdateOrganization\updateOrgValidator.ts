import { Request, Response, NextFunction } from 'express';
import { validationResult, header, check } from 'express-validator';
import { FontType } from '../../../types/projects';

interface UploadedFiles {
  font_url: Express.Multer.File[];
}

const updateOrgValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {

    const files = req.files as UploadedFiles | undefined;
    if (req.body.font_type === 'custom' && (!files || !files.font_url)) {
      res.status(400).json({ error: 'Font file is required for font type custom.' });
      return;
    }
    check('theme')
      .optional()
      .custom((value) => {
        if (value === 'custom') {
          const { primary, secondary, primary_text, secondary_text } = req.body;
          const presentFields = [primary, secondary, primary_text, secondary_text]
            .filter((field) => field !== undefined);
          if (presentFields.length < 1) {
            throw new Error(
              'At least one of primary, secondary, primary_text, or secondary_text must be provided for custom theme',
            );
          }
        }
        return true;
      })
      .run(req),
    check('font_type', 'Font type is Invalid. Please make sure to pass a valid font_type value')
      .optional()
      .isIn(Object.values(FontType))
      .run(req);
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default updateOrgValidate;

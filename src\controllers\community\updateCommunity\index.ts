import { Response } from 'express';
import { community } from '../../../types/community';
import { communityModule } from '../../../modules/community';
import mongoose from 'mongoose';
import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { FileRequest } from '../../../types/extras';

export async function updateCommunity (
  request: FileRequest,
  response: Response,
): Promise<community | void> {
  const { community_id, name, project_id, category } = request.body;
  const organization_id = request.organization_id as string;

  if (!organization_id) {
    response.status(401).json({ status: 0, error: 'Not authorized' });
    return;
  }

  if (!community_id || !mongoose.Types.ObjectId.isValid(community_id)) {
    response.status(400).json({ status: 0, error: 'Invalid community ID' });
    return;
  }

  const communityMod = new communityModule(project_id, organization_id);
  const requestFiles = request.files;

  try {
    const existingCommunity = await communityMod.getCommunityId(community_id);

    if (!existingCommunity) {
      response.status(404).json({ status: 0, error: 'Community not found' });
      return;
    }

    const updatedData: Partial<community> = {
      name: name || existingCommunity.name,
      project_id: project_id || existingCommunity.project_id,
      category: category || existingCommunity.category,
      updated_at: new Date().toISOString(),
    };

    if (requestFiles) {
      const urlObject = await UploadUnitplanFiles(requestFiles, communityMod.storagepath + community_id);
      updatedData.thumbnail = urlObject.thumbnail || existingCommunity.thumbnail;
    }

    const updatedCommunity = await communityMod.UpdateCommunity(community_id, updatedData);

    response.status(200).json({ status: 1, data: updatedCommunity });
  } catch (error) {
    logger.error('Internal Server Error', { message: error });
    response.status(500).json({ status: 0, error: 'Error while updating the community' });
    console.error(error);
  }
}

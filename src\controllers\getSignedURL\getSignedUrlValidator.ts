import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';

const getSignedUrlValidate = [
  body('path', 'path is required').notEmpty().isString(),
  body('actions', 'actions is required and must be a string')
    .notEmpty()
    .isString(),

  body('expires', 'expires is required and must be a string')
    .notEmpty()
    .isString(),

  body('contentType', 'contentType is required and must be a string')
    .notEmpty()
    .isString(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array()[0] });
      return;
    }
    next();
  },
];

export default getSignedUrlValidate;

import mongoose from 'mongoose';
import { projectSVGType } from '../../types/projectSVG';
import { layersSchema } from '../layersSchema';
export const projectsvgSchema = new mongoose.Schema({
  _id: String,
  scene_id: String,
  svg_url: String,
  building_id: String,
  layers: { type: Object, of: layersSchema },
  type: {
    type: String,
    enum: projectSVGType,
    default: projectSVGType.LANDMARK,
  },
  name: String,
  rotation: {
    x: Number,
    y: Number,
    z: Number,
  },
  scale: {
    width: Number,
    height: Number,
  },
  position: {
    x: Number,
    y: Number,
    z: Number,
  },
  order: {
    immutable: false,
    type: Number,
  },
  viewbox: {
    width: Number,
    height: Number,
  },
});

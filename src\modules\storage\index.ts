import { admin, bucketName } from '../../config/firebase';
import logger from '../../config/logger';
import { optionsType, signedUrlType } from '../../types/signedURLs';

export class StorageModule {
  public async getSignedUrl (
    path: string,
    options: optionsType,
  ): Promise<signedUrlType> {
    logger.info('getSignedUrl Called', {path: path, options: options});
    const [url] = await admin
      .storage()
      .bucket(bucketName)
      .file(path)
      .getSignedUrl(options);
    const thumbnailUrl =
      'https://storage.googleapis.com/propvr-in-31420.appspot.com/' +
      encodeURIComponent(path);

    const links: signedUrlType = {
      url: url,
      thumbnail_url: thumbnailUrl,
    };
    logger.info('getSignedUrl Successful', {links: links});
    return links;
  }
}

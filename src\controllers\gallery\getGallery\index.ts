import {  Request, Response } from 'express';
import { GalleryModule } from '../../../modules/gallery';
import logger from '../../../config/logger';

export async function getGallery (request: Request, response: Response):Promise<void> {
  const project_id = request.query.project_id as string;
  const organization_id = request.headers.organization as string;
  const category = request.query.category as string;
  const search = request.query.search as string;
  console.log(project_id, organization_id);

  if (!organization_id){
    logger
      .error('no organization is found');
    response.send({status: 0, error: 'no organization is found'});
    return ;
  }

  const gallerymod = new GalleryModule(project_id, organization_id);
  const gallery = await gallerymod.GetGallery(category, search);
  if (gallery) {
    response.send({ status: 1, data: gallery });
  } else {
    response.send({ status: 1, data: [] });
  }
  return ;
}

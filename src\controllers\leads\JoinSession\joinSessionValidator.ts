import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const joinSessionValidate = [
  body('session_id', 'Session ID is required').notEmpty(),
  body('lead_id', 'Lead ID is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default joinSessionValidate;

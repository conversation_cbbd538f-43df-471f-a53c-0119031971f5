import app  from '../../../app';
import request from 'supertest';
describe('Get List of Unit plan Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/getListOfUnitplan/65701718b5063acb09dbf6e4?organization_id=8X3plU')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual({});
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getListOfUnitplan/65701718b5063acb09dbf6?organization_id=8X3plU')   // Invalid project Id
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).toEqual({});
  });
});

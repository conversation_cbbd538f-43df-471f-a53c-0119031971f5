
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';
import { EnvVar } from '../../../types/kafd';

export async function GenerateTile (
  req: ExtendedRequest,
  res: Response,
): Promise<void> {
  try {
    const { storagePath, project_id, tour_id, imageurl } = req.body;

    const envVars : EnvVar[]  = [
      { name: 'project_id', value: project_id },
      { name: 'tour_id', value: tour_id },
      { name: 'storagepath', value: storagePath },
      { name: 'url', value: imageurl },
    ];
    const jobId = 'kafd-tile-generator';
    const runJob = new cloudRunJobModule;
    runJob.runJobExecution(jobId, envVars);
    res.status(200).json({ status: 1, message: 'GenerateTile excuted successfully' });
  } catch (error) {
    logger.error('Error in GenerateTile', { message: error });
    res.status(500).json({ status: 0, error: `Error GenerateTile: ${error}` });
  }
}

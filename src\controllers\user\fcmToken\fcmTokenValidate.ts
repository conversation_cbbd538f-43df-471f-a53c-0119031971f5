import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

export const validateFcmTokenUpdate = [
  body('_id')
    .notEmpty()
    .withMessage('User ID is required')
    .isString()
    .withMessage('User ID must be a string'),

  body('fcmToken')
    .notEmpty()
    .withMessage('FCM token is required')
    .isString()
    .withMessage('FCM token must be a string'),

  body('action')
    .notEmpty()
    .withMessage('Action is required')
    .isIn(['add', 'remove'])
    .withMessage('Action must be either "add" or "remove"'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        status: 0,
        errors: errors.array().map((err) => err.msg).join(', '),
      });
      return;
    }
    next();
  },
];

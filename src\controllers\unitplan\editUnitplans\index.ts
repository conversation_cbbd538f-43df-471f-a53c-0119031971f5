import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import { unitplanModule } from '../../../modules/unitplan';
// Import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { UnitplanInterface } from '../../../types/unitplan';
import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';

interface UpdatedUnitplanData extends UnitplanInterface {
  thumbnail?: string;
  image_url?: string;
}

export default async function EditUnitplan (
  request: FileRequest,
  response: Response,
): Promise<void> {
  const reqBody: UnitplanInterface = request.body;
  const unitplanId = request.body.unitplan_id as string; // Assuming unitplan_id is provided in the request body
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  const unitplan = new unitplanModule(project_id, organization_id);
  console.log(request.body.project_id);
  // Check if any fields are provided for editing
  if (Object.keys(reqBody).length === 0) {
    response.status(400).json({ error: 'No fields provided for editing.' });
    return;
  }

  // Call a method to upload files if needed
  const requestFiles = request.files;
  let urlObject: { [key: string]: string } | undefined;
  if (requestFiles !== undefined) {
    try {
      urlObject = await UploadUnitplanFiles(
        requestFiles,
        unitplan.storagepath + unitplanId,
      );
    } catch (error) {
      logger.error('Error in UploadUnitplanFiles', {message: error});
      response.json({ status: 0, message: error });
      return;
    }
  }

  // Construct the updated data object
  const updatedData: UpdatedUnitplanData = {};
  if (reqBody.type) {
    updatedData.type = reqBody.type;
  }
  if (reqBody.name) {
    updatedData.name = reqBody.name;
  }
  if (reqBody.measurement) {
    updatedData.measurement = reqBody.measurement;
  }
  if (reqBody.measurement_type) {
    updatedData.measurement_type = reqBody.measurement_type;
  }
  if (reqBody.suite_area) {
    updatedData.suite_area = reqBody.suite_area;
  }
  if (reqBody.suite_area_type) {
    updatedData.suite_area_type = reqBody.suite_area_type;
  }
  if (reqBody.tour_id) {
    updatedData.tour_id = reqBody.tour_id;
  }
  if (reqBody.building_id) {
    updatedData.building_id = reqBody.building_id;
  }
  if (reqBody.bedrooms) {
    updatedData.bedrooms = reqBody.bedrooms;
  }
  if (reqBody.is_residential !== undefined) {
    updatedData.is_residential = reqBody.is_residential;
  }
  if (reqBody.bathrooms) {
    updatedData.bathrooms = reqBody.bathrooms;
  }
  if (reqBody.is_furnished !== undefined) {
    updatedData.is_furnished = reqBody.is_furnished;
  }
  if (urlObject) {
    if (urlObject.lowRes) {
      updatedData.thumbnail = urlObject.lowRes;
    }
    if (urlObject.highRes) {
      updatedData.image_url = urlObject.highRes;
    }
  }
  if (reqBody.unit_type !== undefined) {
    updatedData.unit_type = reqBody.unit_type;
  }
  if (reqBody.exterior_type !== undefined) {
    updatedData.exterior_type = reqBody.exterior_type;
  }
  if (reqBody.scene_id !== undefined) {
    updatedData.scene_id = reqBody.scene_id;
  }
  if (reqBody.gallery_id !== undefined) {
    updatedData.gallery_id = JSON.parse(reqBody.gallery_id);
  }
  if (reqBody.floor_unitplans !== undefined) {
    updatedData.floor_unitplans = JSON.parse(reqBody.floor_unitplans);
  }
  if (reqBody.style !== undefined) {
    updatedData.style = reqBody.style;
  }
  if (reqBody.balcony_measurement !== undefined) {
    updatedData.balcony_measurement = reqBody.balcony_measurement;
  }
  if (reqBody.balcony_measurement_type !== undefined) {
    updatedData.balcony_measurement_type = reqBody.balcony_measurement_type;
  }
  if (reqBody.is_commercial !== undefined) {
    updatedData.is_commercial = reqBody.is_commercial;
  }

  try {
    const updatedUnitplan = await unitplan.editUnitplan(
      unitplanId,
      updatedData,
      'update',
    );
    response.json({ status: 1, data: updatedUnitplan });
  } catch (error) {
    logger.error('Error in editUnitplan', {message: error});
    response.json({ status: 0, message: error });
  }
}

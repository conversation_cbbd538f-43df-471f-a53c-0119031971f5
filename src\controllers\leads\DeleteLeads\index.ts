import { LeadsModule } from '../../../modules/leads';
import { Leads } from '../../../types/leads';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';

export async function DeleteLead (
  request: ExtendedRequest,
  response: Response,
): Promise<Leads | void> {
  const organization_id = request.organization_id as string;
  const {lead_id}  = request.params;
  const leads = new LeadsModule(organization_id);
  await leads
    .DeleteLead(lead_id)
    .then((leadData: Leads| null) => {
      response
        .status(200)
        .json({ status: 1, data: leadData });
    })
    .catch((error: Error) => {
      response
        .status(500)
        .json({ status: 0, error: 'Error while Deleting the lead'+ error });
    });
}

import { Router } from 'express';
// Import runReport from '../googleAnalytics/CoreReport';
// Import realtimeReport from '../googleAnalytics/RealtimeReport';
import { validateReportParams } from '../controllers/analytics/getReports/validateReportParams';
import { generateReport } from '../controllers/analytics/getReports';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { getEventDetails } from '../controllers/analytics/getEventReport';
import { validateEventParams } from '../controllers/analytics/getEventReport/validateEventParams';

const router = Router();
router.post('/getReports',  authMiddleware, validateReportParams, generateReport);
router.post('/getEventReport', authMiddleware, validateEventParams, getEventDetails);

// Router.post('/analytics/realtime', async (req: Request, res: Response) => {
//   Const { orgId, projectId } = req.body;

//   If (!orgId || !projectId) {
//     Return res.status(400).send('Missing required parameters');
//   }

//   Try {
//     Const report = await realtimeReport({ orgId, projectId });
//     Res.json(report);
//   } catch (error) {
//     Console.error('Error running real-time report:', error);
//     Res.status(500).send('Internal Server Error');
//   }
// });

export default router;

import { Request, Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';
import { SessionSource} from '../../../types/session';

const CreateSessionValidate = [
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('source', 'Source is required').notEmpty(),
  body('source', 'Invalid session source. Please ensure that you are using a valid session source value')
    .isIn(Object.values(SessionSource)),
  body('is_scheduled', 'Is Scheduled parameter is required').isBoolean(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default CreateSessionValidate;

import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
// Import multer from 'multer';
import { MasterSceneModule } from '../../../modules/masterScene';
import mongoose, { Types } from 'mongoose';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { deep_zoom_status } from '../../../types/projectScene';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';
import { EnvVar } from '../../../types/projectSVG';
import { MasterSVGModule } from '../../../modules/masterSVG';
// Import fs from 'fs';

// Const fileFields = [
//   { name: 'lowRes' },
//   { name: 'highRes' },
//   { name: 'info_icon' },
// ];

export default async function createScene (request:FileRequest, response:Response): Promise<void> {
    interface createScene {
        maxZoomLevel?: number;
        minZoomLevel?: number;
        organization_id:string;
        type:string;
        name:string;
        active:boolean;
        parent:string;
        info_text?:string;
        info_icon: string;
        root:boolean;
        clouds:boolean;
        gsplat_link: string;
        category: string;
        position?: string;
        polar_angle?: string;
        distance?: string;
        auto_rotate?: boolean;
        path?: string;
        file_url?:string;
        deep_zoom_status?:string;
    }
    const _id = new Types.ObjectId();

    const reqbody:createScene = request.body;
    const organization_id = request.headers.organization as string;
    const masterScene = new MasterSceneModule(organization_id);
    const randomId = new mongoose.Types.ObjectId();
    const masterSVG = new MasterSVGModule(organization_id);
    if (!request.files){
      response.send('Error reading SVG file');
      return;
    }
    const requestFiles = request.files;
    if (requestFiles === undefined) {
      response.send('Error reading SVG file:');
      return;
    }
    let file_url = '' as string;
    const storagePath = (masterScene.storagepath + _id) as string;
    if (reqbody.type === 'deep_zoom') {

      try {

        if (reqbody.file_url){
          file_url = reqbody.file_url;
        }

        // Const outputFrom_getHighres: background | null = await getHighRes(
        //   StoragePath,
        //   RequestFiles,
        //   File_url,
        // );
        let filesToUpload: Express.Multer.File | Express.Multer.File[] |
        { [fieldname: string]: Express.Multer.File[]; } = [];

        Object.values(requestFiles).forEach((filesArray: Express.Multer.File[]) => {
          filesArray.forEach( (file: Express.Multer.File) => {
            if (file.fieldname === 'lowRes') {
              // Wrap the file in an object with the field name as key
              filesToUpload = { [file.fieldname]: [file] };
            }
          });
        });

        UploadUnitplanFiles(filesToUpload, storagePath).then(
          async (urlObject: { [key: string]: string }) => {

            await masterScene
              .createScene({
                _id: _id,
                organization_id: reqbody.organization_id,
                type: reqbody.type,
                name: reqbody.name,
                background: {
                  low_resolution: urlObject.lowRes,
                },
                deep_zoom_status: deep_zoom_status.NOT_STARTED,
                active: reqbody.active,
                parent: reqbody.parent,
                ...(reqbody.info_text && {info_text: reqbody.info_text}),
                category: reqbody.category,
                clouds: reqbody.clouds,
                master_scene: true,
                minZoomLevel: reqbody.minZoomLevel ?reqbody.minZoomLevel:1,
                maxZoomLevel: reqbody.maxZoomLevel?reqbody.maxZoomLevel:10,
              })
              .then((res) => {
                response.send({
                  status: 1,
                  message: 'Created Scene Succesfully',
                  data: res,
                });
              });
            const scene_id = _id as unknown as string;
            const envVars : EnvVar[]  = [
              { name: 'organization_id', value: reqbody.organization_id },
              { name: 'scene_id', value: scene_id },
              { name: 'storagepath', value: storagePath },
              { name: 'url', value: file_url },
              {name: 'scene_type', value: 'master'},
            ];
            const jobId = 'luma-blender-jobs';
            const runJob = new cloudRunJobModule;
            runJob.runJobExecution(jobId, envVars);

          },
        );

      } catch (error){
        if (error instanceof Error){
          response
            .status(400)
            .send({ status: 0, error: 'Error in CreateScene Deep_zoom', message: error.message });
        }

      }
    } else {
      UploadUnitplanFiles(requestFiles, masterScene.storagepath + _id).then(
        (urlObject: { [key: string]: string }) => {
          console.log('urlObject', urlObject);

          masterScene
            .createScene({
              _id: _id,
              organization_id: reqbody.organization_id,
              type: reqbody.type,
              name: reqbody.name,
              background: {
                low_resolution: urlObject.lowRes,
                high_resolution: urlObject.highRes,
              },
              active: reqbody.active,
              info_icon: urlObject.info_icon,
              parent: reqbody.parent,
              ...(reqbody.info_text && {info_text: reqbody.info_text}),
              video: urlObject.video,
              clouds: reqbody.clouds,
              root: reqbody.root,
              gsplat_link: urlObject.gsplat,
              category: reqbody.category,
              position: reqbody.position
                ? JSON.parse(reqbody.position as string)
                : undefined,
              polar_angle: reqbody.polar_angle
                ? JSON.parse(reqbody.polar_angle as string)
                : undefined,
              distance: reqbody.distance
                ? JSON.parse(reqbody.distance as string)
                : undefined,
              auto_rotate: reqbody.auto_rotate,
              master_scene: true,
            })
            .then(async (res) => {
              if (res && reqbody.type === 'gsplat') {
                masterSVG
                  .createSVG({
                    _id: randomId,
                    scene_id: res._id,
                    layers: {},
                    type: reqbody.type,
                  })
                  .then(() => {
                    response.send({ status: 1, data: res });
                  });
              } else if (res && reqbody.type === 'rotatable_image_frame') {
                masterScene
                  .updateScene(reqbody.parent, { frame_id: res._id })
                  .then(() => {
                    response.send({ status: 1, data: res });
                  });
              } else {
                response.send({ status: 1, data: res });
              }
            })
            .catch((error) => {
              console.log(error);
              response.send({ status: 0, message: error });
            });
        },
      );
    }

  // Const upload = multer({ dest: 'masterScenes/' }).fields(fileFields);
  // Upload(request, response, (err) => {

  // });
}

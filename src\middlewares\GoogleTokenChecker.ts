import { Request, Response, NextFunction } from 'express';
import { OAuth2Client, TokenPayload } from 'google-auth-library';

const client = new OAuth2Client();

// Middleware to verify OIDC ID token from Google Cloud
export async function verifyGoogleToken (req: Request, res: Response, next: NextFunction): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    const idToken = authHeader?.startsWith('Bearer ') ? authHeader.split('Bearer ')[1] : null;

    if (!idToken) {
      res.status(401).send('Missing ID token');
      return;
    }

    const ticket = await client.verifyIdToken({
      idToken,
      audience: process.env.AUDIENCE_URL,
    });

    const payload: TokenPayload | undefined = ticket.getPayload();

    if (!payload) {
      res.status(403).send('Invalid token payload');
      return;
    }

    (req as any).user = payload;

    next();
  } catch (error) {
    console.error('Token verification failed:', error);
    res.status(403).send('Unauthorized');
  }
}

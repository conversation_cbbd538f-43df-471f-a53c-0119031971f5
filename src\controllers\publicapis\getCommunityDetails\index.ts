import { Request, Response } from 'express';
import { community } from '../../../types/community';
import { communityModule } from '../../../modules/community';
import logger from '../../../config/logger';

export async function getCommunities (
  request: Request,
  response: Response,
): Promise<community | void> {
  const project_id = request.query.project_id as string;
  const organization = request.query.organization as string;
  const communityMod = new communityModule(project_id, organization);
  const communities = await communityMod.getCommunities();
  if (communities) {
    response.status(200).json({ status: 1, data: communities });
  } else {
    logger.error('community not found');
    response.status(500).json({ status: 0, error: 'community not found' });
  }
}


import { Request, Response } from 'express';
import { ParsedUnitModule } from '../../modules/parsedUnits';
import logger from '../../config/logger';
import { ParsedQs } from 'qs';

function sanitizeQuery (query: ParsedQs): Record<string, string> {
  const cleanQuery: Record<string, string> = {};
  for (const key in query) {
    const value = query[key];
    if (typeof value === 'string') {
      cleanQuery[key] = value;
    }
  }
  return cleanQuery;
}

export async function getParsedUnits (
  request:Request,
  response:Response,
):Promise<void>{
  try {
    const { projectId, organization_id } = request.params;
    const externalUnit = new ParsedUnitModule();
    const query = sanitizeQuery(request.query);
    const unitsList = await externalUnit.getParsedUnits(projectId, organization_id, query);

    response.status(200).json({
      status: 1,
      data: unitsList,
    });
  } catch (error) {
    logger.error('Error in GetUnitsList controller', { error });
    response.status(500).json({
      status: 0,
      error: `Error while fetching units list${error}`,
    });
  }
}

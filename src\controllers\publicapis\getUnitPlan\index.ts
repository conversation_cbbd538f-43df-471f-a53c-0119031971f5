import { Request, Response } from 'express';
import { unitplanModule } from '../../../modules/unitplan';
import logger from '../../../config/logger';

export async function GetUnitplan (
  request: Request,
  response: Response,
): Promise<void> {
  const { unitplan_id, project_id, organization_id } = request.params;
  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'organization_id not found'});
    return;
  }
  const unitplan = new unitplanModule(project_id, organization_id);
  const unitplanData = await unitplan.getUnitplan(unitplan_id);

  if (unitplanData) {
    response.status(200).json({ status: 1, data: unitplanData });
  } else {
    logger.error('unitplan not found');
    response.status(500).json({ status: 0, error: 'unitplan not found' });
  }
}

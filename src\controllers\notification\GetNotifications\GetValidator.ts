import { validationResult, query, header } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

export const GetNotificationsValidate = [
  query('user_id', 'User ID is required').notEmpty(),
  header('organization', 'Organization is required').notEmpty(),
  query('viewed')
    .optional()
    .isString()
    .custom((value) => {
      if (value !== 'false') {
        throw new Error('Unviewed must be "false"');
      }
      return true;
    }),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

import mongoose from 'mongoose';
import { admin } from '../../config/firebase';
import logger from '../../config/logger';
import { Notification,
  NotificationPayload,
  NotificationFilter,
  NotificationResponse,
  NotificationStatus,
  UpdateNotification,
} from '../../types/notification';
import { notificationSchema } from '../../schema/notificationSchema';
import { sessionAnalyticsQuery } from '../../types/session';

export class NotificationModule {
  private model: mongoose.Model<Notification>;
  constructor (user_id:string) {
    this.model = mongoose.model<Notification>(
      `${user_id}_notifications`,
      notificationSchema,
    );
  }
  public async CreateNotification (user_id:string, organization:string, url:string):Promise<Notification> {
    logger.info('CreateNotification Called');
    const newNotification: Notification = {
      _id: new mongoose.Types.ObjectId(),
      url,
      status: NotificationStatus.ACTIVE,
      user_id,
      organization,
      timestamp: new Date().toISOString(),
      viewed: false,
    };
    const notifcations = new this.model(newNotification);
    const notification = await notifcations.save();
    logger.info('CreateNotification Successfull', {notification: notification});
    return notification;
  }
  public async sendNotification (payload: NotificationPayload): Promise<NotificationResponse> {
    try {
      // Normalize tokens to array
      const tokensArray = Array.isArray(payload.tokens)
        ? payload.tokens
        : [payload.tokens];

      // Check if single or multicast
      if (tokensArray.length === 1) {
        // Single device notification
        const response = await admin.messaging().send({
          token: tokensArray[0],
          notification: {
            title: payload.notification.title,
            body: payload.notification.body,
          },
          data: payload.data || {},
        });
        console.log(response);
        logger.info('Single device notification sent successfully', {
          messageId: response,
          token: tokensArray[0],
        });

        return { messageId: response };
      }
      // Multicast notification
      const response = await admin.messaging().sendEachForMulticast({
        tokens: tokensArray,
        notification: {
          title: payload.notification.title,
          body: payload.notification.body,
        },
        data: payload.data || {},
      });
      console.log(response);
      // Log multicast results
      logger.info('Multicast notification sent', {
        successCount: response.successCount,
        failureCount: response.failureCount,
      });

      // Identify failed tokens
      const failedTokens = response.responses
        .map((resp, index) => (resp.success ? null : tokensArray[index]))
        .filter((token) => token !== null) as string[];

      return {
        successCount: response.successCount,
        failureCount: response.failureCount,
        failedTokens,
      };

    } catch (error) {
      // Log and rethrow error
      logger.error('Failed to send notification', {
        error: error instanceof Error ? error.message : error,
        payload,
      });
      throw error;
    }
  }
  public async GetNotifications (query: sessionAnalyticsQuery, viewed?:false): Promise<object | null> {
    try {
      if (!query.user_id || !query.organization_id) {
        throw new Error('User ID and organization ID are required');
      }

      const queryFilter: NotificationFilter = {
        user_id: query.user_id,
        organization: query.organization_id,
      };

      if (viewed === false) {
        queryFilter.viewed = viewed;
      }

      const result = await this.model.find(queryFilter);

      if (result.length > 0) {
        return result;
      }

      return null;
    } catch (error) {
      logger.error(`Unable to fetch notifications for this user - ${query.user_id}`);
      throw new Error(`Unable to fetch notifications for this user - ${error}`);
    }
  }
  public async updateNotificationById (id:string, payload: UpdateNotification): Promise<object | null> {
    try {
      // Create an update object with only the allowed fields
      const updateFields: Partial<UpdateNotification> = {};

      // Only add fields that exist in the payload
      if (payload.status !== undefined) {
        updateFields.status = payload.status;
      }

      if (payload.viewed !== undefined) {
        updateFields.viewed = payload.viewed;
      }

      // Find and update the document with the given id
      const result = await this.model.findByIdAndUpdate(
        id,
        { $set: updateFields },
        { new: true }, // Return the updated document
      );

      // Check if document was found and updated
      if (result) {
        return result;
      }

      // No document was found
      return null;
    } catch (error) {
      logger.error(`Unable to update notification with ID: ${id}`);
      throw new Error(`Unable to update notification - ${error}`);
    }
  }
}

import mongoose from 'mongoose';
import { deep_zoom_status, projectSceneType } from '../../types/projectScene';
import { coordinateLinkType } from '../../types/masterScene';
export const masterscenesSchema = new mongoose.Schema({
  _id: String,
  organization_id: String,
  type: {
    type: String,
    enum: projectSceneType,
    default: projectSceneType.IMAGE,
  },
  name: String,
  background: {
    low_resolution: {
      type: String,
      immutable: false,
    },
    high_resolution: {
      type: String,
      immutable: false,
    },
  },
  video: String,
  active: Boolean,
  info_icon: String,
  parent: String,
  info_text: String,
  earth_position: Object,
  coordinates: [{
    _id: {
      type: String,
      immutable: true,
    },
    lat: {
      type: String,
      immutable: false,
    },
    lng: {
      type: String,
      immutable: false,
    },
    link: {
      type: String,
      immutable: false,
    },
    scene_id: {
      type: String,
      immutable: false,
    },
    project_id: {
      type: String,
      immutable: false,
    },
    name: {
      type: String,
      immutable: false,
    },
    linkType: {
      type: String,
      enum: coordinateLinkType,
      default: coordinateLinkType.MASTER,
    },
    active: {
      type: Boolean,
      immutable: false,
    },
  }],
  coordinate_settings: Object,
  clouds: Boolean,
  root: Boolean,
  gsplat_link: String,
  category: String,
  frames: Object,
  order: Number,
  deep_zoom_status: {
    type: String,
    enum: deep_zoom_status,
  },
  deep_zoom_failed_info: String,
  position: {
    x: Number,
    y: Number,
    z: Number,
  },
  polar_angle: {
    max: Number,
    min: Number,
  },
  distance: {
    max: Number,
    min: Number,
  },
  auto_rotate: Boolean,
  master_scene: Boolean,
  minZoomLevel: Number,
  maxZoomLevel: Number,
});

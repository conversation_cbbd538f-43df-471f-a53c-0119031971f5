import { Request, Response, NextFunction } from 'express';
import { validationResult, param } from 'express-validator';

const listProjectOrgValidate = [
  param('organization_id', 'Organization ID is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default listProjectOrgValidate;

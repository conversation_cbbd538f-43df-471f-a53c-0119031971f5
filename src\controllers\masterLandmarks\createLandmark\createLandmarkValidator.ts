import { check, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { masterLandmarkCategory } from '../../../types/masterLandmark';
interface UploadedFiles {
  thumbnail: Express.Multer.File[];
}

const CreateLandmarkValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  console.log(req.files);
  console.log(req.body);

  const files = req.files as UploadedFiles | undefined;

  if (files) {
    if (!files.thumbnail) {
      res.status(400).json({ error: 'Thumbnail image field is required.' });
    } else {
      const requiredTextFields = [
        'distance',
        'name',
        'category',
        'walk_timing',
        'transit_timing',
        'car_timing',
      ];

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        res.status(400).json({
          error: `Missing text fields: ${missingTextFields.join(', ')}`,
        });
      } else {
        check('category', 'Invalid category value. Please ensure that you are using a valid category value')
          .isIn(Object.values(masterLandmarkCategory)).run(req);
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export default CreateLandmarkValidate;

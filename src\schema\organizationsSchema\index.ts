import mongoose from 'mongoose';
import { FontType, Theme } from '../../types/projects';
import { generateFontLink } from '../../helpers/projects';
export const organizationSchema = new mongoose.Schema({
  _id: String,
  name: String,
  founding_date: Date,
  contact_email: String,
  phone_number: String,
  thumbnail: String,
  address: String,
  website: String,
  max_users: Number,
  roles: Array,
  domain: String,
  unique_org_id: String,
  theme: {
    type: String,
    enum: Theme,
    default: Theme.LIGHT,
  },
  primary: {
    type: String,
    immutable: false,
  },
  primary_text: {
    type: String,
    immutable: false,
  },
  secondary: {
    type: String,
    immutable: false,
  },
  secondary_text: {
    type: String,
    immutable: false,
  },
  font_type: {
    type: String,
    enum: FontType,
    default: 'Roboto',
  },
  font_url: {
    type: String,
    immutable: false,
    default: generateFontLink('Roboto' as FontType),
  },
  measurement_id: {
    type: String,
    immutable: false,
    default: '',
  },
  baseCurrency: {
    type: String,
  },
  exchangeRatio: {
    type: Object,
  },
});

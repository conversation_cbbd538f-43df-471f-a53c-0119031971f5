
import { Request, Response } from 'express';
import { IconLibraryModule } from '../../../modules/iconLibrary';
import { QueryParams } from '../../../types/iconLibrary';
import { arrayToObject } from '../../../helpers/dataFormatHelper';

export async function SearchIcon (
  request: Request,
  response: Response,
): Promise<void> {
  // Const organization_id = request.headers.organization as string;
  const query:QueryParams = { ...request.query };

  const iconLibrary = new IconLibraryModule();

  try {
    const iconSearchResults = await iconLibrary.SearchIcon(query);
    if (iconSearchResults) {
      const SearchResultObj = arrayToObject(iconSearchResults);
      response.send({
        status: 1,
        message: 'SearchIcon Succesfull',
        data: SearchResultObj,
      });
    } else {
      response
        .status(404)
        .send({status: 0, data: 'No Icons Found'});
    }
  } catch (error){
    if (error instanceof Error){
      response
        .status(400)
        .send({ status: 0, error: 'Error in SearchIconsLibrary', message: error.message });
    }
  }

}

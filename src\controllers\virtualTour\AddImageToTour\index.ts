import { VirtualTourModule } from '../../../modules/virtualTour';
import { addImageType, EnvVar } from '../../../types/virtualTour';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import mongoose from 'mongoose';
import logger from '../../../config/logger';
import { cloudRunJobModule } from '../../../modules/cloudrunJobs';

export async function AddImageToTour (
  req: ExtendedRequest,
  res: Response,
): Promise<void> {
  try {
    const organization_id = req.organization_id as string;
    const {project_id, tour_id, images } = req.body;

    if (!project_id || !tour_id ) {
      throw new Error('project_id (or) tour_id is undefined');
    }

    if (!Array.isArray(images) || images.length === 0 ) {
      throw new Error('images must be a non-empty array');
    }

    const allAddedImages = [];

    for (const item of images){

      const { name, rotation, order, url, thumbnail } = item; // Item details

      if (!url || !thumbnail) {
        throw new Error('Urls not found neither in thumbnail nor url');
      }

      const id = new mongoose.Types.ObjectId().toString();
      const tourModule = new VirtualTourModule(organization_id, project_id);
      const storagePath = (tourModule.storagepath +'/'+tour_id) as string;

      const imagePayload: addImageType = {
        id,
        name,
        url: url,
        thumbnail: thumbnail,
        rotation,
        order: Number(order),
      };

      const addedImage = await tourModule.AddImagesToTour(tour_id, imagePayload);

      if (!addedImage) {
        throw new Error(`Failed to AddImagesToTour. Payload: ${JSON.stringify(imagePayload)}, Response: ${JSON.stringify(addedImage)}`);
      }

      const image_id = id as unknown as string;
      const envVars : EnvVar[]  = [
        { name: 'project_id', value: project_id },
        { name: 'organization_id', value: organization_id },
        { name: 'image_id', value: image_id },
        { name: 'tour_id', value: tour_id },
        { name: 'storagepath', value: storagePath },
        { name: 'url', value: url },
      ];
      const jobId = 'tile-generator';
      const runJob = new cloudRunJobModule;
      runJob.runJobExecution(jobId, envVars); // Run the job

      allAddedImages.push(addedImage); // Push the addedImage module result.
    }

    res.status(200).json({ status: 1, data: allAddedImages[allAddedImages.length - 1]  }); // Send the all response
  } catch (error) { // Loop exits immediately
    logger.error('Error in AddImageToTour', { message: error });
    res.status(500).json({ status: 0, error: `Error adding image: ${error}` });
  }
}

import app  from '../../../app';
import request from 'supertest';
describe('List Projects From Organization Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/listProjectsFromOrganization?organization_id=8X3plU')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual({});
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/listProjectsFromOrganization?organization_id=8X3p')   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).toEqual({});
  });
});

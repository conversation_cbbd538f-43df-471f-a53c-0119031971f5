import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const createLayersValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  body('query', 'Query is required').notEmpty(),
  body('position', 'Position is required').notEmpty(),
  body('svg_id', 'SVG ID is required').notEmpty(),
  body('name').custom((value, { req }) => {
    if (JSON.parse(req.body.query)?.type !== 'plane' && !value) {
      throw new Error('Name is required');
    }
    return true;
  }),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createLayersValidate;

import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { TranslationModule } from '../../../modules/translation';
import ExcelJS from 'exceljs';
import logger from '../../../config/logger';
import {
  SupportedLanguages,
  Translations,
  ExtendedTranslationDocument,
} from '../../../types/translation';
import multer from 'multer';
import { Readable } from 'stream';

// Configure multer for file upload
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (_req, file, cb) => {
    if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      cb(null, true);
    } else {
      cb(new Error('Only .xlsx files are allowed'));
    }
  },
}).single('file');

// Helper function to get cell value as string
function getCellValue (cell: ExcelJS.Cell): string {
  if (!cell.value) {
    return '';
  }

  if (cell.value instanceof Date) {
    return cell.value.toISOString();
  }

  if (typeof cell.value === 'object') {
    if ('text' in cell.value) {
      return cell.value.text as string;
    }
    // Handle rich text if present
    if ('richText' in cell.value) {
      return cell.value.richText.map((rt: { text: string }) => rt.text).join('');
    }
  }

  return String(cell.value).trim();
}

export const ImportTranslations = async (req: ExtendedRequest, res: Response): Promise<Response | void> => {
  try {
    logger.info('ImportTranslations Called');

    return new Promise((resolve) => {
      upload(req, res, async (err) => {
        if (err) {
          logger.error('Error uploading file', { error: err });
          resolve(res.status(400).json({
            message: err.message,
            error: err,
          }));
          return;
        }

        if (!req.file) {
          resolve(res.status(400).json({
            message: 'No file uploaded',
          }));
          return;
        }

        const organizationId = req.organization_id as string;
        const translationModule = new TranslationModule(organizationId);

        try {
          const stream = new Readable();
          stream.push(req.file.buffer);
          stream.push(null);

          const workbook = new ExcelJS.Workbook();
          await workbook.xlsx.read(stream);

          const worksheet = workbook.worksheets[0];
          if (!worksheet) {
            throw new Error('Excel file is empty');
          }

          // Get headers
          const headers: string[] = [];
          worksheet.getRow(1).eachCell((cell, colNumber) => {
            const headerValue = getCellValue(cell);
            headers[colNumber - 1] = headerValue.toLowerCase() === 'english' ? 'en' : headerValue.toLowerCase();
          });

          // Get existing translations
          const { translations: existingTranslations } = await translationModule.GetAllTranslation();

          const updates: Promise<unknown>[] = [];
          const updatedTranslations: { id: string; language: string; value: string }[] = [];
          const newTranslations: { text: string; languages: { code: string; value: string }[] }[] = [];

          // Process each row
          for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
            const row = worksheet.getRow(rowNumber);
            if (!row.hasValues) {
              continue;
            }

            const rowData: ExtendedTranslationDocument = {};

            // Build row data
            headers.forEach((header, index) => {
              const cellValue = getCellValue(row.getCell(index + 1));
              if (cellValue) {
                if (header === '_id') {
                  rowData._id = cellValue;
                } else {
                  rowData[header] = cellValue;
                }
              }
            });

            // Skip rows without English text
            if (!rowData.en) {
              continue;
            }

            if (rowData._id) {
              // Update existing translation
              const existingTranslation = existingTranslations[rowData.en];

              if (existingTranslation) {
                // Find changed languages
                Object.entries(rowData).forEach(([lang, value]) => {
                  if (lang !== '_id' && lang !== 'en' && value &&
                    existingTranslation[lang as keyof Translations] !== value) {
                    updates.push(
                      translationModule.UpdateTranslationById(
                        rowData._id as string,
                        lang as SupportedLanguages,
                        String(value),
                      ),
                    );
                    updatedTranslations.push({
                      id: rowData._id as string,
                      language: lang,
                      value: String(value),
                    });
                  }
                });
              }
            } else {
              // Add new translation
              logger.info('Adding new translation', { rowData });
              const translationObj: Record<string, string> = {};
              const newTranslationLanguages: { code: string; value: string }[] = [];

              headers.forEach((header) => {
                if (header !== '_id' && header !== '__v') {
                  translationObj[header] = '';
                }
              });

              Object.entries(rowData).forEach(([key, value]) => {
                if (key !== '_id' && value) {
                  translationObj[key] = String(value);
                  if (key !== 'en') {
                    newTranslationLanguages.push({
                      code: key,
                      value: String(value),
                    });
                  }
                }
              });

              newTranslations.push({
                text: rowData.en,
                languages: newTranslationLanguages,
              });

              updates.push(
                translationModule.AddTranslation(
                  translationObj,
                  SupportedLanguages.EN,
                  rowData.en,
                ),
              );
            }
          }

          await Promise.all(updates);

          logger.info('ImportTranslations Successful');
          resolve(res.status(200).json({
            status: 1,
            data: {
              totalUpdates: updates.length,
              updatedTranslations,
              newTranslations,
            },
          }));
        } catch (error) {
          logger.error('Error processing Excel file', { error });
          resolve(res.status(500).json({
            message: 'Failed to process Excel file',
            error: error,
          }));
        }
      });
    });
  } catch (error) {
    logger.error('Error in ImportTranslations', { error });
    return res.status(500).json({
      message: 'Failed to import translations',
      error: error,
    });
  }
};

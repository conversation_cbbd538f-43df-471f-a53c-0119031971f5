import { NextFunction, RequestHandler, Response } from 'express';
import { ExtendedRequest } from '../types/extras';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { JWTAuthControl } from '../middlewares/jwtController';
import { organizationAccessMiddleware } from './OrganizationAccess';

export const authChecker: RequestHandler = function (
  request: ExtendedRequest,
  response: Response,
  next: NextFunction,
) {
  const authHeader = request.headers.authorization;

  if (authHeader && authHeader.startsWith('Bearer ')) {
    console.log('bearer');
    // If Bearer token is present, call JWTAuthControl
    request.headers.organization = request.organization_id as string;
    return JWTAuthControl(request, response, next);
  }

  // If no Bearer token, proceed with authMiddleware and organizationAccessMiddleware
  authMiddleware(request, response, function (err) {
    if (err) {
      return next(err); // Handle errors from authMiddleware
    }
    // After authMiddleware, call organizationAccessMiddleware
    organizationAccessMiddleware(request, response, function (error) {
      if (error) {
        return next(error); // Handle errors from organizationAccessMiddleware
      }
      next(); // Proceed to the next middleware or route handler
      return 0;
    });
    return 0;
  });
  return 0;
};

import { UserRole } from '../../../types/organization';
import { ExtendedRequest } from '../../../types/extras';
import { SessionModule } from '../../../modules/sessions';
import { Response } from 'express';
import logger from '../../../config/logger';

export async function GetAnalytics (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const organization_id = request.organization_id as string;
  const user_id = request.IsAuthenticated?.uid as string;
  const userRole = request.UserRole;
  const user_id_query = request.query.user_id as string;

  const page = parseInt(request.query.page as string) || 1;
  const limit = parseInt(request.query.limit as string) || 10;

  delete request.query.page;
  delete request.query.limit;
  try {
    let sessionData;

    if (userRole?.role === UserRole.ADMIN) {
      sessionData = await session.getAnalytics(
        request.query,
        organization_id,
        user_id_query,
        page,
        limit,
      );
    } else if (
      userRole?.role === UserRole.EDITOR ||
      userRole?.role === UserRole.READER
    ) {
      if (user_id_query === user_id) {
        sessionData = await session.getAnalytics(
          request.query,
          organization_id,
          user_id_query,
          page,
          limit,
        );
      } else {
        response.status(403).json({ status: 0, error: 'Permission Denied' });
      }
    }

    if (sessionData) {
      response.status(200).json({ status: 1, data: sessionData });
    } else {
      logger.warn('No Analytics found');
      response.status(404).json({ status: 0, error: 'No Analytics found' });
    }
  } catch (error) {
    logger.error('Error in GetAnalytics:', { error });
    response.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

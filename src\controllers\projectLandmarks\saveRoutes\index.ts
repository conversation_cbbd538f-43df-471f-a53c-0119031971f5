import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import { ProjectLandmarkModule } from '../../../modules/projectLandmark';
import { routeCategory } from '../../../types/projectLandmark';
import logger from '../../../config/logger';
export async function saveRoutes (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const project_id = request.body.project_id as string;
  const landmark_id = request.body.landmark_id as string;
  const mode = request.body.mode as routeCategory;
  const json = request.body.json as string;
  const projectLandmark = new ProjectLandmarkModule(project_id, organization_id);
  projectLandmark
    .saveRoutes(landmark_id, json, mode)
    .then(async (res) => {
      response.json({ status: 1, data: res });
    })
    .catch((error) => {
      logger.error('Error in saveRoutes ', {message: error});
      response.json({ status: 0, message: error });
    });
}

import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import { Invitation, UserInviteStatus } from '../../../types/invitations';
import { InvitesModule } from '../../../modules/invites';
import logger from '../../../config/logger';
export async function CreateInvitation (
  request: ExtendedRequest,
  response: Response,
): Promise<Invitation | void> {
  const organization_id = request.headers.organization as string;
  const invitation = new InvitesModule();
  const createInvitationData = {
    email: request.body.email,
    role: request.body.role,
    organization_id: organization_id,
    status: 'pending' as UserInviteStatus,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
  await invitation
    .CreateInvitation(createInvitationData)
    .then((inviteData) => {
      response.status(201).json({ status: 1, data: inviteData });
    })
    .catch((error: Error) => {
      logger.error('Error while creating the project', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating the project'+ error });
    });
}

import {Request, Response} from 'express';
import { MasterSVGModule } from '../../../modules/masterSVG';
import logger from '../../../config/logger';

export default async function updateLayers (request:Request, response:Response):Promise<void>{
  const organization_id = request.headers.organization as string;
  const masterSVG = new MasterSVGModule(organization_id);
  masterSVG.updateLayers(request.body).then((res) => {
    response.send({status: 1, data: res});
  })
    .catch((err) => {
      logger.error('scene not found:', {message: err});
      response.send({status: 0, message: err});
    });
  return;
}

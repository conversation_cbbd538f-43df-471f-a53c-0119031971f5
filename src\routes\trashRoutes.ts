import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { getAllTrash } from '../controllers/trash/getAllTrash';
import { deleteTrash } from '../controllers/trash/deleteTrash';
import deleteTrashValidate from '../controllers/trash/deleteTrash/deleteTrashValidator';

const router = express.Router();

router.get(
  '/getAllTrash',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  getAllTrash,
);
router.post(
  '/deleteTrash',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  deleteTrashValidate,
  deleteTrash,
);
export default router;

import express from 'express';
import { GetApplicationList } from '../controllers/larkxr/getApplicationList';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { GetAuthCode } from '../controllers/larkxr/getAuthCode';
import { GetRunningCnt } from '../controllers/larkxr/getRunningCnt';
const router = express.Router();
router.get('/GetApplicationList',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  GetApplicationList);
router.get('/GetAuthCode',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  GetAuthCode);
router.get('/GetRunningCnt',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  GetRunningCnt);
export default router;

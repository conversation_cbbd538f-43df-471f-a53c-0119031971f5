import { NextFunction, Request, Response } from 'express';
import { query, validationResult } from 'express-validator';
import logger from '../../../config/logger';

const GetHotspotsValidate = [
  query('project_id', 'Project ID is required').notEmpty(),
  query('minimap_id', 'MiniMap ID is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Error: ', {message: errors.array()});
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default GetHotspotsValidate;

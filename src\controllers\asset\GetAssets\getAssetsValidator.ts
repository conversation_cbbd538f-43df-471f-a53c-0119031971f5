import { Request, Response, NextFunction } from 'express';
import { validationResult, header, param } from 'express-validator';
import logger from '../../../config/logger';

const getAssetsValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  param('project_id', 'Project ID  is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Asset Item not found', {message: errors.array()});
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default getAssetsValidate;

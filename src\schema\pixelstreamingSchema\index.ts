import mongoose from 'mongoose';

export const scalesetSchema = new mongoose.Schema({
  _id: String,
  versions: [{
    version: Number,
    instancesPerNode: Number,
    resolutionWidth: Number,
    resolutionHeight: Number,
    pixelstreamingApplicationName: String,
    fps: Number,
    unrealApplicationDownloadUri: String,
    msImprovedWebserversDownloadUri: String,
    msPrereqsDownloadUri: String,
    enableAutoScale: Boolean,
    instanceCountBuffer: Number,
    percentBuffer: Number,
    minMinutesBetweenScaledowns: Number,
    scaleDownByAmount: Number,
    minInstanceCount: Number,
    maxInstanceCount: Number,
    stunServerAddress: String,
    turnServerAddress: String,
    turnUsername: String,
    turnPassword: String,
  }],
  project_id: String,
  organization_id: String,
});

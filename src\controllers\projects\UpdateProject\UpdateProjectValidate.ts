
import { check, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { ctaType, ProjectSettingsType, SceneType, SupportedLanguages, FontType } from '../../../types/projects';
import { SessionType } from '../../../types/session';
import moment from 'moment-timezone';

// Interface UploadedFiles {
//   Font_url: Express.Multer.File[];
// }
const isValidTimezone = (value: string) => {
  return moment.tz.names().includes(value);
};
const allowedProjectSettingsKeys = Object.keys({} as ProjectSettingsType);

const UpdateProjectValidate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {

  // Const files = req.files as UploadedFiles | undefined;

  // Custom validation for font file
  // If (req.body.font_type === 'custom' && (!files || !files.font_url)) {
  //   Res.status(400).json({ error: 'Font file is required for font type custom.' });
  //   Return;
  // }
  // Validation logic
  await Promise.all([
    check('organization', 'Organization is required').notEmpty().run(req),
    check('accesstoken', 'Access Token is required').notEmpty().run(req),
    check('project_id', 'Project Id is required').notEmpty().run(req),
    check('query', 'Settings Type is required').notEmpty().run(req),
    check('query')
      .custom((value) => {
        if (typeof value !== 'object' || Array.isArray(value)) {
          throw new Error('query must be an object');
        }
        const keys = Object.keys(value);
        const invalidKeys = keys.filter((key) => !allowedProjectSettingsKeys.includes(key));
        if (invalidKeys.length > 0) {
          return true;
        }
        if (keys.length === 0) {
          throw new Error('query must contain at least one valid type');
        }
        return true;
      })
      .run(req),
    check('initial_scene_type', 'Scene Type is Invalid. Please make sure to pass a valid scene_type')
      .optional()
      .isIn(Object.values(SceneType))
      .run(req),
    check('cta_type', 'cta type is Invalid. Please make sure to pass a valid cta type value')
      .optional()
      .isIn(Object.values(ctaType))
      .run(req),
    check('default_experience',
      'Default Experience is Invalid. Please make sure to pass a valid default_experience value')
      .optional()
      .isIn(Object.values(SessionType))
      .run(req),
    check('timezone')
      .optional()
      .custom((value) => {
        if (isValidTimezone(value)) {
          return true;
        }
        throw new Error('Timezone is invalid. Please make sure to pass a valid value.');
      })
      .run(req),
    check('theme')
      .optional()
      .custom((value) => {
        if (value === 'custom') {
          const { primary, secondary, primary_text, secondary_text } = req.body;
          const presentFields = [primary, secondary, primary_text, secondary_text]
            .filter((field) => field !== undefined);
          if (presentFields.length < 1) {
            throw new Error(
              'At least one of primary, secondary, primary_text, or secondary_text must be provided for custom theme',
            );
          }
        }
        return true;
      })
      .run(req),
    check('font_type', 'Font type is Invalid. Please make sure to pass a valid font_type value')
      .optional()
      .isIn(Object.values(FontType))
      .run(req),
    check('supported_languages', 'Not a correct supported language code. Check the list of supported languages')
      .optional()
      .isIn(Object.values(SupportedLanguages))
      .run(req),
    check('default_language', 'Not a correct default language code. Check the list of default languages')
      .optional()
      .isIn(Object.values(SupportedLanguages))
      .run(req),
    check('metadata')
      .optional()
      .isObject()
      .withMessage('Metadata must be an object')
      .run(req),
  ]);

  // Check for validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
    return;
  }

  // Proceed to the next middleware if no errors
  next();
};

export default UpdateProjectValidate;

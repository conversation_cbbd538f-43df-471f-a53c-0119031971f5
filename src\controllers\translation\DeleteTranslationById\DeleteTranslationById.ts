import { Response } from 'express';
import { TranslationModule } from '../../../modules/translation';
import logger from '../../../config/logger';
import { ExtendedRequest } from '../../../types/extras';

export async function DeleteTranslationById (request: ExtendedRequest, response: Response):Promise<void>{
  const organization_id = request.organization_id as string;
  const { translationId } = request.query;
  logger.info('DeleteTranslationById Controller Called', {translationId});
  const translations = new TranslationModule(organization_id);
  if (translationId){
    const translated = await translations.DeleteTranslationById(translationId as string);
    response.status(200).send({ status: 1, data: translated });
  } else {
    logger.error('Unable to find TranslationId');
    response.status(500).send('Unable to find TranslationId');
  }
}

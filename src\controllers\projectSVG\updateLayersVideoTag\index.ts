import logger from '../../../config/logger';
import { ProjectSVGModule } from '../../../modules/projectSVG';
import { FileRequest } from '../../../types/extras';
import {  Response } from 'express';
interface File {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  destination: string;
  filename: string;
  path: string;
  size: number;
}
interface FileObject {
  video_tag: File[];
}

export default async function updateLayersVideoTag (
  request: FileRequest,
  response: Response,
):Promise<void>{
  const reqbody = request.body;
  const reqFiles = request.files as FileObject | undefined;
  const organization_id = request.headers.organization as string;
  const projectSVG = new ProjectSVGModule(
    request.body.project_id,
    organization_id,
  );
  let uploadedVideoUrl = '';

  if (reqFiles?.video_tag){
    if (reqFiles?.video_tag[0]){
      const fileName = reqFiles.video_tag[0].originalname;
      const path = reqFiles.video_tag[0].path;

      try {
        uploadedVideoUrl = await projectSVG.uploadVideoFiles(fileName, path, reqbody.scene_id.toString()) as string;
        console.log('Uploaded video URL:', uploadedVideoUrl);
      } catch (uploadError) {
        console.error('Error uploading file:', uploadError);
      }
      projectSVG
        .updateLayersVideo(request.body, uploadedVideoUrl as string)
        .then((res) => {
          response.send({ status: 1, data: res });
        })
        .catch((err) => {
          logger.error('Error in updateLayers', {message: err});
          response.send({status: 0, message: err});
        });
    }
  } else {
    // Empty videoTag
    console.log('empty videoTag');

    const nullVideoTag = null;
    projectSVG
      .updateLayersVideo(request.body, nullVideoTag)
      .then((res) => {
        response.send({ status: 1, data: res });
      })
      .catch((err) => {
        logger.error('Error in updateLayers', {message: err});
        response.send({status: 0, message: err});
      });
  }

}

import { trashModule } from '../../../modules/trash';
import logger from '../../../config/logger';

export async function getAllExpiredTrash (organization_id: string): Promise<any | null> {
  try {
    console.log('organization_id', organization_id);
    const trash = new trashModule(organization_id);
    const result = await trash.getAllExpiredTrash();
    return result;
  } catch (error) {
    logger.error('Error fetching trash data', { error });
    return null;
  }
}

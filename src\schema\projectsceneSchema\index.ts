import mongoose from 'mongoose';
import { deep_zoom_status, projectSceneType } from '../../types/projectScene';
export const projectscenesSchema = new mongoose.Schema({
  _id: String,
  organization_id: String,
  type: {
    type: String,
    enum: projectSceneType,
    default: projectSceneType.IMAGE,
  },
  name: String,
  background: {
    low_resolution: {
      type: String,
      immutable: false,
    },
    high_resolution: {
      type: String,
      immutable: false,
    },
    high_resolution_copy: {
      type: String,
      immutable: false,
    },
  },
  active: Boolean,
  info_icon: String,
  parent: String,
  info_text: String,
  root: Boolean,
  building_id: String,
  floor_ids: Array,
  clouds: Boolean,
  video: String,
  gsplat_link: String,
  category: String,
  frames: Object,
  order: Number,
  deep_zoom_status: {
    type: String,
    enum: deep_zoom_status,
  },
  deep_zoom_failed_info: String,
  position: {
    x: Number,
    y: Number,
    z: Number,
  },
  polar_angle: {
    max: Number,
    min: Number,
  },
  distance: {
    max: Number,
    min: Number,
  },
  auto_rotate: <PERSON><PERSON><PERSON>,
  minZoomLevel: Number,
  maxZoomLevel: Number,
});

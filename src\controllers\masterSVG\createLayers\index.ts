import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import mongoose from 'mongoose';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
import { MasterSVGModule } from './../../../modules/masterSVG/index';
export async function createLayers (
  request: FileRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  const masterSVG = new MasterSVGModule(organization_id);
  const randomId = new mongoose.Types.ObjectId().toString();
  let urlobject;
  if (request.files){
    urlobject =await UploadUnitplanFiles(request.files, masterSVG.storagepath+randomId);
  }
  masterSVG.createLayers(request.body, randomId, urlobject?.svgFile).then((res) => {
    response.send({status: 1, data: res});
  })
    .catch((err) => {
      logger.error('Error in createLayers', {message: err});
      response.send({status: 0, meassage: err});
    });

}

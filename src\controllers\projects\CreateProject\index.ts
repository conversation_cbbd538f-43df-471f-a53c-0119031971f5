import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import { Project, Theme } from '../../../types/projects';
import { ProjectModule } from '../../../modules/projects';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
// Import fs from 'fs';
import mongoose from 'mongoose';
import logger from '../../../config/logger';

export async function CreateProject (
  request: ExtendedRequest,
  response: Response,
): Promise<Project | void> {
  const organization_id = request.organization_id as string;
  // Pass organization_id when initializing ProjectModule
  const project = new ProjectModule(organization_id);
  const requestFiles = request.files;
  const id = new mongoose.Types.ObjectId();
  console.log(request.files);
  if (requestFiles === undefined) {
    response.status(400).json({ error: 'Image fields are required.' });
    return;
  }
  console.log('[[[[[[', request.body.experience);
  const experienceArray = request.body.experience.split(',');
  UploadUnitplanFiles(requestFiles, project.storagepath+id)
    .then((urlObject: { [key: string]: string }) => {
      const createProjectObj = {
        id: id,
        name: request.body.name,
        experience: experienceArray,
        property_type: request.body.property_type,
        city: request.body.city,
        country: request.body.country,
        organization_id: organization_id,
        project_thumbnail: urlObject.project_thumbnail,
        theme: request.body.theme || Theme.DARK,
        primary: request.body.primary,
        secondary: request.body.secondary,
        primary_text: request.body.primary_text,
        secondary_text: request.body.secondary_text,
        font_type: request.body.font_type || 'Roboto',
        font_url:
        `https://fonts.googleapis.com/css2?family=Roboto:ital,
        wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap`,
        tags: request.body.tags ? JSON.parse(request.body.tags) : [],
      };
      project
        .CreateNewProject(createProjectObj)
        .then(async (res) => {
          response.json({ status: 1, data: res });
        })
        .catch((error) => {
          logger.error('Error:', {message: error});
          response.json({ status: 0, message: error });
        });
    })
    .catch((error) => {
      logger.error('Error:', {message: error});
    });

}

import mongoose from 'mongoose';
import { priceCurrency, measurementType, unitStatus } from './units';

export type tableType = {
    drupalID: string,
    organization: string,
    projectId: string
}

export enum damacUnitStatus {
    SOLD = 'Sold',
    AVAILABLE = 'Available',
    NOT_AVAILABLE = 'Not Available'
}

export type DamacUnits = {
    unitNumber: string;
    area: number;
    towerName:string;
    floorName: number;
    status: damacUnitStatus;
    unitType: string;
    bedroomType:string;
    floorPlanImage?:string;
    floorPlan: string;
    price:number;
    currency: priceCurrency;
    unitImage: string;
    facing?:string;
    mirrored?: number;
    areaUnit?: measurementType;
};

export type UpdateUnit = {
    _id: mongoose.Types.ObjectId | string;
    name:string,
    status?:unitStatus,
    price?:string,
    measurement?:number
};

export type resultObject = {
    updatedUnits : Array<UpdateUnit>,
    unitsnotpresent: Record<string, DamacUnits>
}

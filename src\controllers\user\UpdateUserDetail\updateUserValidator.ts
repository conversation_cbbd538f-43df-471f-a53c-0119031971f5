import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

export const UpdateUserDetailsValidate = [
  body('_id')
    .notEmpty()
    .withMessage('User ID is required')
    .isString()
    .withMessage('User ID must be a string')
    .custom((value) => {
      // Additional custom validation for userId (e.g., check MongoDB ObjectId format)
      if (!value || typeof value !== 'string') {
        throw new Error('Invalid User ID');
      }
      return true;
    }),

  body('email')
    .optional()
    .isEmail()
    .withMessage('Invalid email format'),

  body('first_name')
    .optional()
    .isString()
    .withMessage('First name must be a string'),

  body('last_name')
    .optional()
    .isString()
    .withMessage('Last name must be a string'),

  body('phone_number')
    .optional()
    .isString()
    .withMessage('Phone number must be a string'),

  body('profilePicture')
    .optional()
    .isString()
    .withMessage('Profile picture URL must be a string'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ status: 0, errors: 'user ID is required'});
    }
    next();
  },
];
export default UpdateUserDetailsValidate;

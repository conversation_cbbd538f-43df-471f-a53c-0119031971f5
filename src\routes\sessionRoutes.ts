import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { CreateSession } from '../controllers/sessions/CreateSession';
import { GetSession } from '../controllers/sessions/GetSession';
import { GetSessions } from '../controllers/sessions/GetSessions';
import { UpdateSession } from '../controllers/sessions/UpdateSession';
import { EndSession } from '../controllers/sessions/EndSession';
import { StartSession } from '../controllers/sessions/StartSession';
import { GetAvailableSlots } from '../controllers/sessions/GetAvailableSlots';
import CreateSessionValidate from '../controllers/sessions/CreateSession/CreateSessionValidator';
import GetSessionValidate from '../controllers/sessions/GetSession/GetSessionValidator';
import { GetAnalytics } from '../controllers/sessions/GetAnalytics';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import GetAvailableSlotsValidate from '../controllers/sessions/GetAvailableSlots/GetAvailableSlotsValidator';
import updateSessionValidate from '../controllers/sessions/UpdateSession/updateSessionValidator';
import GetSessionsValidate from '../controllers/sessions/GetSessions/getSessionsValidator';
import StartSessionValidate from '../controllers/sessions/StartSession/StartSessionValidator';
import EndSessionValidate from '../controllers/sessions/EndSession/EndSessionValidator';
import GetSessionAnalyticsValidate from '../controllers/sessions/GetAnalytics/GetSessionAnalyticsValidator';
import { BookSession } from '../controllers/sessions/BookSession';
import BookSessionValidate from '../controllers/sessions/BookSession/BookSessionValidator';
import { GetAnonymousSessions } from '../controllers/sessions/GetAnonymousSessions';
import GetAnonymousSessionsValidator from '../controllers/sessions/GetAnonymousSessions/getAnonymousSessionsValidator';
import { AssignSession } from '../controllers/sessions/AssignSession';
import AssignSessionValidator from '../controllers/sessions/AssignSession/AssignSessionValidator';
import { CancelSession } from '../controllers/sessions/CancelSession';
import CancelSessionValidate from '../controllers/sessions/CancelSession/CancelSessionValidator';
import { checkAvailability } from '../controllers/sessions/checkAvailability';
import checkAvailabilitySlotsValidate from '../controllers/sessions/checkAvailability/checkAvailabilityValidator';
import extendSessionValidate from '../controllers/sessions/ExtendSession/ExtendSessionValidator';
import { ExtendSession } from '../controllers/sessions/ExtendSession';
import { AssignInstance } from '../controllers/sessions/AssignInstance';
import { RejoinSession } from '../controllers/sessions/RejoinSession';
import RejoinSessionValidator from '../controllers/sessions/RejoinSession/RejoinSessionValidator';
import AssignInstanceValidator from '../controllers/sessions/AssignInstance/AssignInstanceValidator';
import { EndSessionByTask } from '../controllers/sessions/EndSessionByTask';
import { CheckAvailableSessionSlots } from '../controllers/sessions/CheckAvailableSessionSlots';
import CheckAvailableSessionSlotsValidate from '../controllers/sessions/CheckAvailableSessionSlots/CheckAvailableSessionSlotsValidate';
import { getListOfSessionSlotsValidate } from '../controllers/sessions/GetListOfSessionSlots/GetListOfSessionSlotsValidator';
import { getListOfSessionSlots } from '../controllers/sessions/GetListOfSessionSlots';
const router = express.Router();
router.post(
  '/CreateSession',
  CreateSessionValidate,
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin', 'editor', 'reader']),
  CreateSession,
);
router.post(
  '/GetSession',
  GetSessionValidate,
  authMiddleware,
  organizationAccessMiddleware,
  GetSession,
);
router.post(
  '/GetAvailableSlots',
  GetAvailableSlotsValidate,
  authMiddleware,
  organizationAccessMiddleware,
  GetAvailableSlots,
);

router.post(
  '/checkAvailability',
  checkAvailabilitySlotsValidate,
  authMiddleware,
  organizationAccessMiddleware,
  checkAvailability,
);

router.post(
  '/ExtendSession',
  extendSessionValidate,
  authMiddleware,
  organizationAccessMiddleware,
  ExtendSession,
);

router.post(
  '/UpdateSession',
  updateSessionValidate,
  authMiddleware,
  organizationAccessMiddleware,
  UpdateSession,
);
router.post(
  '/GetSessions',
  GetSessionsValidate,
  authMiddleware,
  organizationAccessMiddleware,
  GetSessions,
);
router.post(
  '/StartSession',
  StartSessionValidate,
  authMiddleware,
  organizationAccessMiddleware,
  StartSession,
);
router.post(
  '/EndSession',
  EndSessionValidate,
  authMiddleware,
  organizationAccessMiddleware,
  EndSession,
);
router.post(
  '/CancelSession',
  CancelSessionValidate,
  authMiddleware,
  organizationAccessMiddleware,
  CancelSession,
);
router.get(
  '/GetAnalytics',
  GetSessionAnalyticsValidate,
  authMiddleware,
  organizationAccessMiddleware,
  GetAnalytics,
);
router.post(
  '/BookSession',
  BookSessionValidate,
  authMiddleware,
  organizationAccessMiddleware,
  BookSession,
);
router.get(
  '/GetAnonymousSessions',
  GetAnonymousSessionsValidator,
  authMiddleware,
  organizationAccessMiddleware,
  GetAnonymousSessions,
);
router.post(
  '/AssignSession',
  AssignSessionValidator,
  authMiddleware,
  organizationAccessMiddleware,
  AssignSession,
);
// For instance
router.post(
  '/AssignInstance',
  AssignInstanceValidator,
  authMiddleware,
  organizationAccessMiddleware,
  AssignInstance,
);

router.post(
  '/RejoinSession',
  RejoinSessionValidator,
  authMiddleware,
  organizationAccessMiddleware,
  RejoinSession,
);
router.post(
  '/EndSessionbyTask',
  EndSessionByTask,
);

router.post(
  '/CheckAvailableSessionSlots',
  CheckAvailableSessionSlotsValidate,
  authMiddleware,
  organizationAccessMiddleware,
  CheckAvailableSessionSlots,
);
router.post(
  '/GetListOfSessionSlots',
  getListOfSessionSlotsValidate,
  authMiddleware,
  organizationAccessMiddleware,
  getListOfSessionSlots,
);
export default router;

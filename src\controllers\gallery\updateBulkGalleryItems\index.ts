import logger from '../../../config/logger';
import { GalleryModule } from '../../../modules/gallery';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
export async function updateBulkGalleryItems (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  const galleryMod = new GalleryModule(project_id, organization_id);
  galleryMod.updateBulkGalleryItems(request.body)
    .then((res) => {
      response.status(201).json({ status: 1, message: res });
    })
    .catch((error: Error) => {
      logger
        .error('Error in updateBulkGalleryItems', {message: error});
      response
        .status(500)
        .json({ status: 0, error: error});
    });
}

import { Request, Response } from 'express';
import { AssetsModule } from '../../../modules/asset';
import logger from '../../../config/logger';

export async function updateAsset (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  const asset = new AssetsModule(project_id, organization_id);
  const { ...updateFields } = request.body;
  asset.UpdateAsset(updateFields).then((asstetItem) => {
    response.status(200).json({ status: 1, data: asstetItem });
  }).catch((error) => {
    logger.error('Asset Item not found', {message: error});
    response.status(404).json({ status: 0, error: 'Asset Item not found'+ error });
  });
}

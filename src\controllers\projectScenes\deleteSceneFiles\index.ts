import {Request, Response} from 'express';
import {ProjectSceneModule} from '../../../modules/projectScene';
import logger from '../../../config/logger';
export default async function deleteSceneFiles (request:Request, response:Response): Promise<void> {
  const reqbody = request.body;
  const organization_id = request.headers.organization as string;
  const projectScene = new ProjectSceneModule(reqbody.project_id, organization_id);
  projectScene.deleteSceneFiles(reqbody.scene_id, reqbody).then(async (res) => {
    response.send({status: 1, data: res});
  }).catch((error) => {
    logger.error('Error in deleteSceneFiles', {message: error});
    response.send({status: 0, message: error});
  });
}

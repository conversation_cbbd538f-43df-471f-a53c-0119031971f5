import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import { coordinateLinkType } from '../../../types/masterScene';

const createCoordinateValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('masterSceneId', 'Master Scene ID is required').notEmpty(),
  body('lat', 'lat - Coordinate Latitute is required').notEmpty(),
  body('lng', 'lng - Coordinate Longitude is required').notEmpty(),
  body('name', 'Coordinate Name is required').notEmpty(),
  body('linkType')
    .exists().withMessage('linkType is required')
    .custom((value: string, {req}) => {
    // Check the linktype is valid
      if (!Object.values(coordinateLinkType).includes(value as coordinateLinkType)) {
        throw new Error(`Invalid link type. Must be one of: ${Object.values(coordinateLinkType).join(', ')}`);
      }

      // Based on linkType validate
      switch (value) {
        case coordinateLinkType.PROJECT:
          if (!req.body.project_id) {
            throw new Error('project_id is required when linkType is project');
          }
          if (!req.body.scene_id) {
            throw new Error('scene_id is required when linkType is project');
          }
          delete req.body.link;
          break;

        case coordinateLinkType.MASTER:
          if (!req.body.scene_id) {
            throw new Error('scene_id is required when linkType is master');
          }
          delete req.body.project_id;
          delete req.body.link;
          break;

        case coordinateLinkType.EXTERNAL:
          if (!req.body.link) {
            throw new Error('link URL is required when linkType is external');
          }
          delete req.body.project_id;
          delete req.body.scene_id;
          break;
      }

      return true; // Validation passed

    }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createCoordinateValidate;

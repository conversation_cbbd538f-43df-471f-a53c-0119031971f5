import { Request, Response } from 'express';
import { UserModule } from '../../../modules/user';
import logger from '../../../config/logger';

export async function ResetPassword (
  req: Request,
  res: Response,
): Promise<void> {
  const User = new UserModule();
  const email = req.body.email;
  const refererHeader = req.headers.referer;
  const defaultUrl = 'https://dashboard-api-frontend-prod-l76o37h3dq-uc.a.run.app';
  const refererUrl = Array.isArray(refererHeader) ?
    refererHeader[0] : refererHeader || defaultUrl;

  User.ResetPassword(email, refererUrl)
    .then(() => {
      res.send({ status: 1, data: true });
    })
    .catch((error) => {
      logger.error('Error in ResetPassword', {message: error});
      res.send({ status: 0, error: error.message || error });
    });
}

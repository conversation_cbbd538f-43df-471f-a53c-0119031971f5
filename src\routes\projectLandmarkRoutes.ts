import express  from 'express';
import createLandmark from '../controllers/projectLandmarks/createLandmark';
import { getListofLandmark } from '../controllers/projectLandmarks/getListofLandmark';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import getListofLandmarksValidate from '../controllers/projectLandmarks/getListofLandmark/getListofLandmarkValidator';
import CreateProjectLandmarkValidate from
  '../controllers/projectLandmarks/createLandmark/createProjectLandmarkValidator';
import uploadHandler from '../helpers/uploadHandler';
import updateLandmark from '../controllers/projectLandmarks/updateLandmark';
import updateLandmarkFiles from '../controllers/projectLandmarks/updateLandmarkFiles';
import updateLandmarkValdiate from '../controllers/projectLandmarks/updateLandmark/updateLandmarkValidate';
import updateLandmarkFilesValidate from
  '../controllers/projectLandmarks/updateLandmarkFiles/updateLandmarkFilesValidator';
import getAllRoutes from '../controllers/projectLandmarks/getAllRoutes';
import getRoutes from '../controllers/projectLandmarks/getRoutes';
import getRoutesValidate from '../controllers/projectLandmarks/getAllRoutes/getRoutesValidator';
import { fetchLandmarks } from '../controllers/projectLandmarks/fetchLandmarks';
import { saveRoutes } from '../controllers/projectLandmarks/saveRoutes';
import saveRoutesValidator from '../controllers/projectLandmarks/saveRoutes/saveRoutesValidator';
import moveToTrashValidate from '../controllers/projectLandmarks/moveToTrash/moveToTrashValidator';
import { moveToTrash } from '../controllers/projectLandmarks/moveToTrash';
import restoreLandmarkValidate from '../controllers/projectLandmarks/restoreLandmark/restoreLandmarkValidator';
import { restoreLandmark } from '../controllers/projectLandmarks/restoreLandmark';
const router = express.Router();

router.post('/createLandmark',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'thumbnail' }, { name: 'icon' }], 'projectLandmarks/'),
  CreateProjectLandmarkValidate,
  createLandmark);
router.get('/getListofLandmark/:project_id',
  getListofLandmarksValidate,
  authMiddleware,
  organizationAccessMiddleware,
  getListofLandmark);
router.post('/updateLandmark',
  authMiddleware,
  organizationAccessMiddleware,
  updateLandmarkValdiate,
  updateLandmark);
router.post('/updateLandmarkFiles',
  authMiddleware,
  organizationAccessMiddleware,
  uploadHandler([{ name: 'thumbnail' }, { name: 'icon' }], 'projectLandmarks/'),
  updateLandmarkFilesValidate,
  updateLandmarkFiles);
router.post('/getAllRoutes',
  authMiddleware,
  organizationAccessMiddleware,
  getRoutesValidate,
  getAllRoutes);
router.post('/getRoutes',
  authMiddleware,
  organizationAccessMiddleware,
  getRoutesValidate,
  getRoutes);
router.post('/fetchNearbyLandmarks',
  authMiddleware,
  organizationAccessMiddleware,
  fetchLandmarks);
router.post('/saveRoutes',
  authMiddleware,
  organizationAccessMiddleware,
  saveRoutesValidator,
  saveRoutes);
router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreLandmark/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreLandmarkValidate,
  restoreLandmark,
);
export default router;

import { Request, Response } from 'express';
import { Units } from '../../../types/units';
import { UnitModule } from '../../../modules/units';
import logger from '../../../config/logger';

export async function UpdateUnit (request: Request, response: Response): Promise<Units | void> {
  const {project_id} = request.body;
  const {unit_id}  = request.params;
  const unit = new UnitModule(project_id);
  unit.updateUnit(request.body, unit_id).then((res) => {
    response.send({status: 1, data: res});
  })
    .catch((err) => {
      logger.error('Error in updateUnit', {message: err});
      response.send({status: 0, message: err});
    });
  return;
}

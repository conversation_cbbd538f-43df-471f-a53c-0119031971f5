import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import { svgManipulationHelper } from '../../../helpers/svgHelper';
import fs from 'fs';
import mongoose from 'mongoose';
import { MasterSVGModule } from '../../../modules/masterSVG';
import logger from '../../../config/logger';
import { Layers } from '../../../types/projectSVG';
import { updatePayload } from '../../../types/masterSVG';

interface File {
  fieldname: string;
  originalname: string;
  encoding: string;
  mimetype: string;
  destination: string;
  filename: string;
  path: string;
  size: number;
}

interface FileObject {
  [fieldname: string]: File[];
}

function isFileObject (obj: FileObject | File[] | undefined): obj is FileObject {
  return obj !== undefined && !Array.isArray(obj);
}

export default async function createSVG (
  request: FileRequest,
  response: Response,
): Promise<void> {
  interface createSVG {
    type: string;
    scene_id: number;
    organization_id: string;
    scene_type?: string;
    layers?: string;
  }

  const randomId = new mongoose.Types.ObjectId();

  const reqFiles: FileObject | File[] | undefined = request.files;  // Console.log(obj['svgFile'][0]['path'])
  const reqbody: createSVG = request.body;
  const organization_id = request.headers.organization as string;
  const masterSVG = new MasterSVGModule(organization_id);
  if (!request.files) {
    response.send({ status: 0, error: 'file not found' });
    return;
  }
  if (reqFiles){
    if (isFileObject(reqFiles) && reqbody.scene_type !== 'deep_zoom'){
      const pathValue = reqFiles.svgFile?.[0]?.path;
      const fileName =reqFiles.svgFile?.[0]?.originalname;
      fs.readFile(pathValue, 'utf8', async (error, data) => {
        if (error) {
          response.send({ status: 0, error: 'file not found' });
          return;
        }
        if (!request.files) {
          response.send({ status: 0, error: 'file not found' });
          return;
        }
        const svgdata: { [key: string]: string | object } =
            await svgManipulationHelper(data, reqbody.type, randomId);
        const url: string = await masterSVG.UploadFiles(
          fileName,
          svgdata.modifiedSvg,
          reqbody.scene_id.toString(),
        );
        await fs.promises.rm('svg/', { recursive: true });
        masterSVG
          .createSVG({
            _id: randomId,
            scene_id: reqbody.scene_id,
            svg_url: url,
            layers: svgdata.layers,
            type: reqbody.type,
          })
          .then((res) => {
            response.send({ status: 1, data: res });
          })
          .catch((err: string) => {
            logger.error('Error in CreateSVG', {message: err});
            response.send({ status: 0, message: err });
          });
      });
    } else {
      console.log('Inside Layers For deep_zoom');

      const layers = reqbody.layers as string;
      const data = JSON.parse(layers);
      const arrayOfObjects: Layers[] = Object.values(data);
      console.log('array of obj', arrayOfObjects);

      const layersObject = arrayOfObjects.reduce(
        (obj: object, item: Layers) => {
          const randomIdForLayers = new mongoose.Types.ObjectId().toString();

          item.layer_id = randomIdForLayers;

          return Object.assign(obj, {
            [randomIdForLayers]: item,
          });
        },
        {},
      );

      // Console.log('1', layersObject);

      const outputFrom_upload = await masterSVG.uploadGtagToFirebaseSvg(
        layersObject as updatePayload,
      );

      console.log('output:::::::::', outputFrom_upload);

      await masterSVG.validateLayers(outputFrom_upload).then((res) => {
        console.log('response', res);
        masterSVG
          .createSVG({
            _id: randomId,
            scene_id: reqbody.scene_id,
            layers: res,
            type: reqbody.type,
          })
          .then((outputFrom_validatelayers) => {
            response.send({ status: 1, data: outputFrom_validatelayers });
          })
          .catch((err: string) => {
            console.error('Error in createSVG', err);
            response.send({ status: 0, message: 'Error in createSVG', Error: err });
          });

      }).catch((error: string) => {
        console.error('Error in validateLayers', error);
        response.send({ status: 0, message: 'Error in validateLayers', Error: error });
      });
    }
  }

  // Const upload = multer({ dest: 'svg/' }).single('svgFile');
  // Upload(request, response, () => {

  // });
}

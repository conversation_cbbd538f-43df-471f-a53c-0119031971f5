import { ExtendedRequest } from '../../../types/extras';
import { ProjectLandmarkModule } from '../../../modules/projectLandmark';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreLandmark (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.params;
  const landmark = new ProjectLandmarkModule(project_id, organization_id);
  const trash_id = request.body.trash_id;
  await landmark
    .restoreLandmark(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Landmark got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreLandmark', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore Landmark: '+ error });
    });
}

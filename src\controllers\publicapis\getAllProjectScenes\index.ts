import { Request, Response } from 'express';

import {ProjectSceneModule} from '../../../modules/projectScene';
import { ProjectSVGModule } from '../../../modules/projectSVG';
import logger from '../../../config/logger';

export async function getAllProjectScenes (
  request: Request,
  response: Response,
): Promise<void> {

  const {project_id, organization_id} = request.params;
  const type = request.query.type as string;
  const parent = request.query.parent as string;
  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'organization_id not found'});
    return;
  }
  //   Pass organization_id in this ProjectModule
  const projectScene = new ProjectSceneModule(project_id, organization_id);

  const projectScenes = await projectScene.getAllScenes(type, parent);
  const project = new ProjectSVGModule(project_id, organization_id);
  if (projectScenes) {
    const keys = Object.keys(projectScenes);
    const promises = keys.map(async (key) => {
      const svgData = await project.getSvgById(key);
      if (svgData === null || Object.keys(svgData).length === 0) {
        return;
      }
      projectScenes[key].svgData = svgData;
    });
    await Promise.all(promises).then(() => {
      response.status(200).json({ status: 1, data: projectScenes});
    });
  } else {
    logger.error('Error in getAllProjectScenes');
    response.status(500).json({ status: 0, error: 'scene not found' });
  }
}

import { Request, Response } from 'express';
import { ProjectLandmarkModule } from '../../../modules/projectLandmark';
import logger from '../../../config/logger';

export async function getListofProjectLandmark (
  request: Request,
  response: Response,
): Promise<void> {
  const {  project_id, organization_id } = request.params;
  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'organization_id not found'});
    return;
  }
  const projectLandmark = new ProjectLandmarkModule(project_id, organization_id);
  const projectLandmarkData = await projectLandmark.getListofLandmark();
  if (projectLandmarkData) {
    response.status(200).json({ status: 1, data: projectLandmarkData});
  } else {
    logger.error('landmark not found');
    response.status(500).json({ status: 0, error: 'landmark not found' });
  }
}

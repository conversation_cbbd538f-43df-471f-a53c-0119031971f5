import { Request, Response } from 'express';
import logger from '../../../config/logger';
import { GetRequest } from '../../../helpers/larkxr';

export async function GetApplicationList (request: Request, response: Response): Promise<void> {
  try {
    const APIUrl = process.env.LARKXR_ADMIN_HOST as string+'appli/getAppliList';
    GetRequest(APIUrl).then((data) => {
      response.status(200).json({ status: 1, data: data });
    })
      .catch((error) => {
        response.status(404).json({ status: 0, error: error });
      });
  } catch (error) {
    logger.error('Internal Server Error', {message: error});
    response.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

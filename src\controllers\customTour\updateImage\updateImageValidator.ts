import {  Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';
import { ExtendedRequest } from '../../../types/extras';

const updateImageValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('tour_id', 'Tour ID is required').notEmpty(),
  body('image_id', 'Image ID is required').notEmpty(),

  (req: ExtendedRequest, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default updateImageValidate;

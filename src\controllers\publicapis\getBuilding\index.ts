import { Request, Response } from 'express';
import { buildingModule } from '../../../modules/building';
import logger from '../../../config/logger';

export async function GetBuilding (request: Request, response: Response): Promise<void> {
  try {
    const project_id = request.query.project_id as string;
    const building_id = request.query.building_id as string;
    const organization_id = request.query.building_id as string;
    const building = new buildingModule(project_id, organization_id);
    const buildingData = await building.GetBuilding(building_id);

    if (buildingData) {
      response.status(200).json({ status: 1, data: buildingData });
    } else {
      response.status(500).json({ status: 0, error: 'Building not found' });
    }
  } catch (error) {
    logger.error('Error in GetBuilding', {message: error});

    response.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

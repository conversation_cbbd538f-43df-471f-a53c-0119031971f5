import express from 'express';
import { generateShortUrl } from '../controllers/shortenUrl/shortUrls';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { shorterUrlValidator } from '../controllers/shortenUrl/shortUrls/shorterUrlValidator';
import { getOrgUrl } from '../controllers/shortenUrl/redirectUrl';

const router = express.Router();
router.post('/url',
  authMiddleware,
  shorterUrlValidator,
  generateShortUrl,
);

router.get('/:_id',
  getOrgUrl,
);

export default router;

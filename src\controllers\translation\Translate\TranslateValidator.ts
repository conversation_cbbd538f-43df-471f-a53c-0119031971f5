import { Request, Response, NextFunction } from 'express';
import { validationResult, query } from 'express-validator';
import { SupportedLanguages } from '../../../types/projects';
import logger from '../../../config/logger';

const TranslateValidator = [
  query('targetLanguageCode', 'Target Language Code is required').notEmpty(),
  query('targetLanguageCode', 'Target Language Code is not supported').isIn(Object.values(SupportedLanguages)),
  query('sourceLanguageCode', 'Source Language Code is required').notEmpty(),
  query('sourceLanguageCode', 'Source Language Code is not supported').isIn(Object.values(SupportedLanguages)),
  query('text', 'Target Language Code is required').notEmpty().isString(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Error in TranslateValidator', errors);
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default TranslateValidator;

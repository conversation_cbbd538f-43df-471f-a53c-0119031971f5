import { validationResult, header, body, check } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
interface UploadedFiles {
  file: Express.Multer.File[];
}

const UpdateUnitByMetaValidator = [
  check('Authorization', 'apikey is required').notEmpty(),
  header('project_id', 'project_id is required').notEmpty(),
  body('data', 'Data must be an array').isArray(),
  body('data.*')
    .exists().withMessage('Object is required')
    .bail()
    .custom((value) => {
    // Define the allowed fields outside metadata
      const allowedFields = ['status', 'metadata', 'price']; // Add allowed fields here
      // Check if any field other than allowed fields is present
      const invalidFields = Object.keys(value).filter((field) => !allowedFields.includes(field));
      if (invalidFields.length > 0) {
        throw new Error(`Invalid field(s) found: ${invalidFields.join(', ')}`);
      }
      return true;
    }),
  body('data.*.metadata')
    .exists().withMessage('Metadata is required')
    .bail()
    .custom((value) => {
      if (Object.keys(value).length === 0) {
        throw new Error('Metadata should not be empty');
      }
      return true;
    }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();

  },
];

const uploadCsvValidator = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;
  console.log(files);
  if (files) {
    if (!files.file) {
      res
        .status(400)
        .json({ error: 'CSV File is required.' });
    } else {
      const requiredTextFields = [
        'project_id',
      ];

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.headers),
      );

      if (missingTextFields.length > 0) {
        res.status(400).json({
          error: `Missing text fields: ${missingTextFields.join(', ')}`,
        });
      } else {
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export {
  UpdateUnitByMetaValidator,
  uploadCsvValidator};

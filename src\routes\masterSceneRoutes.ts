import express from 'express';
import createScene from '../controllers/masterScenes/createScene';
import {getScene} from '../controllers/masterScenes/getScene';
import {getAllScenes} from '../controllers/masterScenes/getAllScenes';
import { CreateCoordinate } from '../controllers/masterScenes/createCoordinate';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { updateCoordinateSettings } from '../controllers/masterScenes/updateCoordinateSettings';
import { UpdateCoordinate } from '../controllers/masterScenes/updateCoordinate';
import { deleteCoordinate } from '../controllers/masterScenes/deleteCoordinate/index';
import getSceneValidate from '../controllers/masterScenes/getScene/getSceneValidator';
import getAllScenesValidate from '../controllers/masterScenes/getAllScenes/getAllScenesValidator';
import createCoordinateValidate from '../controllers/masterScenes/createCoordinate/createCoordinateValidator';
import updateCoordinateValidate from '../controllers/masterScenes/updateCoordinate/updateCoordinateValidate';
import deleteCoordinateValidate from '../controllers/masterScenes/deleteCoordinate/deleteCoordinateValidator';
import uploadHandler from '../helpers/uploadHandler';
import updateScene from '../controllers/masterScenes/updateScene';
import {moveToTrash} from '../controllers/masterScenes/moveToTrash';
import {restoreScenes} from '../controllers/masterScenes/restoreScenes';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import CreateSceneValidate from '../controllers/masterScenes/createScene/createSceneValidator';
import updateSceneAPI from '../controllers/masterScenes/updateSceneAPI';
import UpdateSceneValidate from '../controllers/masterScenes/updateScene/updateSceneValidate';
import UpdateSceneFilesValidate from '../controllers/masterScenes/updateSceneFile/updateSceneFileValidator';
import updateSceneFiles from '../controllers/masterScenes/updateSceneFile';
import { updateBulkSceneFrames } from '../controllers/masterScenes/updateSceneFrame';
import updateBulkSceneFrameValidate from '../controllers/masterScenes/updateSceneFrame/updateBulkSceneFrameValidator';
const router = express.Router();

router.post(
  '/createScene',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler(
    [
      { name: 'info_icon' },
      { name: 'lowRes' },
      { name: 'highRes' },
      { name: 'video' },
      { name: 'gsplat' },
      { name: 'file' },
    ],
    'masterScenes/',
  ),
  CreateSceneValidate,
  createScene,
);
router.get('/getScene/:scene_id', getSceneValidate,
  authMiddleware,
  organizationAccessMiddleware, getScene);
router.get('/getAllScenes', getAllScenesValidate,
  authMiddleware,
  organizationAccessMiddleware, getAllScenes);
router.post('/createCoordinates',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  createCoordinateValidate,
  CreateCoordinate);
router.post('/updateCoordinate',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  updateCoordinateValidate,
  UpdateCoordinate);
router.post('/deleteCoordinate',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  deleteCoordinateValidate,
  deleteCoordinate);
router.post('/updateCoordinateSettings',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  updateCoordinateSettings);
router.post(
  '/updateScene',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  UpdateSceneValidate,
  updateScene,
);
router.post(
  '/updateSceneFiles',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler(
    [
      { name: 'info_icon' },
      { name: 'lowRes' },
      { name: 'highRes' },
      { name: 'video' },
    ],
    'masterScenes/',
  ),
  UpdateSceneFilesValidate,
  updateSceneFiles,
);
router.post('/moveToTrash',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrash,
);
router.post('/restoreScenes',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreScenes,
);
router.post(
  '/updateScene/api',
  updateSceneAPI,
);
router.post(
  '/updateBulkSceneFrames',
  authMiddleware,
  organizationAccessMiddleware,
  updateBulkSceneFrameValidate,
  updateBulkSceneFrames,
);
export default router;

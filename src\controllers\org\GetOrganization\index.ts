import { Response } from 'express';

import { OrganizationModule } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import { Organization } from '../../../types/organization';
import logger from '../../../config/logger';
export default async function GetOrganization (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization = new OrganizationModule();
  const IsAuthenticated = request.IsAuthenticated;
  const organization_id = request.organization_id;
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }
  if (!IsAuthenticated) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }
  await organization
    .GetOrganization(organization_id)
    .then((organizations: Organization) => {
      if (organizations) {
        response.send({ status: 1, data: organizations });
      } else {
        response.send({ status: 0, error: 'no organisation found' });
      }
    })

    .catch((error) => {
      logger.error('error while getting organization', {message: error});
      response.send({ status: 0, error: 'error while getting organization' + error});
    });
  return;
}

import { VirtualTourModule } from '../../../modules/virtualTour';
import { VirtualTour } from '../../../types/virtualTour';
import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';

export async function UpdateTour (
  request: FileRequest,
  response: Response,
): Promise<VirtualTour | void> {
  const {organization_id} = request;

  if (!organization_id) {
    logger.error('Organization ID not found in request headers', { headers: request.headers });
    response.status(500).json({ status: 0, error: 'Organization ID not found' });
    return;
  }

  const { project_id, tour_id} = request.body;
  if (!project_id || !tour_id) {
    logger.error('Project ID or Tour ID missing in request', { project_id, tour_id });
    response.status(400).json({ status: 0, error: 'Project ID and Tour ID are required' });
    return;
  }

  const requestFiles = request.files;

  const virtualTour = new VirtualTourModule(organization_id, project_id);

  let urlObject: { [key: string]: string }={};
  if (requestFiles){
    urlObject = await UploadUnitplanFiles(requestFiles, virtualTour.storagepath + tour_id);
  }

  const payload = {
    ...request.body,
    ...urlObject,
  };

  try {
    const virtualTourData = await virtualTour.UpdateTour(tour_id, payload);

    if (!virtualTourData) {
      response.status(404).json({ status: 0, error: 'Tour not found' });
    }

    response.status(200).json({ status: 1, data: virtualTourData });
  } catch (error) {
    logger.error('Error in UpdateTour', {error});
    response.status(500).json({ status: 0, error: `Error updating tour: ${error}` });
  }
}

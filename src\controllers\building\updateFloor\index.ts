import { Request, Response } from 'express';
import { buildingModule } from '../../../modules/building';
import { Building } from '../../../types/building';
import logger from '../../../config/logger';

export async function UpdateFloor (request: Request, response: Response): Promise<Building | void> {
  const {building_id, floor_id, project_id, organization_id}  = request.params;
  const building = new buildingModule(project_id, organization_id);
  building.UpdateFloor(request.body, building_id, floor_id).then((res) => {
    response.send({status: 1, data: res});
  })
    .catch((err) => {
      logger.error('Internal Server Error', {message: err});
      response.send({status: 0, message: err});
    });
  return;
}

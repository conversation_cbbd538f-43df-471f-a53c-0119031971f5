import { Response } from 'express';
import { customTourModule } from '../../../modules/customTour';
import { ExtendedRequest } from './../../../types/extras';
import logger from '../../../config/logger';

export async function deleteImage (request: ExtendedRequest, response: Response): Promise<void> {
  const organization_id = request.organization_id as string;
  const tour_id = request.body.tour_id;
  const project_id = request.body.project_id;
  const customTourMod = new customTourModule(organization_id, tour_id, project_id);
  customTourMod.deleteImage(request.body.image_id).then((message) => {
    response.status(200).json({status: 1, data: message});
  })
    .catch((err) => {
      logger
        .error('Error in deleteImage', {message: err});
      response.status(404).json({status: 0, error: err});
    });
}

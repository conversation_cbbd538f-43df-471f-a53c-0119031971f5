import { Response } from 'express';
import { customTourModule } from '../../../modules/customTour';
import { ExtendedRequest } from './../../../types/extras';
import logger from '../../../config/logger';

export async function getImages (request: ExtendedRequest, response: Response): Promise<void> {
  try {
    const organization_id = request.organization_id as string;
    const tour_id = request.query.tour_id as string;
    const project_id =  request.query.project_id as string;
    const customTourMod = new customTourModule(organization_id, tour_id, project_id);
    const ImagesData = await customTourMod.getImages();

    if (ImagesData) {
      response.status(200).json({ status: 1, data: ImagesData });
    } else {
      response.status(404).json({ status: 0, error: 'Images not found' });
    }
  } catch (error) {
    logger
      .error('Error in getImages', {message: error});
    response.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

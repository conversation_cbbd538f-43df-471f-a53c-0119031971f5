module.exports = {
  env: {
    browser: true,
    es2021: true,
  },
  extends: ['eslint:recommended', 'plugin:@typescript-eslint/recommended'],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint'],
  rules: {
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/explicit-module-boundary-types': 'error',
    // 'no-console': 'warn',
    'no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    indent: ['error', 2, { SwitchCase: 1 }],
    quotes: ['error', 'single'],
    semi: ['error', 'always'],
    'comma-dangle': ['error', 'always-multiline'],
    'eol-last': ['error', 'always'],
    'no-multiple-empty-lines': ['error', { max: 1, maxEOF: 0 }],
    'arrow-parens': ['error', 'always'],
    // 'no-underscore-dangle': 'error',
    '@typescript-eslint/no-shadow': ['error'],
    'complexity': ['warn', 15],
    'max-depth': ['error', 4],
    'max-len': ['error', { code: 135 }],
    'max-len': ['warn', { code: 120 }],
    'space-before-function-paren': ['error', 'always'],
    '@typescript-eslint/quotes': ['error', 'single'],
    'key-spacing': ['error', { beforeColon: false }],
    'keyword-spacing': ['error', { before: true }],
    'brace-style': 'error',
    'no-trailing-spaces': 'error',
    'no-debugger': 'warn',
    '@typescript-eslint/no-inferrable-types': 'off',
    '@typescript-eslint/no-namespace': 'off',
    'max-nested-callbacks': [2, 3],
    'prefer-const': ['error'],
    'no-use-before-define': ['error'],
    'constructor-super': ['error'],
    'array-callback-return': ['error'],
    'for-direction': ['error'],
    'no-cond-assign': ['error'],
    'no-const-assign': ['error'],
    'no-constant-condition': ['error'],
    'no-dupe-else-if': ['error'],
    'no-dupe-keys': ['error'],
    'no-duplicate-case': ['error'],
    'no-irregular-whitespace': ['error'],
    'no-unexpected-multiline': ['error'],
    'valid-typeof': ['error'],
    'block-scoped-var': ['error'],
    'capitalized-comments': ['error'],
    'consistent-return': ['error'],
    'consistent-this': ['error'],
    curly: ['error'],
    'default-param-last': ['error'],
    'dot-notation': ['error'],
    eqeqeq: ['error'],
    'no-confusing-arrow': ['error'],
    'no-else-return': ['error'],
    'no-empty': ['error'],
    'no-eq-null': ['error'],
    'no-extra-semi': ['error'],
    'no-global-assign': ['error'],
    'no-implicit-globals': ['error'],
    'no-mixed-operators': ['error'],
    'no-multi-str': ['error'],
    'spaced-comment': ['error'],
    'vars-on-top': ['error'],
    'arrow-spacing': ['error'],
    'block-spacing': ['error'],
    'comma-spacing': ['error'],
    'comma-style': ['error'],
    'computed-property-spacing': ['error'],
    'switch-colon-spacing': ['error'],
  },
};

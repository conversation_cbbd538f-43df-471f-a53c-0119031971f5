import express  from 'express';
const  router = express.Router();
import { createCommunity } from '../controllers/community/createCommunity';
import { getCommunities } from '../controllers/community/getCommunities';
import { getSyncUpdata } from '../controllers/community/getSyncUpdata';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import getSyncUpdataValidate from '../controllers/community/getSyncUpdata/getSyncUpdataValidator';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import uploadHandler from '../helpers/uploadHandler';
import CreateCommunityValidator from '../controllers/community/createCommunity/createCommunityValidator';
import { updateCommunity } from '../controllers/community/updateCommunity';
import UpdateCommunityValidator from '../controllers/community/updateCommunity/updateCommunityValidator';
import { moveToTrash } from '../controllers/community/moveToTrash';
import moveToTrashValidate from '../controllers/community/moveToTrash/moveToTrashValidator';
import { restoreCommunity } from '../controllers/community/restoreCommunity';
import restoreCommunityValidate from '../controllers/community/restoreCommunity/restoreCommunityValidator';

router.post('/createCommunity',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'thumbnail' }], 'output'),
  CreateCommunityValidator,
  createCommunity);
router.post('/updateCommunity',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'thumbnail' }], 'output'),
  UpdateCommunityValidator,
  updateCommunity);
router.get('/getCommunities/:project_id', authMiddleware, organizationAccessMiddleware, getCommunities);
router.post('/getSyncUpdata', getSyncUpdataValidate, authMiddleware, organizationAccessMiddleware, getSyncUpdata);
router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreCommunity/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreCommunityValidate,
  restoreCommunity,
);

export default router;

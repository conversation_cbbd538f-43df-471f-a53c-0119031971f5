import app  from '../../../app';
import request from 'supertest';
describe('Get Count for Project Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/getCountforProject?organization_id=8X3plU&project_id=65701718b5063acb09dbf6e4')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual([]);
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getCountforProject'
      +'?organization_id=8X3pU&project_id=65701718b5063acb09dbf6e4')   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(500);
    expect(res.body).toHaveProperty('error');
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getCountforProject'
      +'?organization_id=8X3plU&project_id=65701718b506b09dbf6')   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(500);
    expect(res.body).toHaveProperty('error');
  });
});

import { Request, Response, NextFunction } from 'express';
import { body, query, validationResult } from 'express-validator';
import { SupportedLanguages } from '../../../types/translation';
import logger from '../../../config/logger';

const AddTranslationValidator = [
  // Validate translations object
  query('sourceLanguageCode', 'Source Language Code is required').notEmpty(),
  query('sourceLanguageCode', 'Source Language Code is not supported').isIn(Object.values(SupportedLanguages)),
  query('text', 'Target Language Code is required').notEmpty().isString(),
  body('translations')
    .notEmpty().withMessage('Translations object is required')
    .isObject().withMessage('Translations must be an object')
    .custom((translations) => {
      // Ensure translations object has English text
      if (!translations.en || typeof translations.en !== 'string') {
        throw new Error('English (en) translation is required and must be a string');
      }

      // Validate each language code
      Object.keys(translations).forEach((languageCode) => {
        // Skip if translation is undefined or null
        if (translations[languageCode] === undefined || translations[languageCode] === null) {
          return;
        }

        // Ensure translation is a string
        if (typeof translations[languageCode] !== 'string') {
          throw new Error(`Translation for ${languageCode} must be a string`);
        }

        if (!Object.values(SupportedLanguages).includes(languageCode as SupportedLanguages)) {
          throw new Error(`Unsupported language code: ${languageCode}`);
        }
      });

      return true;
    }),

  // Validation result handler
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      logger.error('Error in TranslateAndStoreValidator', errors.array());
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }

    next();
  },
];

export default AddTranslationValidator;

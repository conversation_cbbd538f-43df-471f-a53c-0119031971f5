import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import logger from '../../../config/logger';
import { MasterSVGModule } from './../../../modules/masterSVG/index';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const svg = new MasterSVGModule(organization_id);
  const svg_id = request.body.svg_id;
  const timeStamp = request.body.timeStamp;

  await svg
    .moveToTrash(svg_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving project svgs to trash: '+ error });
    });
}

import {masterscenesSchema} from '../../schema/masterscenesSchema';
import { coordinate, masterScene, transformedMasterScene} from '../../types/masterScene';
import mongoose from 'mongoose';
import { Models } from '../../types/extras';
import { trashType } from '../../types/trash';
// Import { masterSVG } from '../../types/masterSVG';
import { trashModule } from '../trash/index';
import { MasterSVGModule } from '../masterSVG';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';
import logger from '../../config/logger';
import { updateProjectSceneObj, projectSceneType,
  projectScene, deep_zoom_status, updateBulkSceneType } from '../../types/projectScene';
import { masterSVG } from '../../types/masterSVG';
// Import { response } from 'express';

// Import {storageUpload} from '../../helpers/storageUpload';

export class MasterSceneModule{
  private urlObject: { [key: string]: string } = {};
  private model: mongoose.Model<projectScene>;
  public storagepath:string;
  constructor (organization_id:string) {
    this.model=mongoose.model<projectScene>(`${organization_id}${Models._MASTER_SCENES}`, masterscenesSchema);
    this.storagepath='CreationtoolAssets/'+organization_id+'/master_scenes/';
  }
  // Public async UploadFiles (
  //   RequestFiles:
  //     | { [fieldname: string]: Express.Multer.File[] }
  //     | Express.Multer.File[],
  //   Scene_id:string,
  // ): Promise<{ [key: string]: string }> {
  //   Return new Promise<{ [key: string]: string }>( (resolve, reject) => {
  //     Try {
  //       Const uploadPromises = Object.values(requestFiles).map(
  //         (file: Express.Multer.File[]) => {
  //           Return new Promise<void>((innerResolve, innerReject) => {
  //             Const uploadOptions = {
  //               Destination: this.storagepath+scene_id+'/'+file[0].originalname,
  //             };
  //             StorageUpload(uploadOptions, file[0].path).then((thumbnailUrl) => {
  //               This.urlObject[file[0].fieldname] = thumbnailUrl;
  //               Return innerResolve();
  //             })
  //               .catch((err) => {
  //                 Return innerReject(err);
  //               });
  //           });
  //         },
  //       );

  //       Promise.all(uploadPromises).then(() => {
  //         Resolve(this.urlObject);
  //       });
  //     } catch (error) {
  //       Reject(error);
  //     }
  //   });
  // }
  public async createScene (payload: object): Promise<projectScene | void> {
    logger.info('createScene Called', {payload: payload});
    return new Promise((resolve, reject) => {
      const masScene = new this.model(payload);
      masScene
        .save()
        .then((res) => {
          logger.info('createScene Successfull', {response: res});
          resolve(res);
        })
        .catch((err) => {
          logger.error('An unexpected error in createScene', {message: err});
          reject(err);
        });
    });
  }
  public async getScene (scene_id: string):Promise<projectScene> {
    logger.info('getScene Called', {scene_id: scene_id});
    const query = {
      _id: scene_id,
    };
    const masterScenes = await this.model.findOne(query);
    logger.info('getScene Successfull', {masterScenes: masterScenes});
    return masterScenes as projectScene;
  }
  public async getAllScenes (
    type: string = '',
    parent: string = '',
  ): Promise<transformedMasterScene | null> {
    logger.info('getAllScenes Called', {type: type, parent: parent});
    const masterScenes =
      type && parent
        ? await this.model.aggregate([
          { $match: { type: type, parent: parent } },
          {
            $addFields: {
              hasOrder: {
                $cond: {
                  if: { $eq: [{ $type: '$order' }, 'missing'] },
                  then: 0,
                  else: 1,
                },
              },
            },
          },
          { $sort: { hasOrder: -1, ['order']: 1 } },
          { $unset: 'hasOrder' },
        ])
        : await this.model.find({ type: { $ne: 'rotatable_image_frame' } });
    return masterScenes.reduce((acc, scene) => {
      acc[scene._id] = { sceneData: scene, svgData: {} };
      logger.info('getAllScenes Successfull', {acc: acc});
      return acc;
    }, {} as transformedMasterScene);
  }
  public async createCoordinate (coordinates: coordinate, sceneId: string): Promise<projectScene | void> {
    logger.info('createCoordinate Called', {coordinates: coordinates, sceneId: sceneId});
    try {
      const scene = await this.model.findOne({ _id: sceneId });
      if (!scene) {
        logger.error('Cannot find master scene');
        throw new Error('Cannot find master scene');
      }
      if (!scene.type.includes(projectSceneType.EARTH)) {
        logger.error('Cannot add coordinates to this scene. This is not an Earth scene.');
        throw new Error('Cannot add coordinates to this scene. This is not an Earth scene.');
      }
      scene.coordinates?.push(coordinates);
      const new_coordinate = await scene.save();
      if (new_coordinate) {
        logger.info('createCoordinate Successfull', {new_coordinate: new_coordinate});
        return new_coordinate as projectScene;
      }
      logger.error('Error in creating coordinate.');
      throw new Error('Error in creating coordinate.');

    } catch (error) {
      logger.error('Error in createCoordinate', {message: error});
      console.error('Error:', error);
      throw error; // Rethrow the error to propagate it to the caller
    }
  }
  public async updateCoordinate (sceneId: string, coordinateId: string, coordinates: coordinate):
   Promise<projectScene | null> {
    logger.info('updateCoordinate Called', {sceneId: sceneId, coordinateId: coordinateId, coordinates: coordinates});
    const existingScene = await this.model.findOne({ _id: sceneId }); // Fetch existing session doc
    if (!existingScene) {
      logger.error('Cannot find master scene');
      throw new Error('Cannot find master scene');
    }
    if (!existingScene.type.includes(projectSceneType.EARTH)) {
      logger.error('Cannot add coordinates Settings to this Scene. This is not an Earth scene.');
      throw new Error('Cannot add coordinates Settings to this Scene. This is not an Earth scene.');
    }
    const coordinateExists = await this.model.findOne({ _id: sceneId, 'coordinates._id': coordinateId });
    if (!coordinateExists) {
      logger.error('Coordinate with specified ID not found in this scene.');
      throw new Error('Coordinate with specified ID not found in this scene.');
    }

    const updatedScene = await this.model.findOneAndUpdate(
      { _id: sceneId, 'coordinates': { $elemMatch: { '_id': coordinateId } } },      {
        $set: {
          'coordinates.$.link': coordinates.link,
          'coordinates.$.scene_id': coordinates.scene_id,
          'coordinates.$.project_id': coordinates.project_id,
          'coordinates.$.lat': coordinates.lat,
          'coordinates.$.lng': coordinates.lng,
          'coordinates.$.linkType': coordinates.linkType,
          'coordinates.$.name': coordinates.name,
          '$coordinates.$active': coordinates.active,
        },
      },
      { new: true },
    );
    if (updatedScene) {
      logger.info('updateCoordinate Successfull', {updatedScene: updatedScene});
      return updatedScene as projectScene;
    }
    logger.error('Error in updating coordinate settings.');
    throw new Error('Error in updating coordinate settings');
    return null;
  }
  public async deleteCoordinate (sceneId: string, coordinateId: string): Promise<projectScene | void> {
    logger.info('deleteCoordinate Called', {sceneId: sceneId, coordinateId: coordinateId});
    try {
      const updatedScene = await this.model.findOneAndUpdate(
        { _id: sceneId },
        { $pull: { 'coordinates': { _id: coordinateId } } },
        { new: true },
      );

      if (updatedScene) {
        logger.info('deleteCoordinate Successfull', {updatedScene: updatedScene});
        return updatedScene;
      }
      logger.error('Error in deleting coordinate from Master Scene');
      throw new Error('Error in deleting coordinate from Master Scene');

    } catch (error) {
      logger.error('Error in deleteCoordinate', {message: error});
      throw new Error('Internal Server Error' + error);
    }
  }
  public async updateCoordinateSettings (sceneId: string, coordinateSettings: object): Promise<projectScene | null>{
    logger.info('updateCoordinateSettings Called', {sceneId: sceneId, coordinateSettings: coordinateSettings});
    const existingScene = await this.model.findOne({ _id: sceneId }); // Fetch existing session doc
    if (!existingScene) {
      logger.error('Cannot find master scene');
      throw new Error('Cannot find master scene');
    }
    if (!existingScene.type.includes(projectSceneType.EARTH)) {
      logger.error('Cannot add coordinates Settings to this Scene. This is not an Earth scene.');
      throw new Error('Cannot add coordinates Settings to this Scene. This is not an Earth scene.');
    }
    const scene = await this.model.findOneAndUpdate(
      { _id: sceneId, type: 'Earth'},
      {
        $set: { coordinateSettings: { ...coordinateSettings } },
      },
      { new: true }, // Return the updated document
    );
    if (scene) {
      logger.info('updateCoordinateSettings Successfull', {scene: scene});
      return scene as projectScene;
    }
    logger.error('Error in updating coordinate settings');
    throw new Error('Error in updating coordinate settings');
    return null;

  }

  public async updateScene (
    scene_id: string,
    updateProjectObj: updateProjectSceneObj,
  ): Promise<projectScene | void> {
    logger.info('updateScene Called', {sceneId: scene_id});
    try {
      const sceneData  = await this.model.findOne({_id: scene_id});
      const updateScene = await this.model.findOneAndUpdate(
        { _id: scene_id },
        { $set: {
          name: updateProjectObj.name,
          type: updateProjectObj.type,
          parent: updateProjectObj.parent,
          info_text: updateProjectObj.info_text,
          active: updateProjectObj.active,
          clouds: updateProjectObj.clouds,
          root: updateProjectObj.root,
          category: updateProjectObj.category,
          [`frames.${updateProjectObj.frame_id}.id`]:
            updateProjectObj.frame_id,
          position: updateProjectObj.position,
          polar_angle: updateProjectObj.polar_angle,
          distance: updateProjectObj.distance,
          auto_rotate: updateProjectObj.auto_rotate,
          minZoomLevel: sceneData?.type === projectSceneType.DEEP_ZOOM? updateProjectObj.minZoomLevel:undefined,
          maxZoomLevel: sceneData?.type === projectSceneType.DEEP_ZOOM? updateProjectObj.maxZoomLevel:undefined,
          'background.high_resolution': sceneData?.type === projectSceneType.DEEP_ZOOM? updateProjectObj.highRes:undefined,
          deep_zoom_status: sceneData?.type === projectSceneType.DEEP_ZOOM?  updateProjectObj.deep_zoom_status : undefined,
          deep_zoom_failed_info: sceneData?.type === projectSceneType.DEEP_ZOOM?
            updateProjectObj.deep_zoom_failed_info:undefined,
          ['earth_position.x_axis']: updateProjectObj.earth_position?.x_axis,
          ['earth_position.y_axis']: updateProjectObj.earth_position?.y_axis,
          ['earth_position.z_axis']: updateProjectObj.earth_position?.z_axis,
        } },
        { new: true },
      );

      if (updateScene) {
        logger.info('updateScene Successfull', {updateScene: updateScene});
        return updateScene as projectScene;
      }
      logger.error('Error in updating scene details');
      throw new Error('Error in updating scene details');
    } catch (error) {
      logger.error('Error in UpdateScene', {message: error});
      throw new Error('Internal Server Error' + error);
    }
  }

  public async moveToTrash (
    sceneIds: Array<string>,
    organization_id: string,
    timeStamp: number,
  ): Promise<masterScene | void> {
    logger.info('moveToTrash Called', { sceneIds, organization_id, timeStamp });

    interface linkedTrashData {
      _id: mongoose.Types.ObjectId;
      type: string;
      data: object;
      timeStamp: number;
      root: false;
    }

    const trash = new trashModule(organization_id);

    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: sceneIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });

    if (documents.length === 0) {
      logger.error('Scenes corresponding to scene IDs provided not found');
      throw 'Scenes corresponding to scene IDs provided not found';
    }

    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    const linked_trashIds: Array<string> = [];

    const svg = new MasterSVGModule(organization_id);
    const svgdocuments: Array<UnknownObject> = await svg.model.find({
      scene_id: { $in: sceneIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });

    // Prepare and insert SVG trash only if documents exist
    if (svgdocuments.length > 0) {
      const svgdocumentsObj = arrayToObject(svgdocuments) as Record<string, masterSVG>;
      const svgTrashId = new mongoose.Types.ObjectId();
      linked_trashIds.push(svgTrashId.toString());

      const svgdataToInsertObj: linkedTrashData = {
        _id: svgTrashId,
        type: `${organization_id.toLowerCase()}_master_svgs`,
        timeStamp,
        data: svgdocumentsObj,
        root: false,
      };

      await trash.addtoTrash(svgdataToInsertObj);
    }

    // Insert scene trash
    const scenedataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${organization_id.toLowerCase()}_master_scenes`,
      timeStamp,
      data: documentsObj,
      linked_trashes: linked_trashIds,
    };

    await trash.addtoTrash(scenedataToInsertObj);

    // Delete scenes
    await this.model.deleteMany({
      _id: { $in: sceneIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });

    // Delete SVGs only if they exist
    if (svgdocuments.length > 0) {
      await svg.model.deleteMany({
        scene_id: { $in: sceneIds.map((id) => new mongoose.Types.ObjectId(id)) },
      });
    }

    logger.info('Data moved to trash Successfully');
    return;
  }

  public async restoreScenes (
    organization_id: string,
    trash_Id: string,
    scene_Id: string,
  ): Promise<void> {
    logger.info('restoreScenes Called', { trash_Id, organization_id, scene_Id });

    const trash = new trashModule(organization_id);
    const svg = new MasterSVGModule(organization_id);

    const restoredData = await trash.restoreData(trash_Id);

    if (!restoredData) {
      logger.error('Failed to restore scene data from trash');
      throw new Error('Failed to restore scene data from trash');
    }

    const restorePromises: Promise<any>[] = [];
    const trashIdsToDelete: string[] = [trash_Id]; // Start with main trash ID

    // Restore specific scene if it matches the scene_Id
    const sceneToRestore = Object.values(restoredData.data).find((scene) => scene._id === scene_Id);
    if (sceneToRestore) {
      restorePromises.push(this.createScene(sceneToRestore));
    } else {
      logger.error('Scene not found in trash data for the provided scene_Id');
      throw new Error('Scene not found for the provided scene_Id');
    }

    // Restore linked SVGs if any
    if (Array.isArray(restoredData.linked_trashes) && restoredData.linked_trashes.length > 0) {
      const linkedTrashRestorePromises = restoredData.linked_trashes.map(async (linkedTrashId) => {
        const linkedData = await trash.restoreData(linkedTrashId);

        if (linkedData?.data) {
          const svgDataList = Object.values(linkedData.data);
          for (const svgData of svgDataList) {
            // Restore the linked SVGs (if the scene_id matches)
            if (svgData.scene_id === scene_Id) {
              await svg.createSVG(svgData);
            }
          }
        }

        // Mark linked trash ID for deletion
        trashIdsToDelete.push(linkedTrashId);
      });

      restorePromises.push(...linkedTrashRestorePromises);
    }

    // Execute all restore operations
    await Promise.all(restorePromises);

    // Delete all relevant trash records (main + linked)
    await trash.deleteTrash(trashIdsToDelete);

    logger.info('Scene and linked data restored successfully and deleted from trash.');
  }

  public async UpdateSceneFiles (
    scene_id: string,
    updateProjectObj: updateProjectSceneObj,
    sceneType : string,
  ): Promise<projectScene | void> {
    logger.info('UpdateSceneFiles Called', {scene_id: scene_id, updateProjectObj: updateProjectObj});
    try {
      const updatedProjectScene = await this.model.findOneAndUpdate(
        { _id: scene_id },
        {
          $set: {
            'background.low_resolution': updateProjectObj.lowRes,
            'background.high_resolution': sceneType === 'deep_zoom' ? '': updateProjectObj.highRes,
            video: updateProjectObj.video as string,
            info_icon: updateProjectObj.info_icon as string,
            deep_zoom_status: sceneType === 'deep_zoom' ? deep_zoom_status.NOT_STARTED:undefined,
            deep_zoom_failed_info: undefined,
          },
        },
        { new: true },
      );
      if (updatedProjectScene) {
        logger.info('UpdateSceneFiles Successfull', {updatedProjectScene: updatedProjectScene});
        return updatedProjectScene;
      }
      logger.error('Error in updating project scene data');
      throw new Error('Error in updating project scene data');
    } catch (error) {
      logger.error('Error in updateSceneFiles', {message: error});
      throw new Error('Internal Server Error' + error);
    }
  }
  public async updateBulkSceneFrame (
    payload: updateBulkSceneType,
  ): Promise<string> {
    logger.info('updateBulkSceneFrame Called', {payload: payload});
    return new Promise<string>((resolve, reject) => {
      const promises: Promise<projectScene | null>[] = payload.query.map(
        async (item) => {
          return this.model
            .findOneAndUpdate(
              { _id: item.id },
              {
                $set: {
                  order: item.order,
                },
              },
              { new: true }, // Return the updated document
            )
            .then((res) => {
              return res;
            })
            .catch(() => {
              return null;
            });
        },
      );

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('updateBulkSceneFrame Successfull', {results: results});
            resolve('Documents updated successfully');
          } else {

            reject('Error while updating Scenes');
          }
        })
        .catch((error) => {
          logger.error('Error in updateBulkSceneFrame', {message: error});
          reject(error);
        });
    });
  }
}

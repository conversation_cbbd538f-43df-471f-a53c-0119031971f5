import mongoose, { PipelineStage } from 'mongoose';
import { iconLibrarySchema } from '../../schema/iconLibrarySchema';
import { allIcons, iconLibrary, QueryParams, updateIconLibrary } from '../../types/iconLibrary';
import logger from '../../config/logger';
import { Models } from '../../types/extras';

export class IconLibraryModule{
  private model: mongoose.Model<iconLibrary>;
  public storagepath;
  constructor () {
    this.model = mongoose.model<iconLibrary>(
      `${Models.ICONLIBRARY}`,
      iconLibrarySchema,
    );
    this.storagepath='CreationtoolAssets/projects/iconlibrary/';
  }

  public async CreateIcon (payload: iconLibrary):Promise<iconLibrary | void>{
    logger.info('CreateIcon called', {payload: payload });
    return new Promise((resolve, reject) => {
      const iconLib = new this.model(payload);
      console.log(payload);
      iconLib
        .save()
        .then((res) => {
          logger.info('CreateIcon Successfull', {response: res});
          resolve(res);
        })
        .catch((err) => {
          logger.error('Error in CreateIcon', {message: err});
          reject(err);
        });
    });
  }

  public async UpdateIcon (icon_id : string, payload:updateIconLibrary):Promise<iconLibrary | void>{
    logger.info('UpdateIcon called', {payload: payload });
    try {
      const updatedIconLibrary = await this.model.findOneAndUpdate(
        { _id: icon_id },
        {
          $set: {
            name: payload.name,
            type: payload.type,
            category: payload.category,
          },
        },
        { new: true },
      );
      if (updatedIconLibrary) {
        logger.info('UpdateIcon Successfull', {updatedIconLibrary: updatedIconLibrary});
        return updatedIconLibrary;
      }
      logger.error('Error in updatedIconLibrary');
      throw new Error('Error in updatedIconLibrary');
    } catch (err) {
      logger.error('Error in UpdateIcon', {message: err});
      throw new Error('Error in UpdateIcon' + err);
    }
  }

  public async GetIcon (type: string, category:string):Promise<allIcons | void | string>{
    logger.info('GetIcon called', {type: type, category: category });
    try {
    //   Const icons = await this.model.find({ type, category });
      const icons = await this.model.aggregate([
        {
          $match: {
            $and: [
              type ? { type: type } : {},
              category ? { category: category } : {},
            ],
          },
        },
      ]);
      // Console.log('icons', icons);
      if (icons.length > 0){
        return icons.reduce((acc, icon) => {
          acc[icon._id.toString()] = icon;
          logger.info('GetIcon Successfull', {acc: acc});
          return acc;
        }, {} as allIcons);
      } else if (icons.length === 0) {
        logger.info('GetIcon Successfull', {message: 'No Icons Present in the DB' });
        return 'No Icons Present';
      }
      throw new Error('No Icons Found on type & category');

    } catch (err) {
      logger.error('Error fetching icons:', err);
      throw new Error(`Error fetching icons:${err}`);
    }
  }

  public async SearchIcon (query:QueryParams):Promise<Array<allIcons> | null>{

    logger.info('SearchIcon called', { query: query.searchText });

    const pipeline: PipelineStage[] = [];

    pipeline.push({
      $match: { $or: [{ 'name': { $regex: query.searchText }  }, { 'category': { $regex: query.searchText }  } ] },
    });

    if (pipeline.length > 0) {
      const searchResult = await this.model.aggregate(pipeline);
      if (searchResult.length > 0) {
        logger.info('SearchIcon Successfull', {result: searchResult});
        return searchResult;
      }
      return null;
    }
    return null;
  }
}

import mongoose from 'mongoose';
import { customTourModule } from '../../../modules/customTour/index';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function CreateHotspot (
  request: ExtendedRequest,
  response: Response,
): Promise<object | void> {
  const organization_id = request.organization_id;
  if (!organization_id) {
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }
  const tour_id = request.body.tour_id;
  const project_id = request.body.project_id;
  const image_id = request.body.image_id;
  const tour = new customTourModule(organization_id, tour_id, project_id);
  const hotspot_id = new mongoose.Types.ObjectId();
  const createhotspotObj = {
    _id: hotspot_id,
    position: request.body.position,
    destination: request.body.destination,
  };
  tour.createHotspot(image_id, createhotspotObj).then((hotspotData) => {
    response.status(201).json({ status: 1, data: hotspotData });
  }).catch((error) => {
    logger.error('Unable to create hotspot:', {message: error});
    response.send({ status: 0, error: 'Unable to create hotspot' + error });
  });

}

import { Response } from 'express';
import {Units} from '../../../types/units';
import { ppgUnitType, unitMetaData } from '../../../types/webhooks';
import { UnitModule } from '../../../modules/units';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { ExtendedRequest, FileRequest } from '../../../types/extras';
import { CustomUserModule } from '../../../modules/webhooks';
import mongoose from 'mongoose';
import { sendMessageToChannel } from '../../../helpers/slackMessenger';
import { ProjectModule } from '../../../modules/projects';
import logger from '../../../config/logger';

export async function UpdateUnitByMeta (request: ExtendedRequest, response: Response):Promise<Units | void>{
  const project_id = request.headers.project_id as string;
  const unitsArray = request.body.data;
  const unit = new UnitModule(project_id);
  const organization_id = request.organization_id as string;
  const project = new ProjectModule(organization_id);
  const projectData = await project.getProjectById(project_id);
  if (projectData){
    Promise.all(unitsArray.map((unitData:ppgUnitType) => {
      return new Promise((resolve, reject) => {
        unit.updateUnitByMeta(unitData.metadata as unitMetaData, unitData).then((res:Units|null) => {
          resolve(res);
        }).catch((err:Error) => {
          reject(err);
        });
      });
    })).then(() => {
      response.send({status: 1, 'message': 'Status updated successfully for unit'});
    }).catch((err) => {
      logger.error('Error in unitsArray Mapping', {message: err});
      response.send({status: 0, message: err});
    });
  } else {
    response.send({status: 0, message: 'project_id not found'});
  }

}

export async function uploadcsv (
  request: FileRequest,
  response: Response,
): Promise<object | void> {
  // Const organization_id = request.headers.organization as string;
  const project_id = request.headers.project_id as string;
  const organization_id = request.organization_id as string;
  const project = new ProjectModule(organization_id);
  const projectData = await project.getProjectById(project_id);
  const authHeader = request.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];
  if (token && request.headers.project_id){
    if (!request.files) {
      response.send({ status: 0, error: 'file not found' });
      return;
    }
    if (projectData){
      const csvId = new mongoose.Types.ObjectId();
      const webhookModule = new CustomUserModule(project_id);
      const mediaUrl = await UploadUnitplanFiles(request.files, webhookModule.storagepath+csvId);
      const heading = 'New CSV File Upload detected!';
      const msg = mediaUrl.file;
      const webhookUrl = process.env.SLACK_WEBHOOKURL as string;
      sendMessageToChannel(webhookUrl, heading, msg).then(() => {
        response.send({status: 1, message: 'File uploaded successfully' });
      }).catch((err) => {
        logger.error('Error in sendMessageToChannel', {message: err});
        response.send({status: 0, error: 'error uploading file'});
      });
    } else {
      response.send({status: 0, error: 'project_id not found'});
    }
  } else {
    response.send({status: 0, error: 'please check headers'});
  }
}

// Src/middlewares/validateEventParams.ts

import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';

export const validateEventParams = [
  body('startDate').notEmpty().withMessage('Start date is required'),
  body('endDate').notEmpty().withMessage('End date is required'),
  body('orgId').notEmpty().withMessage('Organization ID is required'),
  body('projectId').notEmpty().withMessage('Project ID is required'),
  body('eventName').notEmpty().withMessage('Event name is required'),
  body('customFields').isArray().withMessage('Custom fields must be an array'),
  body('customFields').custom((value) => value.length > 0).
    withMessage('Custom fields array must contain at least one field'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      res.status(400).json({ status: 0, error: errors.array() });
    } else {
      next();
    }
  },
];

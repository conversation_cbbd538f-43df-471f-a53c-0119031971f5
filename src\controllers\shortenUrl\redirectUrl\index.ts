import logger from '../../../config/logger';
import { ShortUrlModule } from '../../../modules/shorterUrl';
import { ExtendedRequest } from '../../../types/extras';
import { ShortUrl } from '../../../types/shorterUrl';
import { Response } from 'express';

export async function getOrgUrl (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {

    const shortUrl = new ShortUrlModule();
    const shortUrlData: ShortUrl = {
      _id: request.params._id,
    };

    const org_url = await shortUrl.redirectToOrgUrl(shortUrlData);
    if (org_url) {
      // Response.set('title', org_url.title);
      // Response.set('description', org_url.description);
      // Response.set('image', org_url.image);
      // Response.set('org_url', org_url.org_url);
      // Response.redirect(org_url.org_url)
      const htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <meta property="og:site_name" content="propvr 360 Lite">
          <meta property="og:title" content="${org_url.title}" />
          <meta property="og:description" content="${org_url.description}" />
          <meta property="og:image" content="${org_url.image}">
          <meta property="og:type" content="website" />
          <meta property="og:updated_time" content="${Math.floor(Date.now() / 1000)}" />
          <title>${org_url.title}</title>
          <meta http-equiv="refresh" content="0;url=${org_url.org_url}" />
      </head>
      <body>
          <script>
              window.location.href = "${org_url.org_url}";
          </script>
      </body>
      </html>
  `;

      // Send HTML response
      response.setHeader('Content-Type', 'text/html');
      response.send(htmlContent);

    } else {
      response.status(404).json({ status: 0, error: 'URL not found' });
    }
  } catch (error) {
    logger.error('Error while fetching the original URL', {message: error});
    response.status(500).json({ status: 0, error: `Error while fetching the original URL: ${error}` });
  }
}


import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

type Middleware = (req: Request, res: Response, next: NextFunction) => void;

const sendEmailVerificationValidate: Middleware[] = [
  body('email', 'Valid email is required').isEmail().notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default sendEmailVerificationValidate;

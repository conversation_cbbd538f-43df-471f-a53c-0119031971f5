import { ExtendedRequest } from '../../../types/extras';
import { communityModule } from '../../../modules/community';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreCommunity (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.params;
  const community = new communityModule(project_id, organization_id);
  const trash_id = request.body.trash_id;
  await community
    .restoreCommunity(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Community got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreCommunity', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore Community : '+ error });
    });
}

import { Request, Response } from 'express';
import { community } from '../../../types/community';
import { communityModule } from '../../../modules/community';
import logger from '../../../config/logger';

export async function getSyncUpdata (request: Request, response: Response): Promise<community | void> {
  const {project_id, community_id} = request.body;
  const {organization} = request.params;
  const communityMod = new communityModule(project_id, organization);
  communityMod.getSyncUpdata(community_id, project_id, organization).then((res) => {
    response.send({status: 1, data: res});
  })
    .catch((err) => {
      logger.error('Error:', {message: err});
      response.send({status: 0, message: err});
    });
  return;
}

import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import logger from '../../../config/logger';
interface UploadedFiles {
  thumbnail: Express.Multer.File[];
}

const CreateProjectValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  console.log(req.files);
  console.log(req.body);

  const files = req.files as UploadedFiles | undefined;

  if (files) {
    if (!files.thumbnail) {
      logger.error('Thumbnail image field is required.');
      res.status(400).send({status: 0, error: 'Thumbnail image field is required.'});
    } else {
      const requiredTextFields = [
        'project_id',
        'name',
        'category',
      ];

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        logger.error(`Missing text fields: ${missingTextFields.join(', ')}`);
        res.status(400).send({status: 0, error: `Missing text fields: ${missingTextFields.join(', ')}`});
      } else {
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    logger.error('Invalid file structure in the request.');
    res.status(400).send({status: 0, error: 'Invalid file structure in the request.'});
  }
};

export default CreateProjectValidate;

import { admin, bucketName} from '../../../config/firebase';
import { Request, Response } from 'express';
import { GetSignedUrlConfig } from '@google-cloud/storage';
import logger from '../../../config/logger';

export async function GenerateSignedUrl ( request: Request,
  response: Response):Promise<void>{

  const { fileName } = request.body;

  const options:GetSignedUrlConfig = {
    version: 'v4',
    action: 'write',
    expires: Date.now() + (15 * 60 * 1000), // 15 minutes
    contentType: 'video/quicktime',
  };

  try {
    const [url] = await admin.storage().bucket(bucketName).file('luma-videos/'+fileName).getSignedUrl(options);
    const thumbnailUrl =
          'https://storage.googleapis.com/propvr-in-31420.appspot.com/luma-videos/' +
          encodeURIComponent(
            fileName,
          );

    response.status(200).send({status: 1, data: {url: url, storageLink: thumbnailUrl}});
  } catch (error) {
    logger.error('Error in GenerateSignedUrl', {message: error});
    response.status(500).send('Error generating signed URL');
  }
}

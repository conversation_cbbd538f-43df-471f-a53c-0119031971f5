import { ExtendedRequest } from '../../../types/extras';
import { unitplanModule } from '../../../modules/unitplan';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreUnitplan (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.params;
  const unitplan = new unitplanModule(project_id, organization_id);
  const trash_id = request.body.trash_id;
  await unitplan
    .restoreUnitplan(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: '<PERSON><PERSON><PERSON> got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreUnitplan', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore Unitplan : '+ error });
    });
}

import app  from '../../../app';
import request from 'supertest';
describe('Get All Scenes Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/getAllScenes?organization_id=8X3plU')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual({});

    const data : Record<string,  object> = res.body.data;
    Object.values(data).forEach((scene) => {
      expect(scene).toHaveProperty('sceneData');
      expect(scene).toHaveProperty('svgData');
    });

  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getAllScenes?organization_id=8X3p')   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).toEqual({});
  });
});

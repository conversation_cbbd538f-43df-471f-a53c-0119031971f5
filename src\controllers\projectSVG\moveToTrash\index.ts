import { ExtendedRequest } from '../../../types/extras';
import { ProjectSVGModule } from '../../../modules/projectSVG';

import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const svg = new ProjectSVGModule(project_id, organization_id);
  const svg_id = request.body.svg_id;
  const timeStamp = request.body.timeStamp;

  await svg
    .moveToTrash(svg_id, project_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving project svgs to trash: '+ error });
    });
}

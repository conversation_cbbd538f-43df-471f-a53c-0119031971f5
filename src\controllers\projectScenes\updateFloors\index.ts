import { ExtendedRequest } from '../../../types/extras';
import { projectScene } from '../../../types/projectScene';
import { ProjectSceneModule } from '../../../modules/projectScene';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function updateFloors (
  request: ExtendedRequest,
  response: Response,
): Promise<projectScene | void> {
  const organization_id = request.query.organization_id as string;
  const project_id = request.query.project_id as string;
  const projectSceneMod = new ProjectSceneModule(project_id, organization_id);
  const {floor_ids, scene_id, building_id} = request.body;
  await projectSceneMod
    .updateFloors(scene_id, floor_ids, building_id)
    .then((sceneData) => {
      response.status(201).json({ status: 1, data: sceneData });
    })
    .catch((error: Error) => {
      logger.error('Error in updateFloors', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while updating coordinate'+ error });
    });
}

import { ExtendedRequest } from '../../../types/extras';
import { SidebarModule } from '../../../modules/sidebar';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreSidebar (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.params;
  const sidebar = new SidebarModule(project_id);
  const trash_id = request.body.trash_id;
  await sidebar
    .restoreSidebar(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Sidebar got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreSidebar', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore Sidebar : '+ error });
    });
}

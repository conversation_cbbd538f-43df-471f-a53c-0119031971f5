import { validationResult } from 'express-validator';
import logger from '../../../config/logger';
import { ExtendedRequest } from './../../../types/extras';
import { Response, NextFunction } from 'express';

const DeleteMiniMapValidate = (
  req: ExtendedRequest,
  res: Response,
  next: NextFunction,
): void => {
  const requiredFields = ['project_id', 'minimap_id'];
  const missingTextFields = requiredFields.filter(
    (field) => !(field in req.body),
  );
  if (missingTextFields.length > 0) {
    logger.error(`Missing text fields: ${missingTextFields.join(', ')}`);
    res.status(400).send({status: 0, error: `Missing text fields: ${missingTextFields.join(', ')}`});
  } else {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log(errors);
    } else {
      next();
    }
  }
};

export default DeleteMiniMapValidate;

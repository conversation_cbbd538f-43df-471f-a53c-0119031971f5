import { Request, Response } from 'express';
import { Units } from '../../../types/units';
import { UnitModule } from '../../../modules/units';
import { Types } from 'mongoose';
import logger from '../../../config/logger';

async function CreateUnit (unitData:Units){
  return new Promise((resolve, reject) => {
    const { unitplan_id, project_id, name, status, metadata,
      floor_id, building_id, community_id, price, currency, tour_id, measurement, measurement_type,
      balcony_measurement, balcony_measurement_type, cta_link } = unitData;
    const unit = new UnitModule(project_id);
    unit
      .createUnit({_id: new Types.ObjectId(), unitplan_id, project_id, name, status, metadata,
        floor_id, building_id, community_id, currency, price, tour_id, measurement, measurement_type,
        balcony_measurement, balcony_measurement_type, cta_link,
      })
      .then((res) => {
        resolve(res);
      })
      .catch((error: Error) => {
        logger.error('Error in createUnit', {message: error});
        console.error(error);
        reject(error);
      });
  });
}

export async function BulkCreateUnits (
  request: Request,
  response: Response,
): Promise<Units | void> {
  try {
    const unitsArray = request.body;
    console.log('unitArray', unitsArray);
    const promises = unitsArray.map((item:Units) => CreateUnit(item));
    Promise.all(promises).then((result) => {
      response.status(201).json({status: 1, data: result});
    });

  } catch (error){
    logger.error('Error in BulkCreateUnits', {message: error});
    response.status(500).json({ status: 0, error: 'Error while creating the units' });
  }

}

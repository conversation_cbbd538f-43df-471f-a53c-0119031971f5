
import { Request, Response } from 'express';
import { IconLibraryModule } from '../../../modules/iconLibrary';

export async function GetIconsLibrary (
  request: Request,
  response: Response,
):Promise<void> {
  const iconType = request.query.type as string;
  const iconCategory = request.query.category as string;
  // Const {organization_id} = request.params;
  console.log('type', iconType);
  console.log('cat', iconCategory);
  // Console.log('id', organization_id);

  const iconLibrary = new IconLibraryModule();

  try {
    iconLibrary.GetIcon(iconType, iconCategory)
      .then((res) => {
        console.log('res', res);
        response.send({
          status: 1,
          message: 'GetIcon Succesfull',
          data: res,
        });
      }).catch((err) => {
        response.send({
          status: 0,
          message: err.message,
        });
      });
  } catch (error){
    if (error instanceof Error){
      response
        .status(400)
        .send({ status: 0, error: 'Error in GetIconsLibrary', message: error.message });
    }
  }

}

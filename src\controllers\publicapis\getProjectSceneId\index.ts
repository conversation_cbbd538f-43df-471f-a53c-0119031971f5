import { Request, Response } from 'express';

import {ProjectSceneModule} from '../../../modules/projectScene';
import logger from '../../../config/logger';

export async function getProjectSceneId (
  request: Request,
  response: Response,
): Promise<void> {

  const {project_id, organization_id} = request.params;
  const floor_id = request.query.floor_id as string;

  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'organization_id not found'});
    return;
  }
  //   Pass organization_id in this ProjectModule
  const projectScene = new ProjectSceneModule(project_id, organization_id);

  const sceneId = await projectScene.getProjectSceneId(floor_id);
  if (sceneId) {
    response.status(200).json({ status: 1, scene_id: sceneId});
  } else {
    logger.error('Floor not present in scenes');
    response.status(500).json({ status: 0, error: 'Floor not present in scenes' });
  }
}

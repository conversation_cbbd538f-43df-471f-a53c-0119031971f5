import { Request, Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';
import { MediaType } from '../../../types/amenity';
interface UploadedFiles {
  thumbnail: Express.Multer.File[],
  file: Express.Multer.File[]
}

const updateGalleryItemValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('project_id', 'Project ID  is required').notEmpty(),
  body('item_id', 'Gallery Item ID  is required').notEmpty(),
  body('type', 'Please select valid gallery item type').optional().isIn(Object.values(MediaType)),
  (req: Request, res: Response, next: NextFunction): void => {
    const files = req.files as UploadedFiles | undefined;
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    const { type, link } = req.body;
    if (type) {
    // Check if the type is 'link', then link field should not be empty
      if (type === MediaType.LINK && !link) {
        res.status(400).json({ errors: [{ msg: 'Link is required for this type of gallery item' }] });
        return;
      }

      // Check if the type is changed from 'link' and if so, make file and thumbnail mandatory
      if (type !== MediaType.LINK && (!files || !files.file || !files.thumbnail)) {
        res.status(400).json({ errors: [{ msg: 'File and Thumbnail are required for this type of gallery item' }] });
        return;
      }
    }
    next();
  },
];

export default updateGalleryItemValidate;

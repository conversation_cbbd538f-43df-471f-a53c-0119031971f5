import { Types } from 'mongoose';

// Types/notification.ts
export interface NotificationPayload {
    tokens: string | string[];
    notification: {
      title: string;
      body: string;
    };
    data?: Record<string, string>;
}

export interface NotificationResponse {
    successCount?: number;
    failureCount?: number;
    failedTokens?: string[];
    messageId?: string;
}

export type Notification = {
  _id:Types.ObjectId,
  url:string,
  status:string,
  user_id:string,
  timestamp:string,
  organization:string,
  viewed:boolean
}

export enum NotificationStatus {
  ACTIVE='active',
  CANCELLED='cancelled'
}

export type UpdateNotification = {
  status?:NotificationStatus,
  viewed?:boolean
}

export type NotificationFilter = {
  user_id: string;
  organization: string;
  viewed?: boolean;
}

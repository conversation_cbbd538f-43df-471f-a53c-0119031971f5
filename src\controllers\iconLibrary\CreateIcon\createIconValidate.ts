import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import { iconLibrary_types } from '../../../types/iconLibrary';

const createIconValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('type', 'Icon type is required').notEmpty().isIn(Object.values(iconLibrary_types)),
  body('name', 'Icon type is required').notEmpty(),
  body('category', 'Icon category is required').notEmpty(),

  (req: Request, res: Response, next: NextFunction): void => {

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createIconValidate;

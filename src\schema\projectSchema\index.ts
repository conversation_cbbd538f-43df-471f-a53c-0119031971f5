import mongoose from 'mongoose';
import { ctaType, FontType, PropertyType, Theme, unitCardType } from '../../types/projects';
// Define sub-schemas for each type of project setting
export const projectSchema = new mongoose.Schema({
  _id: {
    type: String,
    immutable: true,
  },
  units: {
    type: Object,
    immutable: false,
  },
  name: {
    type: String,
    immutable: false,
  },
  description: {
    type: String,
    immutable: false,
  },
  experience: {
    type: Array,
    immutable: false,
  },
  property_type: {
    type: String,
    enum: PropertyType,
  },
  is_public: {
    type: Boolean,
    immutable: false,
  },
  organization_id: {
    type: String,
    immutable: false,
  },
  role: {
    type: String,
    immutable: false,
  },
  city: {
    type: String,
    immutable: false,
  },
  country: {
    type: String,
    immutable: false,
  },
  project_thumbnail: {
    type: String,
    immutable: false,
  },
  data_sync: {
    number_of_units: {
      type: Number,
      immutable: false,
    },
    number_of_floors: {
      type: Number,
      immutable: false,
    },
    number_of_amenities: {
      type: Number,
      immutable: false,
    },
    number_of_buildings: {
      type: Number,
      immutable: false,
    },
  },
  projectSettings: {
    general: {
      slots: {
        type: Array,
        immutable: false,
      },
      is_enabled: {
        type: Boolean,
        immutable: false,
      },
      branding_logo: {
        type: String,
        immutable: false,
      },
      branding_logo_dark: {
        type: String,
        immutable: false,
      },
      lat: {
        type: Number,
        immutable: false,
      },
      long: {
        type: Number,
        immutable: false,
      },
      hideStatus: {
        type: Boolean,
        immutable: false,
      },
      timezone: {
        type: String,
        immutable: false,
      },
      updated_at: {
        type: String,
        immutable: false,
      },
    },
    pixelstreaming: {
      max_concurrent_sessions: {
        type: Number,
        immutable: false, // Immutable fields are not updatable
      },
      pixel_streaming_endpoint: {
        type: String,
        immutable: false,
      },
      is_enabled: {
        type: Boolean,
        immutable: false,
      },
      session_duration: {
        type: Number,
        immutable: false,
      },
      resource_group: {
        type: String,
        immutable: false,
      },
      vm_scaleset_name: {
        type: String,
        immutable: false,
      },
      min_instances: {
        type: Number,
        immutable: false,
      },
      auto_scale: {
        type: Boolean,
        immutable: false,
      },
      application_id: {
        type: String,
        immutable: false,
      },
    },
    salestool: {
      is_enabled: {
        type: Boolean,
        immutable: false,
      },
      session_duration: {
        type: Number,
        immutable: false,
      },
      default_experience: {
        type: String,
        immutable: false,
      },
      tags: {
        type: Array,
      },
    },
    ale: {
      is_enabled: {
        type: Boolean,
        immutable: false,
      },
      initial_scene_type: {
        type: String,
        immutable: false,
      },
      initial_scene_id: {
        type: String,
        immutable: false,
      },
      welcome_video: {
        type: String,
        immutable: false,
      },
      welcome_thumbnail: {
        type: String,
        immutable: false,
      },
      shortened_link: {
        type: String,
        immutable: false,
      },
      default_language: {
        type: String,
        immutable: false,
      },
      supported_languages: {
        type: Array,
        immutable: false,
      },
      currency_support: {
        type: Boolean,
        default: false,
        immutable: false,
      },
      cta_name: {
        type: String,
        default: 'Book Unit',
        immutable: false,
      },
      cta_type: {
        type: String,
        default: ctaType.DEFAULT,
        enum: ctaType,
        immutable: false,
      },
      is_cta_enabled: {
        type: Boolean,
        immutable: false,
      },
      unit_card_customize_type: {
        type: String,
        default: unitCardType.DEFAULT,
        enum: unitCardType,
        immutable: false,
      },
      unitcard_config: {
        type: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        measurement: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        bedrooms: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        bathrooms: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        status: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        style: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        price: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        view: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        maid: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        building_id: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        floor_id: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        units: {
          type: Boolean,
          default: true,
          immutable: false,
        },
        favIcon: {
          type: Boolean,
          default: true,
          immutable: false,
        },
      },
    },
    embed: {
      is_enabled: {
        type: Boolean,
        immutable: false,
      },
    },
    theme: {
      theme: {
        type: String,
        enum: Theme,
      },
      primary: {
        type: String,
        immutable: false,
      },
      primary_text: {
        type: String,
        immutable: false,
      },
      secondary: {
        type: String,
        immutable: false,
      },
      secondary_text: {
        type: String,
        immutable: false,
      },
      font_type: {
        type: String,
        enum: FontType,
      },
      font_url: {
        type: String,
        immutable: false,
      },
    },
    gallery: {
      type: Object,
    },
    amenity: {
      type: Object,
    },
    hologram: {
      project_logo: {
        type: String,
        immutable: false,
      },
      project_type: {
        type: String,
        immutable: false,
      },
      project_location: {
        type: String,
        immutable: false,
      },
      amount: {
        type: String,
        immutable: false,
      },
      bedrooms: {
        type: Array,
        immutable: false,
      },
      thumbnail: {
        type: String,
        immutable: false,
      },
      file: {
        type: String,
        immutable: false,
      },
      tags: {
        type: Array,
      },
    },
    metadata: {
      type: Object,
    },
  },
});

const commonKeys = [
  'g',
  'x',
  'y',
  'title',
  'category',
  'placement',
  'reSize',
  'zIndex',
  'maxZoomLevel',
  'minZoomLevel',
  'name',
  'rotation',
  'position',
  'scale.x',
  'scale.y',
  'scale',
  'svg_url',
  'width',
  'height',
  'video_tag',
  'type',
  'layer_id',
  '_id',
];
export const checkFieldsSchema = {
  'project': ['project_id', ...commonKeys],
  'image': ['image_id', ...commonKeys],
  'scene': ['scene_id', ...commonKeys],
  'pin': ['scene_id', ...commonKeys],
  'amenity': ['amenity_id', ...commonKeys],
  'community': ['community_id', 'scene_id', 'showLabel', ...commonKeys],
  'tower': ['building_id', 'scene_id', ...commonKeys],
  'building': ['building_id', 'scene_id', 'showLabel', ...commonKeys],
  'toweroverlay': ['floor_id', 'scene_id', 'building_id', ...commonKeys],
  'floor': ['floor_id', 'building_id', 'scene_id', ...commonKeys],
  'units': ['units', ...commonKeys],
  'label': ['title', 'category', ...commonKeys],
  'amenitycategory': ['amenity_category', ...commonKeys],
  'grouped_units': ['showLabel', ...commonKeys],
  'zoom_target': [...commonKeys],
};
// Export const checkMasterFieldsSchema = {
//   'scene': ['scene_id', ...commonKeys],
//   'project': ['project_id', ...commonKeys],
//   'pin': ['scene_id', ...commonKeys],
//   'image': ['image_id', ...commonKeys],
//   'route': [...commonKeys],
//   'radius': [...commonKeys],
//   'label': ['title', 'category', ...commonKeys],
//   'unavailable': [...commonKeys],
//   'static': [...commonKeys],
//   'plane': [...commonKeys],
//   'none': [...commonKeys],
//   'zoom_target': [...commonKeys],
// };

// Const sidebarCommonKeys = [
//   'project_id',
//   'type',
//   'id',
//   'icon_id'
// ]
// Export const sidebarCheckFields = {
//   'custom':['link',...sidebarCommonKeys],
//   'projectscene':['scene_id',...sidebarCommonKeys],
//   'masterscene':['scene_id',...sidebarCommonKeys],
//   'amenity':[...sidebarCommonKeys],
//   'gallery':[...sidebarCommonKeys],
//   'unitplan':[...sidebarCommonKeys],
//   'map':[...sidebarCommonKeys],
// }

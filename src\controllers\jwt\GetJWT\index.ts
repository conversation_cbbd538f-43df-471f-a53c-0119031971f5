import { Response } from 'express';
import { JWTModule } from '../../../modules/jwt';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function GetJWT (
  request:ExtendedRequest,
  response:Response,
):Promise<string|void>{
  try {
    const authHeader = request.headers.authorization as string;
    const token = authHeader && authHeader.split(' ')[1] as string;
    const jwtModule = new JWTModule();
    const jwtRecord = await jwtModule.GetJWTRecord(token);
    if (jwtRecord){
      response.status(200).send({status: 1, data: jwtRecord});
    } else {
      response.status(500).send({status: 0, message: 'Record not found'});
    }
  } catch (err){
    logger.error('Internal server error', {message: err});
    response.status(500).send({status: 0, message: 'Internal server error'});
  }
}

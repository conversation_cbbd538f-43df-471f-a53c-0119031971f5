import { ExtendedRequest } from '../../../types/extras';
import { unitplanModule } from '../../../modules/unitplan';

import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const unitplan = new unitplanModule(project_id, organization_id);
  const unitplan_id = request.body.unitplan_id;
  const timeStamp = request.body.timeStamp;
  const parentID = request.body.parentID;

  await unitplan
    .moveToTrash(unitplan_id, project_id, organization_id, timeStamp, parentID)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving unitplans to trash: '+ error });
    });
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const assignRoletoUserValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('role', 'Role is required'),
  body('user_id', 'User ID is required'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    console.log('validate');
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default assignRoletoUserValidate;

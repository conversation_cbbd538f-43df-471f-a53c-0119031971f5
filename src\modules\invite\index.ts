import { inviteType } from '../../types/invite';
import logger from '../../config/logger';
import { sendEmail } from '../../helpers/session';
export class SendgridModule{

  public async SendMail (inviteData:inviteType):Promise<string|void>{
    logger.info('SendMail Called', {inviteData: inviteData});
    const msg = {
      to: inviteData.toAddress,
      from: inviteData.fromAddress,
      subject: inviteData.subject,
      isMultiple: true,
      html: inviteData.message,
    };
    sendEmail(msg).then(() => {
      logger.info('Mail Sent Successfully', {message: msg});
      return 'Mail Sent Successfully' as string;
    }).catch((err:string) => {
      logger.error('Error sending mail', {message: err});
      throw new Error('Error sending mail'+err);
    });
  }

}

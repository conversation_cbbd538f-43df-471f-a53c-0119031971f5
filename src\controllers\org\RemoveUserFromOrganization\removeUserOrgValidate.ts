import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const removeUserOrgValidate = [
  header('accesstoken', 'Access Token is required').notEmpty(),
  header('organization', 'Organization  is required').notEmpty(),
  body('email', 'email  is required').notEmpty(),
  body('email', 'Email is invalid').isEmail(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default removeUserOrgValidate;

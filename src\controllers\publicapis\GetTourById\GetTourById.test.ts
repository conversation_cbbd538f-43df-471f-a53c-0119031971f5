import app  from '../../../app';
import request from 'supertest';
describe('Get Tour by Id Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/GetTourById' +
            '?organization_id=HIPUat&project_id=123&tour_id=6583fbe7cd59b7ddb5d0f06b')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual({});
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/GetTourById'+
            '?organization_id=HIPUa&project_id=123&tour_id=6583fbe7cd59b7ddb5d0f06b')   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(500);
    expect(res.body).toHaveProperty('error');
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/GetTourById'+
            '?organization_id=HIPUat&project_id=13&tour_id=6583fbe7cd59b7ddb5d0f06b')   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(500);
    expect(res.body).toHaveProperty('error');
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/GetTourById'+
            '?organization_id=HIPUat&project_id=123&tour_id=6583fbe7cd59b7ddb5d0f0')   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(500);
    expect(res.body).toHaveProperty('error');
  });

});

import { Request, Response, NextFunction } from 'express';
import fs from 'fs';
import multer, { Field } from 'multer';
// Interface File {
//   Path: string;
//   // Other properties of the File type
// }
const ensureDirectory = (directoryPath: string) => {
  console.log(directoryPath);
  if (!fs.existsSync(directoryPath)) {
    fs.mkdirSync(directoryPath);
    console.log(`'${directoryPath}' created.`);
  } else {
    console.log(`'${directoryPath}' already exists.`);
  }
};
const createStorage = (directoryName: string) => {
  const storage = multer.diskStorage({
    destination: function (req, file, cb) {
      const dest = `${directoryName}/`;
      ensureDirectory(dest);
      cb(null, dest);
    },
    filename: function (req, file, cb) {
      cb(null, Date.now() + '-' + file.originalname);
    },
  });
  return multer({ storage });
};
const uploadHandler = (fieldNames: Field[], dirName: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const customUploadDir = dirName;
    const upload = createStorage(customUploadDir);
    upload.fields(fieldNames)(req, res, (err) => {
      if (err) {
        // Return res.status(500).send('File upload error');
      }
      // Check if req.files is an array (File[]) or an object ({ [fieldname: string]: File[] })
      //   Res.status(200).send('Files uploaded.');
      next();
    });
  };
};
export default uploadHandler;

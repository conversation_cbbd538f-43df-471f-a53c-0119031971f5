import {  Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';
import { ExtendedRequest } from '../../../types/extras';

const deleteOptionValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('project_id', 'Project ID is required').notEmpty(),
  body('option_id', 'Option ID is required').notEmpty(),
  (req: ExtendedRequest, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default deleteOptionValidate;

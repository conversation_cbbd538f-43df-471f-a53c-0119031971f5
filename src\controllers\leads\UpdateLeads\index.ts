import { LeadsModule } from '../../../modules/leads';
import { Leads } from '../../../types/leads';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';

export async function UpdateLead (
  request: ExtendedRequest,
  response: Response,
): Promise<Leads | void> {
  const organization_id = request.organization_id as string;
  const lead_id = request.body.lead_id as string;
  const newLead = {
    ...request.body.name && { name: request.body.name },
    ...request.body.phone_number && { phone_number: request.body.phone_number },
    ...request.body.email && { email: request.body.email },
    interested_in: {
      ...request.body.project_id && { project_id: request.body.project_id },
      ...request.body.type && { type: request.body.type },
      ...request.body.type !== 'project' && request.body.unit_id && { unit_id: request.body.unit_id },
    },
    ...request.body.source && { lead_source: request.body.source },
    ...request.body.lead_product_interest && { lead_product_interest: request.body.lead_product_interest },
    ...request.body.lead_industry_type && { lead_industry_type: request.body.lead_industry_type },
    ...request.body.lead_status && { lead_status: request.body.lead_status },
  };

  const leads = new LeadsModule(organization_id);
  await leads
    .UpdateLead(lead_id, newLead)
    .then((leadData: Leads| null) => {
      response
        .status(200)
        .json({ status: 1, data: leadData });
    })
    .catch((error: Error) => {
      response
        .status(500)
        .json({ status: 0, error: 'Error while updating lead '+ error });
    });
}

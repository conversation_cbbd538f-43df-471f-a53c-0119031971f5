import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import {  CreateIconLibrary } from '../controllers/iconLibrary/CreateIcon';
import uploadHandler from '../helpers/uploadHandler';
import { UpdateIconLibrary } from '../controllers/iconLibrary/UpdateIcon';
import { GetIconsLibrary } from '../controllers/iconLibrary/GetIcons';
import { SearchIcon } from '../controllers/iconLibrary/SearchIcons';
import getIconValidate from '../controllers/iconLibrary/GetIcons/getIconValidate';
import updateIconValidate from '../controllers/iconLibrary/UpdateIcon/updateIconValidate';
import createIconValidate from '../controllers/iconLibrary/CreateIcon/createIconValidate';
import searchIconValidate from '../controllers/iconLibrary/SearchIcons/searchIconValidate';

const router = express.Router();

router.post(
  '/createIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler(
    [
      { name: 'icon' },
    ],
    'masterScenes/',
  ),
  createIconValidate,
  CreateIconLibrary,
);

router.post(
  '/updateIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  updateIconValidate,
  UpdateIconLibrary,
);
router.get(
  '/getIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  getIconValidate,
  GetIconsLibrary,
);

router.get(
  '/searchIcon',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  searchIconValidate,
  SearchIcon,
);

export default router;

import mongoose from 'mongoose';
import { CustomUser } from '../../types/webhooks';
import { customUserSchema } from '../../schema/webhooks';
import logger from '../../config/logger';
import { Models } from '../../types/extras';
export class CustomUserModule{
  private model:mongoose.Model<CustomUser>;
  public storagepath;
  constructor (project_id:string){
    this.model = mongoose.model<CustomUser>(`${project_id}${Models._CUSTOMUSERS}`, customUserSchema);
    this.storagepath = 'CreationtoolAssets/csvDump/'+project_id+'/inventory/csv';

  }

  public async GetUserId (apikey:string):Promise<CustomUser|null>{
    logger.info('GetUserId Called', {apikey: apikey});
    const userId = await this.model.findOne({key: apikey}) ;
    if (userId){
      logger.info('GetUserId Successfull', {userId: userId});
      return userId;
    }
    logger.info('Error in GetUserId');
    return null;
  }
}

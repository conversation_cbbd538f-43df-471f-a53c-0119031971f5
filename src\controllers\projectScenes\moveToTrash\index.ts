import { ExtendedRequest } from '../../../types/extras';
import { projectScene } from '../../../types/projectScene';
import { ProjectSceneModule } from '../../../modules/projectScene';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<projectScene | void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const scene = new ProjectSceneModule(project_id, organization_id);
  const scene_id = request.body.scene_id;
  const timeStamp = request.body.timeStamp;

  await scene
    .moveToTrash(scene_id, project_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting master scenes : '+ error });
    });
}

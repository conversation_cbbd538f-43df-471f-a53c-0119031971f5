import mongoose from 'mongoose';
import { unitStatus, priceCurrency, measurementType } from '../../types/units';
export const unitSchema = new mongoose.Schema({
  _id: String,
  unitplan_id: String,
  project_id: String,
  name: String,
  status: {
    type: String,
    enum: unitStatus,
    default: unitStatus.AVAILABLE,
  },
  metadata: Object,
  building_id: String,
  floor: String,
  floor_id: String,
  price: String,
  max_price: String,
  community_id: String,
  currency: {
    type: String,
    enum: priceCurrency,
    default: priceCurrency.INR,
  },
  tour_id: String,
  measurement: Number,
  measurement_type: {
    type: String,
    enum: measurementType,
    default: measurementType.SQFT,
  },
  cta_link: {type: String},
  balcony_measurement: Number,
  balcony_measurement_type: {
    type: String,
    enum: measurementType,
    default: measurementType.SQFT,
  },
  suite_area: Number,
  suite_area_type: {
    type: String,
    enum: measurementType,
    default: measurementType.SQFT,
  },
});

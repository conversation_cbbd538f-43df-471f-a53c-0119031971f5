import {Request, Response} from 'express';
import { MasterSceneModule } from '../../../modules/masterScene/index';
export default async function updateScene (request:Request, response:Response): Promise<void> {
  const masterScene = new MasterSceneModule(request.headers.organization as string);
  masterScene.updateScene(request.body.scene_id, request.body).then(async (res) => {
    response.send({status: 1, data: res});
  }).catch((error) => {
    response.send({status: 0, message: error});
  });
}

import { Request, Response } from 'express';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';
export async function EndSession (
  request: Request,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const session_id = request.body.session_id;

  session
    .EndSession(session_id)
    .then((sessiondata) => {
      response.send({ status: 1, data: sessiondata });
    })
    .catch(() => {
      logger.error('Error in CreateSession');
      response.send({ status: 0, error: 'Error ending session' });
    });
}

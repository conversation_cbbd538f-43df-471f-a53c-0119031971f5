import { Response } from 'express';

import { ProjectModule } from '../../../modules/projects';
import { ExtendedRequest } from '../../../types/extras';
import {httpRequestTimer} from '../../../utilis/metrics';
import logger from '../../../config/logger';
import { Project } from '../../../types/projects';
import { PublicApis } from '../../../modules/publicapis';

export default async function listProjectsFromOrganization (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const start = Date.now();
  try {
    const organization_id = request.params.organization_id as string;
    const project = new ProjectModule(organization_id as string);
    const publicAPI = new PublicApis(organization_id as string);
    if (organization_id === undefined || organization_id === ''){
      logger.error('organization_id not found');
      response.status(400).send({status: 0, error: 'organization_id not found'});
      return;
    }
    const projectData:Record<string, Project> = await project.GetListOfAllProjects();
    if (projectData !== null){
      const listOfProjects = Object.keys(projectData);
      for (const id of listOfProjects) {
        const result = await publicAPI.processProjects(id);
        if (result){
          // Console.log("111",result);
          projectData[id].units = result;
        }
      }
    }
    response.send({ status: 1, data: projectData });
    return;
  } finally {
    const responseTimeInMs = Date.now() - start;
    httpRequestTimer.labels(request.method, request.route.path,
      response.statusCode.toString()).observe(responseTimeInMs);
  }

}

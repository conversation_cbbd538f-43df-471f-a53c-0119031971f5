import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { getUserByEmail, singlesigningOn } from '../../../helpers/authUser';
import { OrganizationModule } from '../../../modules/organization';
import { UserModule } from '../../../modules/user';
import jwt from 'jsonwebtoken';
import { admin } from '../../../config/firebase';
// Import jwt from 'jsonwebtoken';
import logger from '../../../config/logger';
const organization = new OrganizationModule();
const userMod = new UserModule();
export async function createUser (request: ExtendedRequest, response: Response):Promise<void>{
  const email = request.body.email;
  const role = request.body.role;
  const first_name = request.body.first_name;
  const last_name = request.body.last_name;
  const uid = request.body.uid;
  const organization_id=request.organization_id;
  if (organization_id){
    // Const secretKey = process.env.JWT_TOKEN as string;
    // Const expTime = process.env.JWT_TOKEN_EXP;
    // Const token = jwt.sign({} as object, secretKey, { expiresIn: expTime});

    // Const secretKey = process.env.JWT_TOKEN as string;

    // Const expTime = process.env.JWT_TOKEN_EXP as any;

    // Fix 1: Properly type the payload and options
    // Const payload = {} as jwt.JwtPayload;
    // Const options: jwt.SignOptions = {
    //   ExpiresIn: expTime,
    // };

    // Const token = jwt.sign(payload, secretKey, options);

    const isUser = await userMod.getUserDetailsByEmail(email);
    let user;
    if (!isUser){
      user = await userMod.CreateUser({
        first_name: first_name,
        last_name: last_name,
        email: email,
        uid: uid,
      });
    } else {
      user = await getUserByEmail(email);
    }
    if (user){
      try {
        const updatedUser = await organization.AddUserToOrganization(organization_id, {
          user_id: user.uid,
          email: email,
          first_name: first_name,
          last_name: last_name,
          organization_id: [organization_id],
        });

        if (updatedUser){
          const userRoleObj = {
            user_id: user.uid,
            organizationId: organization_id,
            roleId: role,
            email: user.email?user.email:'',
          };
          organization.AssignRoleToUser(userRoleObj).then(() => {
            response.send({status: 1, data: updatedUser});
          });
        } else {
          response.send({status: 0, error: 'Error adding user to organization'});
        }

      } catch (error) {
        logger.error('Error in AddUserToOrganization', {message: error});
        response.send({status: 0, error: 'User already exists'});
      }

    } else {
      response.send({status: 0, error: 'Error creating user'});
    }

  } else {
    response.send({status: 0, error: 'Invalid API key'});
  }
}
export async function CheckUserExists (request: ExtendedRequest, response: Response):Promise<void> {
  const email = request.body.email;
  const organization_id = request.organization_id;
  const user = await getUserByEmail(email);
  if (user && organization_id){
    const listofusers = await userMod.ListUsersInOrganization(organization_id, []);
    if (listofusers){
      const emailExists = listofusers.some((userdata) => userdata.email === email);
      if (emailExists) {
        response.send({status: 1, user: true});
      } else {
        response.send({status: 0, user: false});
      }
    } else {
      response.send({status: 0, user: false});
    }
  } else {
    response.send({status: 0, user: false});
  }
}
export async function singleSignOn (request: ExtendedRequest, response: Response):Promise<void> {
  const email= request.body.email;
  const organization_id = request.organization_id;
  const domain = request.body.domain;
  const user = await getUserByEmail(email);
   interface orguser{
    _id:string,
    email:string,
    organization_id:Array<{[key:string]:string}>
   }
   if (organization_id && user){
     const currentuser = await userMod.GetUserDetails(user.uid) as orguser;
     if (currentuser && currentuser.organization_id.some((org) => org._id === organization_id)){
       singlesigningOn(email, organization_id, domain).then((link) => {
         response.send({status: 1, data: link});
       })
         .catch((error) => {
           logger.error('Error in singlesigningOn', {message: error});
           response.send({status: 0, error: error});
         });
     } else {
       response.send({status: 0, error: 'Email does not exists in the organization'});
     }

   } else {
     response.send({status: 0, error: 'Invalid organization ID or user not found'});
   }

}
export async function SSO (request: ExtendedRequest, response: Response):Promise<void> {
  const email = request.body.email;
  const organization_id = request.organization_id;
  const domain = request.body.domain;
  const role = request.body.role;
  const first_name = request.body.first_name;
  const last_name = request.body.last_name;
  // Const uid = request.body.uid;
  const user = await getUserByEmail(email);
  if (!organization_id){
    response.send({status: 0, message: 'Invalid API key'});
    return;
  }

  if (user){
    const addUserToOrg = async () => {
      const updatedUser = await organization.AddUserToOrganization(organization_id, {
        user_id: user.uid,
        email: email,
        first_name: first_name,
        last_name: last_name,
        organization_id: [organization_id],
      });

      if (updatedUser){
        const userRoleObj = {
          user_id: user.uid,
          organizationId: organization_id,
          roleId: role,
          email: user.email?user.email:'',
        };
        return organization.AssignRoleToUser(userRoleObj).then(() => {
          return updatedUser;
        }).catch((error) => {
          logger.error('Error in AssignRoleToUser', {message: error});
          return null;
        });
      }
      return null;

    };

    const listofusers = await userMod.ListUsersInOrganization(organization_id, []);
    if (listofusers){
      const emailExists = listofusers.some((userdata) => userdata.email === email);
      if (emailExists) {
        singlesigningOn(email, organization_id, domain).then((link) => {
          response.send({status: 1, data: link});
        })
          .catch((error) => {
            logger.error('Error in singlesigningOn', {message: error});
            response.send({status: 0, error: error});
          });
      } else {
        addUserToOrg().then((updatedUser) => {
          if (updatedUser){
            singlesigningOn(email, organization_id, domain).then((link) => {
              response.send({status: 1, data: link});
            })
              .catch((error) => {
                logger.error('Error in singlesigningOn', {message: error});
                response.send({status: 0, error: error});
              });
          } else {
            response.send({status: 0, error: 'Error adding user to organization'});
          }
        })
          .catch((error) => {
            logger.error('Error in addUserToOrg', {message: error});
            response.send({status: 0, error: 'Error adding user to organization'});
          });
      }
    } else {
      addUserToOrg().then((updatedUser) => {
        if (updatedUser){
          singlesigningOn(email, organization_id, domain).then((link) => {
            response.send({status: 1, data: link});
          })
            .catch((error) => {
              logger.error('Error in singlesigningOn', {message: error});
              response.send({status: 0, error: error});
            });
        } else {
          response.send({status: 0, error: 'Error adding user to organization'});
        }
      })
        .catch((error) => {
          logger.error('Error in addUserToOrg', {message: error});
          response.send({status: 0, error: 'Error adding user to organization'});
        });
    }
  } else {
    // Const secretKey:string = process.env.JWT_TOKEN || 'fallback_secret';
    // Const expTime:string = process.env.JWT_TOKEN_EXP || '1h';

    // Const secretKey = process.env.JWT_TOKEN as string;

    // Const expTime = process.env.JWT_TOKEN_EXP as any;

    // Fix 1: Properly type the payload and options
    // Const payload = {} as jwt.JwtPayload;
    // Const options: jwt.SignOptions = {
    //   ExpiresIn: expTime,
    // };

    // Const token = jwt.sign(payload, secretKey, options);
    const isUser = await userMod.getUserDetailsByEmail(email);
    let userdetails;
    if (!isUser){
      const secretKey = process.env.JWT_TOKEN as string;

      const expTime = process.env.JWT_TOKEN_EXP as any;

      // Fix 1: Properly type the payload and options
      const payload = {} as jwt.JwtPayload;
      const options: jwt.SignOptions = {
        expiresIn: expTime,
      };

      const token = jwt.sign(payload, secretKey, options);
      const userCredential = await admin.auth().createUser({
        email: email,
        password: token,
        displayName: first_name,
      });
      userdetails = await userMod.CreateUser({
        first_name: first_name,
        last_name: last_name,
        email: email,
        uid: userCredential.uid,
      });
    } else {
      userdetails = await getUserByEmail(email);
    }
    if (userdetails){
      try {
        const updatedUser = await organization.AddUserToOrganization(organization_id, {
          user_id: userdetails.uid,
          email: email,
          first_name: first_name,
          last_name: last_name,
          organization_id: [organization_id],
        });

        if (updatedUser){
          const userRoleObj = {
            user_id: userdetails.uid,
            organizationId: organization_id,
            roleId: role,
            email: userdetails.email?userdetails.email:'',
          };
          organization.AssignRoleToUser(userRoleObj).then(() => {
            singlesigningOn(email, organization_id, domain).then((link) => {
              response.send({status: 1, data: link});
            })
              .catch((error) => {
                response.send({status: 0, error: error});
              });
          });
        } else {
          response.send({status: 0, error: 'Error adding user to organization'});
        }

      } catch (error) {
        logger.error('Error in AddUserToOrganization', {message: error});
        response.send({status: 0, error: 'User already exists'});
      }

    } else {
      response.send({status: 0, error: 'Error creating user'});
    }

  }

}

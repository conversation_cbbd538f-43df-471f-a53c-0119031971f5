import {Response} from 'express';
import {ProjectSceneModule} from '../../../modules/projectScene';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function updateBulkSceneFrames (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  const sceneMod = new ProjectSceneModule(project_id, organization_id);
  sceneMod.updateBulkSceneFrame(request.body)
    .then((res) => {
      response.status(201).json({ status: 1, message: res });
    })
    .catch((error: Error) => {
      logger.error('Error in updateBulkSceneFrames', {message: error});
      response
        .status(500)
        .json({ status: 0, error: error});
    });
}

import app  from '../../../app';
import request from 'supertest';
describe('Get BreadCrumbs Endpoint', () => {
  it('should return a data array with data', async () => {
    const res = await request(app)
      .get(`/publicapis/getBreadCrumbs?organization_id=8X3plU
            &project_id=65701718b5063acb09dbf6e4&currentScene=65701eddf364f150a1ee4357`)
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual([]);
  });

  it('should return an empty data array with no data', async () => {
    const res = await request(app)
      .get(`/publicapis/getBreadCrumbs?organization_id=8X3plU
            &project_id=65701718b5063acb0f6e4&currentScene=65701eddf364f150a1ee4357`)   // Invalid project Id
      .send();
    expect(res.statusCode).toEqual(400);
    expect(res.body).toHaveProperty('message');
    expect(res.body.message).toEqual({});
  });

  it('should return an empty data array with no data', async () => {
    const res = await request(app)
      .get(`/publicapis/getBreadCrumbs?organization_id=8X3plU
            &project_id=65701718b5063acb09dbf6e4&currentScene=65701eddf364f150a1ee43`)   // Invalid currentScene Id
      .send();
    expect(res.statusCode).toEqual(400);
    expect(res.body).toHaveProperty('message');
    expect(res.body.message).toEqual({});
  });
});

// First, import the Image type to ensure compatibility
import { VirtualTourModule } from '../../../modules/virtualTour';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import axios from 'axios';
import { tourCategory, tourType, VirtualTour, Image } from '../../../types/virtualTour';

// Define interface for external API data
interface ImageLink {
  'dest-image'?: string;
  position: string;
  [key: string]: any;
}

interface ImageData {
  name?: string;
  thumbnail?: string;
  url?: string;
  rotation?: string;
  links?: Record<string, ImageLink>;
  [key: string]: any;
}

interface ExternalData {
  images: Record<string, ImageData>;
  displayName?: string;
  details?: string;
  [key: string]: any;
}

// Define our transformed data types to match VirtualTour requirements
interface TransformedImageLink {
  _id: string;
  position: {
    x: string;
    y: string;
    z: string;
  };
  text: string;
  destination_img_id: string;
}

// Helper functions
function getDestinationImageId (destImageUrl: string | undefined, images: Record<string, ImageData>): string {
  if (!destImageUrl) {
    return '';
  }

  for (const [imageId, imageData] of Object.entries(images)) {
    if (imageData.url === destImageUrl) {
      return imageId;
    }
  }

  return '';
}

function getDestinationImageName (destImageUrl: string | undefined, images: Record<string, ImageData>): string {
  if (!destImageUrl) {
    return '';
  }

  for (const [, imageData] of Object.entries(images)) {
    if (imageData.url === destImageUrl) {
      return imageData.name || '';
    }
  }

  return '';
}

// Function to transform external data to our schema
function transformToDbSchema (
  data: ExternalData,
  organization_id: string,
  project_id: string,
  tour_id: string,
  existingTour: VirtualTour,
  description?: string,
  category?: string,
): Partial<VirtualTour> {
  try {
    const { images, displayName, details } = data;

    if (!images) {
      throw new Error('No images found in the data');
    }

    // Create the updated tour object with only required fields
    // Use Partial<VirtualTour> to match UpdateTour parameter type
    const transformedData: Partial<VirtualTour> = {
      _id: tour_id,
      name: displayName || existingTour?.name || 'Virtual Tour',
      // Use passed description from frontend, fall back to existing description
      description: description || existingTour?.description || '',
      organization: organization_id,
      project_id: project_id,
      category: existingTour?.category||category || tourCategory.INTERIOR,
      type: tourType.CUSTOM,
      images: {}, // Will be populated below
      created_at: details || new Date().toISOString(),
      updated_at: new Date().toISOString(),
      unitplan_id: existingTour?.unitplan_id || '',
      link: existingTour?.link, // Preserve existing link
    };

    // Transform images
    let order = 0;
    for (const [imageId, imageData] of Object.entries(images)) {
      // Create link objects according to our schema
      const links: Record<string, TransformedImageLink> = {};
      if (imageData.links) {
        for (const [linkId, linkData] of Object.entries(imageData.links)) {
          // Get destination image name for the link text
          const destImageName = getDestinationImageName(linkData['dest-image'], images);

          // Parse position coordinates
          const positionParts = linkData.position.split(' ');
          links[linkId] = {
            _id: linkId,
            position: {
              x: positionParts[0] || '0',
              y: positionParts[1] || '0',
              z: positionParts[2] || '0',
            },
            text: destImageName, // Use destination image name as link text
            destination_img_id: getDestinationImageId(linkData['dest-image'], images),
          };
        }
      }

      // Create image object that matches Image type
      const imageObj: Image = {
        id: imageId,
        name: imageData.name || `Image ${order + 1}`,
        thumbnail: imageData.thumbnail || '',
        url: imageData.url || '',
        rotation: imageData.rotation || '0 0 0',
        groupId: '',  // Add default value
        subGroupId: '', // Add default value
        links: {}, // Initialize links as an empty object
        order: order, // Assign the current order value
      };

      // Only add links if they exist
      if (Object.keys(links).length > 0) {
        imageObj.links = links;
      }

      // Only add order if it's needed
      if (order > 0) {
        imageObj.order = order;
      }

      transformedData.images![imageId] = imageObj;
      order++;
    }

    return transformedData;
  } catch (error) {
    logger.error('Error transforming data:', error);
    throw new Error(`Failed to transform data: ${(error as Error).message}`);
  }
}

// Main API handler
export async function ConvertExternalTour (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { organization_id } = request;

  if (!organization_id) {
    logger.error('Organization ID not found in request headers', { headers: request.headers });
    response.status(500).json({ status: 0, error: 'Organization ID not found' });
    return;
  }

  const { link, project_id, tour_id, description, category } = request.body;

  if (!link || !project_id || !tour_id) {
    logger.error('Missing required fields', { link, project_id, tour_id });
    response.status(400).json({ status: 0, error: 'Link, Project ID and Tour ID are required' });
    return;
  }

  try {
    const virtualTour = new VirtualTourModule(organization_id, project_id);

    const existingTour = await virtualTour.GetTourById(tour_id);

    if (!existingTour) {
      response.status(404).json({ status: 0, error: 'Tour not found' });
      return;
    }

    // Extract userId and project from the link
    const urlParts = link.split('/');
    const userId = urlParts[4];
    const project = urlParts[5];

    if (!userId || !project) {
      response.status(400).json({ status: 0, error: 'Invalid URL format' });
      return;
    }

    const apiUrl = `https://cloudfunctions-172924419383.asia-south1.run.app/getImages?userid=${userId}&project=${project}`;

    const externalResponse = await axios.get(apiUrl);
    console.log('External Response:', externalResponse.data);
    const externalData = externalResponse.data;

    const transformedData = transformToDbSchema(
      externalData,
      organization_id,
      project_id,
      tour_id,
      existingTour,
      description,
      category,
    );

    if (transformedData.type === tourType.CUSTOM) {
      transformedData.link = null;
    }

    // Update existing tour
    const result = await virtualTour.UpdateTour(tour_id, transformedData);

    if (transformedData.type === tourType.CUSTOM && result && 'link' in result) {
      const updatedTour = await virtualTour.RemoveField(tour_id, 'link');

      if (updatedTour) {
        response.status(200).json({
          status: 1,
          data: updatedTour,
          message: 'Tour updated successfully',
        });
        return;
      }
    }

    if (!result) {
      response.status(500).json({ status: 0, error: 'Failed to update tour data' });
      return;
    }

    response.status(200).json({
      status: 1,
      data: result,
      message: 'Tour updated successfully',
    });
  } catch (error) {
    logger.error('Error in ConvertExternalTour', { error });
    response.status(500).json({ status: 0, error: `Error converting tour: ${error}` });
  }
}

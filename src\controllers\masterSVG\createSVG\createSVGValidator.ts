import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { masterSVGType  } from '../../../types/masterSVG';

interface UploadedFiles {
  svgFile: Express.Multer.File[];
}

const CreateSVGValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  console.log(req.files);
  console.log(req.body);

  const files = req.files as UploadedFiles | undefined;
  console.log(files);
  if (files) {
    const requiredTextFields = [
      'type',
      'scene_id',
    ];

    const missingTextFields = requiredTextFields.filter(
      (field) => !(field in req.body),
    );

    if (missingTextFields.length > 0) {
      res.status(400).json({
        error: `Missing text fields: ${missingTextFields.join(', ')}`,
      });
    } else {
      body('type', 'Invalid type value. Please ensure that you are using a valid type value')
        .isIn(Object.values(masterSVGType)).run(req);
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        console.log(errors);
      } else {
        next();
      }
    }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export default CreateSVGValidate;

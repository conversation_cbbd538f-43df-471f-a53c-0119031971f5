import {  Request, Response } from 'express';
import { unitplanModule } from '../../../modules/unitplan';
import logger from '../../../config/logger';

export async function getTypes (request: Request, response: Response):Promise<void> {

  const project_id = request.query.project_id as string;

  const organization_id = request.headers.organization as string;
  console.log(project_id, organization_id);

  if (!organization_id){
    logger
      .error('no organization is found');
    response.send({status: 0, error: 'no organization is found'});
    return ;
  }
  const unitplanmod = new unitplanModule(project_id, organization_id);
  const unitplan = await unitplanmod.getTypes();
  if (unitplan) {
    response.send({ status: 1, data: unitplan });
  } else {
    response.send({ status: 1, data: [] });
  }

}

import { Request, Response } from 'express';
import { unitplanModule } from '../../../modules/unitplan';
import logger from '../../../config/logger';

export default async function editHotspots (
  request: Request,
  response: Response,
): Promise<void> {
  const { project_id, unitplan_id, hotspot_id, hotspots } = request.body;
  const organization_id = request.headers.organization as string;

  const updateHotspot = {
    ...(hotspots.text && { text: hotspots.text}),
    ...(hotspots.x !== undefined && { x: hotspots.x}),
    ...(hotspots.y !== undefined && { y: hotspots.y}),
    ...(hotspots.scale && { scale: hotspots.scale}),
    ...(hotspots.type && { type: hotspots.type}),
    ...('image_id' in hotspots && { image_id: hotspots.image_id }),
    ...(hotspots.label_id && { label_id: hotspots.label_id}),
    ...(hotspots.group_id && { group_id: hotspots.group_id}),
    ...(hotspots.subGroup_id && { subGroup_id: hotspots.subGroup_id}),
  };

  const unitplan = new unitplanModule(project_id, organization_id);
  unitplan.editHotspots(unitplan_id, hotspot_id, updateHotspot).then((res) => {
    response.status(200).json({status: 1, data: res});
  }).catch((error: Error) => {
    logger.error('Error in edit hotspots', {message: error});
    response.status(500).json({status: 0, error: 'Error edit hotspots '+error});
  });
}

import { LeadsModule } from '../../../modules/leads';
import { <PERSON>Status, LeadType, Leads } from '../../../types/leads';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { SessionModule } from '../../../modules/sessions';
// Import { ProjectModule } from '../../../modules/projects';
import { UserModule } from '../../../modules/user';
import { Session } from '../../../types/session';
import { SendgridModule } from '../../../modules/invite';
import logger from '../../../config/logger';
export async function CreateLead (
  request: ExtendedRequest,
  response: Response,
): Promise<Leads | void> {
  let organization_id = request.organization_id as string;
  const sessionId= request.body.session_id as string;
  const user_id = request.IsAuthenticated?.uid as string;
  let sessionData: Session;
  let newLead;
  // If (request.body.type === 'unit' && !request.body.unit_id) {
  //   Response
  //     .status(500)
  //     .json({ status: 0, error: 'No unit_id found for lead interested type-unit' });
  //   Return;
  // }
  if (sessionId) {
    const session = new SessionModule();
    sessionData = await session.getSessionById(sessionId);
    organization_id = sessionData.organization_id;
    if (!sessionData) {
      response
        .status(500)
        .json({ status: 0, error: 'Session ID not found' });
      return;
    }
    newLead = {
      session_id: sessionId,
      organization_id: sessionData.organization_id,
      name: request.body.name,
      phone_number: request.body.phone_number,
      email: request.body.email,
      duration_minutes: sessionData.duration_minutes,
      joining_time: new Date().toISOString(),
      type: LeadType.PROJECT,
      lead_source: request.body.source,
      start_time: sessionData.start,
      user_id: sessionData.user_id,
      lead_status: request.body.lead_status || LeadStatus.NEW,
      lead_industry_type: request.body.lead_industry_type,
      lead_product_interest: request.body.lead_product_interest,
      next_interaction_time: new Date().toISOString(),
      last_interaction_time: new Date().toISOString(),
      end_time: sessionData.end_time,
      lead_creation_time: new Date().toISOString(),
    };
  } else {
    newLead = {
      session_id: null,
      organization_id: organization_id,
      name: request.body.name,
      phone_number: request.body.phone_number,
      email: request.body.email,
      duration_minutes: null,
      joining_time: new Date().toISOString(),
      unit_id: request.body.type !== 'project' ? request.body.unit_id : undefined,
      lead_source: request.body.source,
      start_time: null,
      user_id: user_id,
      lead_industry_type: request.body.lead_industry_type,
      lead_product_interest: request.body.lead_product_interest,
      lead_status: request.body.lead_status || LeadStatus.NEW,
      next_interaction_time: new Date().toISOString(),
      last_interaction_time: new Date().toISOString(),
      end_time: null,
      lead_creation_time: new Date().toISOString(),
    };
  }
  console.log(organization_id);
  const leads = new LeadsModule(organization_id);
  await leads
    .CreateLead(newLead)
    .then((leadData) => {
      response.status(201).json({ status: 1, data: leadData });
      if (leadData) {
      // Send invite mail
        // Const project = new ProjectModule(organization_id);
        const user =new UserModule();
        user.getUsername(sessionData.user_id).then((username) => {
          // Project.getProjectById(sessionData.project_id).then((projectData) => {
          //   If (!projectData) {
          //     Throw new Error('Project Data not found');
          //   }
          //       Const projectname = projectData?.name;
          const subject = 'Join Our Virtual Property Showcase and Discover';
          const message = `<p>Dear ${leadData.name},
      <br></br><br></br>
      We would like to invite you to our virtual showcase meeting for property that I am representing.
      The meeting will take place on
      ${new Date(leadData.start_time).toUTCString()}. To attend the meeting,
      you can click on the link provided below at the scheduled time. You can join the meeting
      from the comfort of your home or office,
      and there's no need to download any software or create an account.
      You are free to use any device, but we strongly suggest you to use desktop or laptop with
      latest chrome browser for best experience.
      <br></br><br></br>
      Link: <a href=${sessionData.invite_link}>Click here to join </a>
      <br></br><br></br>
      Look forward to showcasing the property to you.
      <br></br>
      Best regards,
      <br></br>
      ${username || 'PropVR' }</p>`;
          const inviteData = {
            toAddress: [leadData.email],
            fromAddress: '<EMAIL>',
            message: message,
            subject: subject,
            project_name: '',
            first_name: leadData.name,
            invite_link: sessionData.invite_link,
            user_name: username? username : '',
            start: leadData.start_time,
          };
          const mailsender = new SendgridModule();
          mailsender.SendMail(inviteData);
          response.status(201).json({ status: 1, data: leadData });

        }).catch((err) => {
          logger.error('Error while finding project name', {message: err});
          response
            .status(500)
            .json({ status: 0, error: 'Error while finding project name'+ err });
        });
        // }).catch((error) => {
        //   Logger.error('Error while fetching username', {message: error});
        //   Response
        //     .status(500)
        //     .json({ status: 0, error: 'Error while fetching username'+ error });
        // });
      }
    })
    .catch((error: Error) => {
      logger.error('Error in CreateLead', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating the lead'+ error });
    });

}

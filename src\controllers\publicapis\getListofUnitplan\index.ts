
// Import { Request, Response } from 'express';
// Import { unitplanModule } from '../../../modules/unitplan';

// Export async function GetListofUnitplan (
//   Request:Request,
//   Response:Response,
// ):Promise<void>{
//   Const {project_id} = request.params;
//   Const organization_id = request.query.organization_id as string;
//   If (organization_id === undefined || organization_id === ''){
//     Response.status(400).send({status: 0, error: 'orgnization_id not found'});
//     Return;
//   }
//   Const unitplan = new unitplanModule(project_id, organization_id);

//   Const unitplanList = await unitplan.getListOfUnitplan();

//   If (unitplanList) {
//     Response.status(200).json({ status: 1, data: unitplanList });
//   } else {
//     Response.status(500).json({ status: 0, error: 'unitplans not found' });
//   }

// }

import { Request, Response } from 'express';
import { unitplanModule } from '../../../modules/unitplan';
import logger from '../../../config/logger';

export async function GetListofUnitplan (
  request:Request,
  response:Response,
):Promise<void>{
  const {project_id, organization_id} = request.params;
  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'orgnization_id not found'});
    return;
  }
  const unitplan = new unitplanModule(project_id, organization_id);

  try {
    unitplan.getUnitPlanDataWithMeasurementRange(project_id)
      .then((combinedResult) => {
        unitplan.updateUnitBedrooms(combinedResult);
        response.status(200).json({ status: 1, data: combinedResult });
      })
      .catch((error) => {
        logger.error('unitplans not found', {message: error});
        response.status(500).json({ status: 0, error: 'unitplans not found' });
      });

  } catch (error) {
    logger.error('Error in GetListofUnitplan', {message: error});
    response.status(500).json({ status: 0, error: 'unitplans not found' });
    throw error;
  }

}

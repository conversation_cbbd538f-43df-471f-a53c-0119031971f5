import { Request, Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';
import logger from '../../../config/logger';

const moveAssetValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('project_id', 'Project ID  is required').notEmpty(),
  body('sourceUrl', 'Source Url  is required').notEmpty(),
  body('destinationPath', 'Destination Path  is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Error:', {message: errors.array()});
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default moveAssetValidate;

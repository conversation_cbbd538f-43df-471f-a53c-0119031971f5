import { Request, Response } from 'express';
import { ProjectModule } from '../../../modules/projects';
import {httpRequestTimer} from '../../../utilis/metrics';
import logger from '../../../config/logger';
export async function GetListOfProjects (
  request: Request,
  response: Response,
): Promise<void> {
  const start = Date.now();
  try {
    const organization_id = request.headers.organization as string;
    //   Pass organization_id in this ProjectModule
    const project = new ProjectModule(organization_id);

    const projectData = await project.GetListOfAllProjects();

    if (projectData) {
      response.status(200).json({ status: 1, data: projectData });
    } else {
      logger.error('Projects not found');
      response.status(404).json({ status: 0, error: 'Projects not found' });
    }
  } finally {
    const responseTimeInMs = Date.now() - start;
    httpRequestTimer.labels(request.method, request.route.path,
      response.statusCode.toString()).observe(responseTimeInMs);
  }

}

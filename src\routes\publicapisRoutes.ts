import express from 'express';
import { getCountforProject } from '../controllers/publicapis/getProjectCount';
import { getCountforBuilding } from '../controllers/publicapis/getCountForBuildings';
import { GetBuildingByFloor } from '../controllers/publicapis/getbuildingByFloor';
import { GetListOfUnits } from '../controllers/publicapis/getUnits';
import { GetCommunities } from '../controllers/publicapis/getCountForCommunities';
import { GetAmenities } from '../controllers/publicapis/getAmenities';
import { getBreadCrumbs } from '../controllers/publicapis/getBreadCrumbs';
import { getListofLandmark } from '../controllers/publicapis/getListofLandmarks';
import { getAllScenes } from '../controllers/publicapis/getAllScenes';
import { getScene } from '../controllers/publicapis/getScene';
import { getListofProjectLandmark } from '../controllers/publicapis/getListofProjectLandmarks';
import { getAllProjectScenes } from '../controllers/publicapis/getAllProjectScenes';
import { GetSession } from '../controllers/publicapis/getsession';
import { GetListofUnitplan } from '../controllers/publicapis/getListofUnitplan';
import { GetUnitplan } from '../controllers/publicapis/getUnitPlan';
import { GetListofUnits } from '../controllers/publicapis/getListOfUnits';
import { GetTourById } from '../controllers/publicapis/GetTourById';
import listProjectsFromOrganization from '../controllers/publicapis/listprojectsfromOrganization';
import { ListTours } from '../controllers/publicapis/ListTours';
import { GetListOfBuildings } from '../controllers/publicapis/getListofBuildings';
import listProjectOrgValidate from '../controllers/publicapis/listprojectsfromOrganization/listProjectOrgValidator';
import getCountforProjectValidate from '../controllers/publicapis/getProjectCount/getCountforProjectValidator';
import getCommunitiesValidate from '../controllers/publicapis/getcommunities/getCommunitiesValidator';
import getListOfBuildingsValidate from '../controllers/publicapis/getListofBuildings/getListofBuildingValidator';
import getCountforBuildingValidate from '../controllers/publicapis/getCountForBuildings/getCountforBuildingValidator';
import getFloorDetailsValidate from '../controllers/publicapis/getbuildingByFloor/getFloorDetailsValidator';
import getListofUnitsValidate from '../controllers/publicapis/getListOfUnits/getListofUnitsValidator';
import getCountforUnitsValidate from '../controllers/publicapis/getUnits/getCountforUnitsValidator';
import getAmenitiesValidate from '../controllers/publicapis/getAmenities/getAmenitiesValidator';
import getBreadCrumbValidate from '../controllers/publicapis/getBreadCrumbs/getBreadCrumbValidate';
import getListofLandmarkValidator from '../controllers/publicapis/getListofLandmarks/getListoflandmarkValidator';
import getAllScenesValidator from '../controllers/publicapis/getAllScenes/getAllScenesValidator';
import getSceneValidator from '../controllers/publicapis/getScene/getSceneValidator';
import getProjectLandmarksValidate from
  '../controllers/publicapis/getListofProjectLandmarks/getProjectLandmarksValidate';
import getProjectScenesValidate from '../controllers/publicapis/getAllProjectScenes/getProjectScenesValidator';
import getSessionValidate from '../controllers/publicapis/getsession/getSessionValidator';
import getListUnitplanValidate from '../controllers/publicapis/getListofUnitplan/getListUnitplanValidators';
import getUnitplanValidate from '../controllers/publicapis/getUnitPlan/getUnitplanValidator';
import getTourValidate from '../controllers/publicapis/GetTourById/getTourValidator';
import getListToursValidate from '../controllers/publicapis/ListTours/getListToursValidator';
import { getProjectSceneId } from '../controllers/publicapis/getProjectSceneId';
import { GetProject } from '../controllers/publicapis/getProject';
import GetOrganization from '../controllers/publicapis/getOrganization';
import { CreateLead } from '../controllers/publicapis/createLead';
import CreateLeadValidate from '../controllers/publicapis/createLead/createLeadValidator';
import { GetLeadForGuest } from '../controllers/publicapis/getLeadForGuest';
import getOptionsValidate from '../controllers/publicapis/getOptions/getOptionsValidator';
import { getOptions } from '../controllers/publicapis/getOptions';
import { getCategories } from '../controllers/publicapis/getCategories';
import getCategoriesValidate from '../controllers/publicapis/getCategories/getCategoriesValidator';
import { getFilteredUnits } from '../controllers/publicapis/getFilteredUnits';
import { getFilterDataPoints } from '../controllers/publicapis/getFilterDataPoints';
import { getTranslations } from '../controllers/publicapis/getTranslations/getTranslations';
import { Translate } from '../controllers/publicapis/translate/translate';
import { getParsedUnits } from '../controllers/parsedUnits';
import getUnitsValidator from '../controllers/parsedUnits/getUnitsValidator';
const router = express.Router();
router.post('/organization/:organization_id/project/:project_id/getProject', GetProject);
router.get(
  '/organization/:organization_id/listProjectsFromOrganization',
  listProjectOrgValidate,
  listProjectsFromOrganization);
router.get(
  '/organization/:organization_id/project/:project_id/getCountforProject',
  getCountforProjectValidate,
  getCountforProject);
router.get(
  '/organization/:organization_id/project/:project_id/getCommunities',
  getCommunitiesValidate,
  GetCommunities);
router.get(
  '/organization/:organization_id/project/:project_id/getListOfBuildings',
  getListOfBuildingsValidate,
  GetListOfBuildings);
router.get(
  '/organization/:organization_id/project/:project_id/getCountforBuilding',
  getCountforBuildingValidate,
  getCountforBuilding);
router.get(
  '/organization/:organization_id/project/:project_id/getFloorDetails',
  getFloorDetailsValidate,
  GetBuildingByFloor);
router.get(
  '/organization/:organization_id/project/:project_id/getListofUnits',
  getListofUnitsValidate,
  GetListofUnits);
router.get(
  '/organization/:organization_id/project/:project_id/getCountForUnits',
  getCountforUnitsValidate,
  GetListOfUnits);
router.get(
  '/organization/:organization_id/project/:project_id/GetAmenities',
  getAmenitiesValidate,
  GetAmenities);
router.get(
  '/organization/:organization_id/project/:project_id/getBreadCrumbs',
  getBreadCrumbValidate,
  getBreadCrumbs);
router.get(
  '/organization/:organization_id/getListofLandmarks',
  getListofLandmarkValidator,
  getListofLandmark);
router.get('/organization/:organization_id/getAllScenes', getAllScenesValidator, getAllScenes);
router.get('/organization/:organization_id/:scene_id/getScene', getSceneValidator, getScene);
router.get(
  '/organization/:organization_id/project/:project_id/getListofProjectLandmark',
  getProjectLandmarksValidate,
  getListofProjectLandmark);
router.get(
  '/organization/:organization_id/project/:project_id/getAllScenes',
  getProjectScenesValidate,
  getAllProjectScenes);
router.get('/getSession', getSessionValidate, GetSession);
router.get(
  '/organization/:organization_id/project/:project_id/getListOfUnitplan',
  getListUnitplanValidate,
  GetListofUnitplan);
router.get(
  '/organization/:organization_id/project/:project_id/getUnitplan/:unitplan_id',
  getUnitplanValidate,
  GetUnitplan);
router.get('/organization/:organization_id/project/:project_id/GetTourById', getTourValidate, GetTourById);
router.get('/organization/:organization_id/project/:project_id/ListTours', getListToursValidate, ListTours);
router.get('/organization/:organization_id/project/:project_id/getProjectSceneId', getProjectSceneId);
router.get('/organization/:organization_id/getOrganization', GetOrganization);
router.post('/CreateLead', CreateLeadValidate, CreateLead);
router.post('/GetLead', GetLeadForGuest);
router.get(
  '/organization/:organization_id/project/:project_id/getOptions',
  getOptionsValidate,
  getOptions,
);
router.get('/organization/:organization_id/project/:project_id/getCategories',
  getCategoriesValidate,
  getCategories);
router.get('/organization/:organization_id/project/:project_id/filterunits', getFilteredUnits);
router.get('/organization/:organization_id/project/:project_id/filterDataPoints', getFilterDataPoints );
router.get('/organization/:organization_id/getTranslations', getTranslations );
router.get('/organization/:organization_id/translate', Translate );

router.get('/organization/:organization_id/project/:projectId/getParsedUnits',
  getUnitsValidator,
  getParsedUnits,
);
export default router;

import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

export function validateUrl (value: string): boolean {
  const urlPattern = new RegExp(
    '^' +
    '(?:(?:(?:https?|ftp):)?\\/\\/)' + // Protocol
    '(?:\\S+(?::\\S*)?@)?' + // Authentication
    '(?:(?!10(?:\\.\\d{1,3}){3})' + // IP exclusion
    '(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})' +
    '(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})' +
    '(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])' + // IP octets
    '(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}' +
    '(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|' + // Domain
    '(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)' +
    '(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*' +
    '(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))' + // TLD
    '(?::\\d{2,5})?' + // Port
    '(?:[/?#]\\S*)?' + // Path
    '$', 'i', // Case-insensitive
  );
  return urlPattern.test(value);
}

export const shorterUrlValidator = [
  body('org_url', 'URL is required').notEmpty(),
  body('org_url', 'Invalid URL format').custom((value) => validateUrl(value)),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

import { Request, Response } from 'express';
import logger from '../../../config/logger';
import { MiniMapModule } from '../../../modules/miniMap';

export async function GetMiniMap (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.query.project_id as string;
  const minimap_id = request.query.minimap_id as string;
  const organization_id = request.headers.organization as string;
  if (!organization_id){
    logger.error('No organization is found ');
    response.send({status: 0, error: 'no organization is found'});
    return ;
  }
  const miniMap = new MiniMapModule(project_id, organization_id);
  try {
    const miniMapData = await miniMap.GetMiniMap(minimap_id);

    if (miniMapData) {
      response.send({ status: 1, data: miniMapData });
    } else {
      response.send({ status: 1, data: [] });
    }
  } catch (error) {
    logger.error('Error while fetching minimap', { message: error });
    response.status(500).send({ status: 0, error: 'Error while fetching minimap' });
  }
}

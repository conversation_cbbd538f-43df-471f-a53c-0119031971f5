import {Request, Response} from 'express';
import { ProjectLandmarkModule } from '../../../modules/projectLandmark';
import logger from '../../../config/logger';

export default async function updateLandmark (request:Request, response:Response):Promise<void>{
  const organization_id = request.headers.organization as string;
  const landmark = new ProjectLandmarkModule(request.body.project_id, organization_id);
  landmark.updateLandmark(request.body).then((res) => {
    response.send({status: 1, data: res});
  })
    .catch((err) => {
      logger.error('Error in updateLandmark', {message: err});
      response.send({status: 0, meaasage: err});
    });
  return;
}

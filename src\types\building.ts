import { Types } from 'mongoose';

type Unit = string;
export enum BuildingType {
  TOWER = 'tower',
  VILLA = 'villa'
}
export type Floor = {
    floor_id:string;
    floor_name: string;
    units: Array<Unit>;
    order: number;
    updated_at : string;
  }
export type Building = {
  _id: Types.ObjectId;
  project_id: string;
  name: string;
  type:string;
  total_floors:number;
  floors: Record<string, Floor>;
  community_id:string;
  thumbnail : string;
  updated_at : string;
};
export type CreateBuildingType = {
  _id: Types.ObjectId;
  project_id: string;
  name: string;
  type:string;
  total_floors:number;
  community_id:string;
  thumbnail? : string;
  updated_at : string;
};

export type FloorsRange = {
  minFloor: number,
  maxFloor: number
}

export type FilterBuilding = {
  id:string,
  name:string,
  floors: FloorsRange
}

export type transformedFilterBuilding = {
  [key:string]:FilterBuilding
}
export type updateBuildingType = {
  name?:string,
  total_floors:number,
  type?:string,
  floors: Record<string, Floor>;
  community_id?: string,
  order?:number
  thumbnail?:string,
  project_id: string;
  updated_at : string;
}

export type bulkFloorUpdateQuery = {
  floor_id:string
  order?: number,
}

export type bulkFloorUpdateType = {
    query:bulkFloorUpdateQuery[],
    project_id:string
}

import { ExtendedRequest } from '../../../types/extras';
import { AmenityModule } from '../../../modules/amenity';

import { Response } from 'express';
import logger from '../../../config/logger';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const amenity = new AmenityModule(project_id, organization_id);
  const amenity_id = request.body.amenity_id;
  const timeStamp = request.body.timeStamp;

  await amenity
    .moveToTrash(amenity_id, project_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving project amenitys to trash: '+ error });
    });
}

import { check, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { Theme } from '../../../types/projects';
interface UploadedFiles {
  thumbnail: Express.Multer.File[];
}

const createOrganizationValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  console.log(req.files);
  console.log(req.body);

  const files = req.files as UploadedFiles | undefined;

  if (files) {
    if (!files.thumbnail) {
      res
        .status(400)
        .json({ error: 'Thumbnail Image is required.' });
    } else {
      const requiredTextFields = [
        'name',
        'founding_date',
        'contact_email',
        'is_public',
        'phone_number',
        'address',
        'website',
        'max_users',
      ];
      if (req.body.theme === Theme.CUSTOM) {
        requiredTextFields.push('primary', 'secondary');
      }
      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        res.status(400).json({
          error: `Missing text fields: ${missingTextFields.join(', ')}`,
        });
      } else {
        check('theme', 'Invalid project theme value. Please ensure that you are using a valid theme value')
          .isIn(Object.values(Theme)).run(req);

        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export default createOrganizationValidate;

import mongoose from 'mongoose';
import { shortUrlSchema } from '../../schema/shorterUrlSchema';
import { ShortUrl, shortUrlInput } from '../../types/shorterUrl';
import logger from '../../config/logger';
import { Models } from '../../types/extras';

function alphaNumericString (length: number) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-';
  let retVal = '';
  for (let i = 0, n = charset.length; i < length; ++i) {
    retVal += charset.charAt(Math.floor(Math.random() * n));
  }
  return retVal;
}

export class ShortUrlModule {
  private model: mongoose.Model<ShortUrl>;

  constructor () {
    this.model = mongoose.model<ShortUrl>(`${Models.SHORTURL}`, shortUrlSchema);
  }

  public async generateShortUrl (shortUrl: shortUrlInput): Promise<ShortUrl> {
    logger.info('generateShortUrl Called', {shortUrl: shortUrl});
    const { org_url, title, description, image } = shortUrl;
    const _id = alphaNumericString(6);
    const short_url = `${process.env.ROOT_TINY_URL}` + 'tiny/' + `${_id}`;

    const existingUrl = await this.model.findOne({ org_url });
    if (existingUrl) {
      existingUrl.title = title;
      existingUrl.description = description;
      existingUrl.image = image;
      await existingUrl.save();
      logger.info('generateShortUrl Successfull', {existingUrl: existingUrl});
      return existingUrl;
    }

    const newUrl = new this.model({
      org_url,
      short_url,
      _id,
      date: new Date(),
      title,
      description,
      image,
    });

    await newUrl.save();
    logger.info('generateShortUrl Successfull', {newUrl: newUrl});
    return newUrl;

  }

  public async redirectToOrgUrl (shortUrl: ShortUrl): Promise<{
    _id: string,
    org_url: string,
    date: Date,
    description?: string,
    title?: string,
    image?: string
  } | null> {
    logger.info('redirectToOrgUrl Called', {shortUrl: shortUrl});
    const { _id } = shortUrl;

    const url = await this.model.findOne({ _id });
    if (url && url.org_url) {
      const updatedDate = new Date();
      await this.model.updateOne(
        { _id: _id },
        { $set: { date: updatedDate } },
      );
      logger.info('redirectToOrgUrl Successfull', {shortUrl: shortUrl});
      return {
        _id: _id,
        org_url: url.org_url,
        date: updatedDate,
        image: url.image,
        description: url.description,
        title: url.title,
      };
    }
    return null;
  }
}

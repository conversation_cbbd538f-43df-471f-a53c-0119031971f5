import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import { PublicApis } from '../../../modules/publicapis';
import logger from '../../../config/logger';

export async function getFilterDataPoints (
  request:ExtendedRequest,
  response:Response,
):Promise<void>{
  const { project_id } = request.params;
  const query = {...request.query};
  const unit = new PublicApis(project_id);
  const filterDataPoints = await unit.getFilterDataPoints(project_id, query);
  if (filterDataPoints) {
    response.status(200).json({ status: 1, data: filterDataPoints });
  } else {
    logger.error('No Units found');
    response.status(404).json({ status: 0, error: 'No Units found' });
  }

}

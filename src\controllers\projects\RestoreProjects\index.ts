import { ExtendedRequest } from '../../../types/extras';

import { Response } from 'express';
import logger from '../../../config/logger';
import { ProjectModule } from '../../../modules/projects';
export async function restoreProjects (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const project = new ProjectModule(organization_id);
  const trash_id = request.body.trash_id;
  await project
    .restoreProjects(organization_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Project got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreProjects', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error in restoreProjects : '+ error });
    });
}

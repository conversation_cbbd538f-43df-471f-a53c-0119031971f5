import { LeadsModule } from '../../../modules/leads';
import { Request, Response } from 'express';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';

export async function GetLeadForGuest (
  request: Request,
  response: Response,
): Promise<void> {
  try {
    const lead_id = request.body.lead_id;
    const session_id = request.body.session_id;

    const sessionModule = new SessionModule();
    const session_response = await sessionModule.getSessionById(session_id);

    if (!session_response || !session_response.organization_id) {
      response.status(404).json({ status: 0, error: 'Organization ID not found' });
      return;
    }

    const organization_id = session_response.organization_id;

    const leadModule = new LeadsModule(organization_id);
    const leadDetail = await leadModule.GetLeadById(lead_id);

    if (leadDetail) {
      response.status(200).json({ status: 1, data: leadDetail });
    } else {
      response.status(404).json({ status: 0, error: 'No Lead Found with the specified ID' });
    }
  } catch (error) {
    logger.error('Error in GetLeadForGuest', {message: error});
    response.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

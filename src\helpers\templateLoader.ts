import fs from 'fs';
import path from 'path';
import logger from '../config/logger';

interface TemplateVariables {
  [key: string]: string;
}

export function loadTemplate (templateName: string, variables: TemplateVariables): string {
  try {
    const templatePath = path.resolve(process.cwd(), 'src', 'templates', `${templateName}.html`);
    // Check if template file exists
    if (!fs.existsSync(templatePath)) {
      throw new Error(`Template file not found: ${templatePath}`);
    }
    let templateContent = fs.readFileSync(templatePath, 'utf8');

    // Replace variables in template
    Object.keys(variables).forEach((key) => {
      const placeholder = `{{${key}}}`;
      const value = variables[key] || '';
      templateContent = templateContent.replace(new RegExp(placeholder, 'g'), value);
    });

    logger.info('Template loaded successfully', { templateName, variableCount: Object.keys(variables).length });
    return templateContent;

  } catch (error) {
    logger.error('Error loading template', { templateName, error });
    throw new Error(`Failed to load template: ${templateName}`);
  }
}

export function loadEmailVerificationTemplate (verificationLink: string, displayName: string): string {
  return loadTemplate('emailVerification', {
    verificationLink,
    displayName,
  });
}

export function loadPasswordResetTemplate (resetLink: string, displayName: string): string {
  return loadTemplate('passwordReset', {
    resetLink,
    displayName,
  });
}

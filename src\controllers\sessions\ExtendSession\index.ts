import { Request, Response } from 'express';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';
import { ExportSessionInput } from '../../../types/session';
export async function ExtendSession (
  request: Request,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const organization_id = await session.getOrganizationId(request.body.session_id);
  const update_session_data : ExportSessionInput  = {
    session_id: request.body.session_id,
    duration: parseInt(request.body.duration),
  };
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }

  session
    .ExtendSession(update_session_data)
    .then((sessionData) => {
      response.send({ status: 1, data: sessionData });
    })
    .catch((error) => {
      logger.error('Error in UpdateSession', {message: error});
      response.send({ status: 0, error: 'Error while updating session' + error });
    });
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';

const convertExternalTourValidate = [
  header('organization', 'Organization is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),

  body('project_id', 'Invalid or missing Project ID').notEmpty().isString(),
  body('tour_id', 'Invalid or missing Tour ID').notEmpty().isString(),
  body('link', 'Invalid or missing Link URL').notEmpty().isString().isURL(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default convertExternalTourValidate;

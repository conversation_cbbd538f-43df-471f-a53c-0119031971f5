import app from '../../../app';
import request from 'supertest';
import path from 'path';
import fs from 'fs';
const dummyJpgFile = fs.readFileSync(path.resolve(__dirname, '../../../testassets/dummy.jpg'));

describe('POST Endpoints', () => {

  it('should create an amenity', async () => {
    const res = await request(app)
      .post('/amenity/CreateAmenity')
      .field('name', 'Amenity Name')
      .field('category', 'indoor')
      .field('community_id', '65e5f76c7608d0d42f049d58')
      .field('project_id', '65e04e579bda4a8afbbbdb5e')
      .attach('thumbnail', dummyJpgFile, { filename: 'dummy.jpg' })
      .set('organization', process.env.TEST_ORG as string)
      .set('accesstoken', process.env.TEST_ACCESSTOKEN as string);
    expect(res.statusCode).toEqual(201); // Assuming successful creation returns status code 200
    expect(res.body).toHaveProperty('status', 1);
    expect(res.body).toHaveProperty('data');

    // Add more assertions as needed to verify the response
  }, 7000);
  it('should handle invalid file structure', async () => {
    const res = await request(app)
      .post('/amenity/CreateAmenity')
      .field('name', 'Amenity Name')
      .field('category', 'indoor')
      .field('community_id', '65e5f76c7608d0d42f049d58')
      .field('project_id', '65e04e579bda4a8afbbbdb5e')
      .set('organization', process.env.TEST_ORG as string)
      .set('accesstoken', process.env.TEST_ACCESSTOKEN as string);
    expect(res.statusCode).toEqual(400); // Assuming successful creation returns status code 200
    expect(res.body).toHaveProperty('status', 0);
    expect(res.body).toHaveProperty('error');
    // Add more assertions as needed to verify the response
  });
  it('should handle missing files', async () => {
    const res = await request(app)
      .post('/amenity/CreateAmenity')
      .field('name', 'Amenity Name')
      .field('category', 'indoor')
      .field('community_id', '65e5f76c7608d0d42f049d58')
      .field('project_id', '65e04e579bda4a8afbbbdb5e')
      .set('organization', process.env.TEST_ORG as string)
      .set('accesstoken', process.env.TEST_ACCESSTOKEN as string);

    expect(res.statusCode).toEqual(400); // Assuming successful creation returns status code 200
    expect(res.body).toHaveProperty('status', 0);
    expect(res.body).toHaveProperty('error');
    // Add more assertions as needed to verify the response
  });
  it('should handle missing text files', async () => {
    const res = await request(app)
      .post('/amenity/CreateAmenity')
      .field('name', 'Amenity Name')
      .field('category', 'indoor')
      .field('community_id', '65e5f76c7608d0d42f049d58')
      .field('project_id', '65e04e579bda4a8afbbbdb5e')
      .set('organization', process.env.TEST_ORG as string)
      .set('accesstoken', process.env.TEST_ACCESSTOKEN as string);

    expect(res.statusCode).toEqual(400); // Assuming successful creation returns status code 200
    expect(res.body).toHaveProperty('status', 0);
    expect(res.body).toHaveProperty('error');
    // Add more assertions as needed to verify the response
  });
  it('should handle error if accesstoken is not passed', async () => {
    const res = await request(app)
      .post('/amenity/CreateAmenity')
      .field('name', 'Amenity Name')
      .field('category', 'indoor')
      .field('community_id', '65e5f76c7608d0d42f049d58')
      .field('project_id', '65e04e579bda4a8afbbbdb5e')
      .set('organization', process.env.TEST_ORG as string)
      .attach('thumbnail', dummyJpgFile, { filename: 'dummy.jpg' });

    expect(res.statusCode).toEqual(400); // Assuming successful creation returns status code 200
    expect(res.body).toHaveProperty('status', 0);
    // Add more assertions as needed to verify the response
  });
  it('should handle error if accesstoken has expired', async () => {
    const res = await request(app)
      .post('/amenity/CreateAmenity')
      .field('name', 'Amenity Name')
      .field('category', 'indoor')
      .field('community_id', '65e5f76c7608d0d42f049d58')
      .field('project_id', '65e04e579bda4a8afbbbdb5e')
      .set('organization', process.env.TEST_ORG as string)
      .set('accesstoken', process.env.TEST_EXPIREDTOKEN as string)
      .attach('thumbnail', dummyJpgFile, { filename: 'dummy.jpg' });

    expect(res.statusCode).toEqual(400); // Assuming successful creation returns status code 200
    expect(res.body).toHaveProperty('status', 0);
    expect(res.body).toHaveProperty('error');

    // Add more assertions as needed to verify the response
  });
});

import { Request, Response } from 'express';
import { Units } from '../../../types/units';
import { UnitModule } from '../../../modules/units';
import { Types } from 'mongoose';
import logger from '../../../config/logger';

export async function CreateUnits (
  request: Request,
  response: Response,
): Promise<Units | void> {

  const { unitplan_id, project_id, name, status, metadata,
    floor_id, building_id, community_id, price, currency, tour_id, measurement, measurement_type,
    balcony_measurement, balcony_measurement_type, cta_link, max_price, suite_area, suite_area_type,
  } = request.body;

  // Const project_id = request.headers.project_id as string;
  const unit = new UnitModule(project_id);
  await unit
    .createUnit({_id: new Types.ObjectId(), unitplan_id, project_id, name, status, metadata,
      floor_id, building_id, community_id, currency, price, tour_id, measurement, measurement_type,
      balcony_measurement, balcony_measurement_type, cta_link, max_price, suite_area, suite_area_type,
    })
    .then((unitData) => {
      response.status(201).json({ status: 1, data: unitData });
    })
    .catch((error: Error) => {
      logger.error('Error in createUnit', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating the units' });
      console.error(error);
    });
}

import {  Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function GetTourGroupById (
  req: ExtendedRequest,
  res: Response,
): Promise<void> {
  try {
    const organization_id = req.organization_id;
    const { project_id, tour_id, group_id } = req.params;

    if (!organization_id) {
      res.status(400).json({ status: 0, error: 'Organization ID is required' });
      return;
    }

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const group = await tourModule.GetGroupById(tour_id, group_id);

    if (!group) {
      res.status(404).json({ status: 0, error: 'Group not found' });
      return;
    }

    res.status(200).json({ status: 1, data: group });
  } catch (error) {
    logger.error('Error in GetGroupById', { error });
    res.status(500).json({ status: 0, error: `Error fetching group: ${error}` });
  }
}

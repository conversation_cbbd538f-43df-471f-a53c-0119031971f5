import { MailerModule } from '../../../modules/mailer';
import { ProjectModule } from '../../../modules/projects';
import { UserModule } from '../../../modules/user';
import { Request, Response } from 'express';
import { mailUserData } from '../../../types/mailer';
import { SessionModule } from '../../../modules/sessions';
import { NotificationModule } from '../../../modules/notification';
import { UserDetailsWithFCM } from '../../../types/meetings';
export default async function sendInviteHost (
  request: Request,
  response: Response,
):Promise<void> {
  try {
    const mailer = new MailerModule();
    const session_id = request.body.session_id;
    const sessionModule = new SessionModule();
    const sessionData = await sessionModule.getSessionById(session_id);
    const notificationModule = new NotificationModule(sessionData.user_id);
    if (!sessionData) {
      response.status(404).send({ status: 0, message: 'Session Data not found' });
      return;
    }
    const projectModule = new ProjectModule(sessionData.organization_id);
    const project = await projectModule.getProjectById(sessionData.project_id as string);
    if (!project) {
      response.status(404).send({ status: 0, message: 'Project Data not found' });
      return;
    }

    const userModule = new UserModule();
    const userDetail = await userModule.GetUserDetails(sessionData.user_id) as UserDetailsWithFCM;
    const username = await userModule.getUsername(sessionData.user_id);
    const userEmail = await userModule.getUserEmail(sessionData.user_id);
    if (!username || !userEmail) {
      response.status(404).send({ status: 0, message: 'User Data not found' });
      return;
    }
    const userData: mailUserData = {
      username: username,
      useremail: userEmail,
    };
    const thread_id = await mailer.SendHostInviteMail(sessionData, project, userData);
    console.log('Look here', thread_id, sessionData, thread_id);
    if (!sessionData.thread_id && thread_id) {
      await sessionModule.UpdateThreadId(session_id, thread_id);
    }
    const tokens = userDetail.fcmToken;
    if (!tokens || (Array.isArray(tokens) && tokens.length === 0)) {
      response.status(400).json({
        status: 0,
        message: 'No FCM tokens available',
        error: 'User has no registered FCM tokens',
      });
      return;
    }

    const payload = {
      _id: sessionData.user_id,
      notification: {
        title: 'Invite Sent',
        body: 'Invite Sent Successfully',
      },
      data: {
        meeting_id: sessionData._id.toString(),
        type: 'invite_sent',
        user_id: sessionData.user_id,
        organization: sessionData.organization_id,
      },
      tokens,
    };
    notificationModule.sendNotification(payload);
    response.status(200).send({ status: 1, data: 'Invite Sent Successfully' });
  } catch (error) {
    response.status(500).send({ status: 0, message: error });
  }

}

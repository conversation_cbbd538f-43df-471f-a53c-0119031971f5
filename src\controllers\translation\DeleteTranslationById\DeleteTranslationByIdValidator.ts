import { Request, Response, NextFunction } from 'express';
import { validationResult, query } from 'express-validator';
import logger from '../../../config/logger';

const DeleteTranslationByIdValidator = [
  query('translationId', 'Translation Id is required').notEmpty().isString(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Error in DeleteTranslationByIdValidator', errors);

      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default DeleteTranslationByIdValidator;

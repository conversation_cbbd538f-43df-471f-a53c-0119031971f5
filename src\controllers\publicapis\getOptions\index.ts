
import { Request, Response } from 'express';
import { SidebarModule } from '../../../modules/sidebar';
import logger from '../../../config/logger';

export async function getOptions (
  request:Request,
  response:Response,
):Promise<void>{
  const { project_id} = request.params;

  const sidebarMod = new SidebarModule(project_id);

  const options = await sidebarMod.getOptions();

  if (options) {
    response.status(200).json({ status: 1, data: options });
  } else {
    logger.error('Options not found');
    response.status(404).json({ status: 0, error: 'Options not found' });
  }

}

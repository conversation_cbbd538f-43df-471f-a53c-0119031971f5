import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';
import { LeadSource, LeadStatus } from '../../../types/leads';

const CreateLeadValidate = [

  body('session_id', 'Session ID is required').optional(),
  body('source', 'Lead Source is required').notEmpty(),
  body('source', 'Invalid source value. Please ensure that you are using a valid status value')
    .isIn(Object.values(LeadSource)),
  body('status', 'Invalid Status value. Please ensure that you are using a valid status value').optional()
    .isIn(Object.values(LeadStatus)),
  body('name', 'Lead Name is required').notEmpty(),
  body('email', 'Lead Email is required').notEmpty(),
  body('email', 'Email is invalid').isEmail(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default CreateLeadValidate;

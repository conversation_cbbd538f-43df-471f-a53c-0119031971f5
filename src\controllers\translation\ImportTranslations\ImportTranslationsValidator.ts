import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import logger from '../../../config/logger';

const ImportTranslationsValidator = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  try {
    // Check if Content-Type is multipart/form-data
    const contentType = req.headers['content-type'] || '';
    if (!contentType.includes('multipart/form-data')) {
      logger.error('Invalid content type', { contentType });
      res.status(400).json({
        status: 0,
        error: 'Request must be multipart/form-data',
      });
      return;
    }

    // Check for any validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Validation errors', errors);
      res.status(400).json({
        status: 0,
        errors: errors.array(),
      });
      return;
    }

    next();
  } catch (error) {
    logger.error('Error in ImportTranslationsValidator', { error });
    res.status(500).json({
      status: 0,
      error: 'Error validating import request',
    });
  }
};

export default ImportTranslationsValidator;

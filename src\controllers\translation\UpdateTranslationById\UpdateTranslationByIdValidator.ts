import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';
import { SupportedLanguages } from '../../../types/projects';
import logger from '../../../config/logger';

const UpdateTranslationByIdValidate = [
  body('translationId', 'Translation Id is required').notEmpty().isString(),
  body('targetLanguageCode', 'Target Language Code is required').notEmpty(),
  body('targetLanguageCode', 'Target Language Code is not supported').isIn(Object.values(SupportedLanguages)),
  body('translation', 'Please provide the new translation for this word').notEmpty().isString(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Error in UpdateTranslationByIdValidate', errors);
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default UpdateTranslationByIdValidate;

import { GalleryModule } from '../../../modules/gallery';
import { Request, Response } from 'express';
import { galleryItem } from '../../../types/gallery';
import logger from '../../../config/logger';
export async function DeleteGalleryItem (
  request: Request,
  response: Response,
): Promise<galleryItem | void> {
  const project_id = request.body.project_id;
  const organization_id = request.body.organization;
  const gallery = new GalleryModule(project_id, organization_id);
  const item_id = request.body.item_id;
  gallery
    .DeleteGalleryItem(item_id)
    .then((deletedGalleryItem) => {
      response.status(201).json({ status: 1, data: deletedGalleryItem });
    })
    .catch((error: Error) => {
      logger
        .error('Error while creating the gallery item:', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting the gallery item'+ error });
    });
}

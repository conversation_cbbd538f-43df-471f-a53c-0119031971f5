import {Request, Response} from 'express';
import {ProjectSceneModule} from '../../../modules/projectScene';
import logger from '../../../config/logger';
export default async function updateScene (request:Request, response:Response): Promise<void> {
  const organization_id = request.headers.organization as string;
  const projectScene = new ProjectSceneModule(request.body.project_id, organization_id);
  projectScene.UpdateScene(request.body.scene_id, request.body).then(async (res) => {
    response.send({status: 1, data: res});
  }).catch((error) => {
    logger.error('Error in updateScene', {message: error});
    response.send({status: 0, message: error});
  });
}

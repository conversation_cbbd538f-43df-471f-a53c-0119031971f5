import { Request, Response } from 'express';
import { OrganizationModule } from '../../../modules/organization';

const organizationModule = new OrganizationModule();

export default async function checkUserExistence (
  req: Request,
  res: Response,
): Promise<Response> {
  const { unique_org_id } = req.params;
  if (
    !unique_org_id ||
    typeof unique_org_id !== 'string' ||
    !unique_org_id.trim()
  ) {
    console.error('Missing or invalid unique_org_id in request');
    return res.status(400).json({
      error: 'unique_org_id is required and must be a non-empty string',
    });
  }

  try {
    console.info(`Checking if unique_org_id "${unique_org_id}" exists`);
    const exists = await organizationModule.Isunique_org_idExists(
      unique_org_id,
    );

    if (exists) {
      console.info(`unique_org_id "${unique_org_id}" exists`);
      return res.status(200).json({
        exists: true,
        message: `unique_org_id "${unique_org_id}" already exists`,
      });
    }

    console.info(`unique_org_id "${unique_org_id}" does not exist`);
    return res.status(200).json({
      exists: false,
      message: `unique_org_id "${unique_org_id}" is available`,
    });
  } catch (error) {
    console.error(`Error checking unique_org_id "${unique_org_id}"`, {
      error: error,
    });
    return res.status(500).json({ error: 'Internal Server Error' });
  }
}

import { VirtualTourModule } from '../../../modules/virtualTour';
import { VirtualTour } from '../../../types/virtualTour';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function GetTourById (
  request: ExtendedRequest,
  response: Response,
): Promise<VirtualTour | void>{
  const { project_id, organization_id } = request.params;
  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'organization_id not found'});
    return;
  }
  const tour_id = request.query.tour_id as string;
  const virtualTour = new VirtualTourModule(organization_id, project_id);
  virtualTour.GetTourById(tour_id).then((virtualTourData) => {
    if (virtualTourData===null){
      response.status(500).json({status: 0, error: 'Resource not found...'});
    } else {
      response.status(200).json({status: 1, data: virtualTourData});
    }
  }).catch((error: Error) => {
    logger.error('Error in GetTourById', {message: error});
    response.status(500).json({status: 0, error: 'Error getting virtual tour by Id '+error});
  });
}

import { ProjectModule } from '../../../modules/projects';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';

export default async function ListProjectsFromOrganization (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id;
  const project = new ProjectModule(organization_id as string);
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }
  const projects = await project.ListProjectsFromOrganization();
  response.send({ status: 1, data: projects });
  return;
}

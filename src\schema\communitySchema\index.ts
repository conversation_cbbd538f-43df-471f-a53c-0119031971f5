import mongoose from 'mongoose';
import { CommunityType } from '../../types/community';
export const communitySchema = new mongoose.Schema({
  _id: String,
  project_id: String,
  name: String,
  total_towers: Number,
  total_units: Number,
  total_amenities: Number,
  thumbnail: String,
  category: {
    type: String,
    enum: CommunityType,
    default: CommunityType.TOWER,
  },
  updated_at: String,
});

import mongoose, { Pi<PERSON>ineStage } from 'mongoose';
import {
  Project,
} from '../../types/projects';
import { projectSchema } from '../../schema/projectSchema';
import { Models } from '../../types/extras';
import { buildingSchema } from '../../schema/buildingSchema';
import { unitSchema } from '../../schema/UnitSchema';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';
import { filterQuery, unitFilterQuery } from '../../types/units';
import { unitplanSchema } from '../../schema/unitplanSchema';
import { transformedFilterBuilding, FilterBuilding } from '../../types/building';
import { communitySchema } from '../../schema/communitySchema';
import logger from '../../config/logger';

export class PublicApis {
  private model: mongoose.Model<Project>;
  constructor (organization_id: string) {
    this.model = mongoose.model<Project>(
      `${organization_id}${Models._PROJECTS}`,
      projectSchema,
    );

  }

  public async GetProjectCount (project_id: string): Promise<object> {
    logger.info('GetProjectCount Called', {project_id: project_id});
    const aggregationPipeline: PipelineStage[] = [
      {
        $match: {
          _id: project_id,
        },
      },
      {
        $lookup: {
          from: project_id + Models._UNITS,
          localField: '_id',
          foreignField: 'project_id',
          as: 'units',
        },
      },
      {
        $lookup: {
          from: project_id + Models._AMENITIES,
          localField: '_id',
          foreignField: 'project_id',
          as: 'amenities',
        },
      },
      {
        $lookup: {
          from: project_id + Models._BUILDINGS,
          localField: '_id',
          foreignField: 'project_id',
          as: 'buildings',
        },
      },
      {
        $unwind: '$buildings',
      },
      {
        $unwind: '$buildings.floors',
      },
      {
        $group: {
          _id: '$_id', // Group by the project_id
          unitCount: { $first: '$units' },
          amenityCount: { $first: '$amenities' },
          floors: { $sum: { $size: { $objectToArray: '$buildings.floors' } } },
        },
      },
      {
        $project: {
          _id: 1,
          unitCount: {$size: '$unitCount'},
          amenityCount: {$size: '$amenityCount'},
          floors: '$floors',
        },
      },
    ];

    const result = await this.model.aggregate(aggregationPipeline);
    const obj = result[0];
    logger.info('GetProjectCount Successfull', {obj: obj});
    return obj;
  }
  public async GetCountForBuildings (project_id: string): Promise<object> {
    logger.info('GetCountForBuildings Called', {project_id: project_id});
    const aggregationPipeline: PipelineStage[] = [
      {
        $lookup: {
          from: project_id + Models._UNITS,
          let: { buildingId: '$_id' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$building_id', '$$buildingId'] },
                    { $eq: ['$status', 'available'] }, // Only include available units
                  ],
                },
              },
            },
          ],
          as: 'units',
        },
      },
      {
        $unwind: '$units',
      },
      {
        $lookup: {
          from: project_id + Models._UNITPLANS,
          localField: 'units.unitplan_id',
          foreignField: '_id',
          as: 'unitplans',
        },
      },
      {
        $unwind: '$unitplans',
      },
      {
        $group: {
          _id: '$_id',
          name: { $first: '$name' },
          floors: { $first: '$floors' },
          units: { $sum: 1 },
          measurementType: { $first: '$unitplans.measurement_type' },
          minArea: { $min: '$unitplans.measurement' },
          maxArea: { $max: '$unitplans.measurement' },
          uniqueUnitTypes: { $addToSet: '$unitplans.type' },
          category: { $first: '$type' },
          uniqueBedrooms: { $addToSet: '$unitplans.bedrooms' },
          minBedrooms: { $min: '$unitplans.bedrooms' },
          maxBedrooms: { $max: '$unitplans.bedrooms' },
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          totalUnits: '$units',
          minArea: 1,
          maxArea: 1,
          measurementType: 1,
          floors: { $size: { $objectToArray: '$floors' } },
          uniqueUnitTypes: 1,
          category: 1,
          uniqueBedrooms: 1,
          minBedrooms: 1,
          maxBedrooms: 1,
        },
      },
    ];

    const building_model = mongoose.model<Project>(
      project_id+Models._BUILDINGS,
      buildingSchema,
    );
    const result = await building_model.aggregate(aggregationPipeline);
    // Const result: Document[] = await building_model.find();
    const obj = arrayToObject(result);
    logger.info('GetCountForBuildings Successfull', {obj: obj});
    return obj;
  }
  public async GetBuildingByFloor (project_id: string, building_id: string): Promise<object> {
    logger.info('GetBuildingByFloor Called', {project_id: project_id, building_id: building_id});
    const aggregationPipeline: PipelineStage[] = [
      {
        $match: {
          building_id: building_id,
        },
      },
      {
        $lookup: {
          from: project_id + Models._BUILDINGS,
          localField: '_id',
          foreignField: 'building_id',
          as: 'buildings',
        },
      },
      {
        $lookup: {
          from: project_id + Models._UNITPLANS,
          localField: 'unitplan_id',
          foreignField: '_id',
          as: 'unitplans',
        },
      },
      {
        $unwind: '$unitplans',
      },
      {
        $match: {
          status: 'available', // Filter documents to include only those with "status: 'available'"
        },
      },
      {
        $group: {
          _id: '$floor_id',
          floors: { $first: '$floor_id' },
          status: { $addToSet: '$status' },
          measurementType: { $first: '$measurement_type' },
          units: { $sum: 1 },
          minArea: { $min: '$measurement' },
          maxArea: { $max: '$measurement' },
          minBedrooms: { $min: '$unitplans.bedrooms' },
          maxBedrooms: { $max: '$unitplans.bedrooms' },
          availabilityStatus: {$push: '$status'},
          uniqueBedrooms: { $addToSet: '$unitplans.bedrooms' },
        },
      },
      {
        $project: {
          _id: 1,
          floors: 1,
          measurementType: 1,
          status: {
            $cond: {
              if: { $in: ['available', '$status'] },
              then: 'available',
              else: 'reserved',
            },
          },
          units: 1,
          minArea: 1,
          maxArea: 1,
          minBedrooms: 1,
          maxBedrooms: 1,
          uniqueBedrooms: 1,
          totalAvailableUnits: {
            $reduce: {
              input: '$availabilityStatus',
              initialValue: 0,
              in: {
                $cond: {
                  if: { $eq: ['$$this', 'available'] },
                  then: { $add: ['$$value', 1] },
                  else: '$$value',
                },
              },
            },
          },
        },
      },
    ];

    const units_model = mongoose.model<Project>(
      project_id+Models._UNITS,
      unitSchema,
    );
    const result = await units_model.aggregate(aggregationPipeline);
    const obj = arrayToObject(result);
    logger.info('GetBuildingByFloor Successfull', {obj: obj});
    return obj;
  }
  public async GetListOfUnits (project_id: string): Promise<object> {
    logger.info('GetListOfUnits Called', {project_id: project_id});
    const aggregationPipeline: PipelineStage[] = [
      {
        $lookup: {
          from: project_id + Models._UNITPLANS,
          localField: 'unitplan_id',
          foreignField: '_id',
          as: 'unitplans',
        },
      },
      {
        $unwind: '$unitplans',
      },
      {
        $project: {
          _id: 1,
          unitplan_id: 1,
          name: 1,
          status: 1,
          price: 1,
          max_price: 1,
          measurement_type: 1,
          currency: 1,
          floor_id: 1,
          building_id: 1,
          tour_id: 1,
          measurement: 1,
          balcony_measurement: 1,
          balcony_measurement_type: 1,
          suite_area: 1,
          suite_area_type: 1,
          community_id: 1,
          bedroom: '$unitplans.bedrooms',
          type: '$unitplans.unit_type',
          cta_link: 1,
        },
      },
    ];

    const units_model = mongoose.model<Project>(
      project_id+Models._UNITS,
      unitSchema,
    );
    const result = await units_model.aggregate(aggregationPipeline);
    console.log(result, 'inside get count');
    const obj = arrayToObject(result);
    logger.info('GetListOfUnits Successfull', {obj: obj});
    return obj;
  }

  public async GetListOfCommunities (project_id: string): Promise<object> {
    logger.info('GetListOfCommunities Called', {project_id: project_id});

    const aggregationPipeline: PipelineStage[] = [
      // Stage 1: Lookup and unwind buildings
      {
        $lookup: {
          from: project_id + Models._BUILDINGS,
          localField: '_id',
          foreignField: 'community_id',
          as: 'building',
        },
      },
      {
        $lookup: {
          from: project_id + Models._UNITS,
          localField: '_id',
          foreignField: 'community_id',
          as: 'units',
        },
      },
      {
        $lookup: {
          from: project_id + Models._AMENITIES,
          localField: '_id',
          foreignField: 'community',
          as: 'amenities',
        },
      },
      // Stage 2: Group by community_id and unitplan_id to find unique unitplan_ids
      {
        $group: {
          _id: {
            community_id: '$_id',
            unitplan_id: '$units.unitplan_id',
          },
          thumbnail: { $first: '$thumbnail' },
          category: { $first: '$category' },
          name: { $first: '$name' },
          totalBuildings: { $first: '$building' },
          totalFloors: { $first: '$building.total_floors' },
          totalAmenities: { $first: '$amenities' },
          totalUnits: { $first: '$units' },
        },
      },
      // Stage 3: Lookup unitplan collection based on unitplan_id
      {
        $lookup: {
          from: project_id + Models._UNITPLANS,
          localField: '_id.unitplan_id',
          foreignField: '_id',
          as: 'unitplans',
        },
      },
      {
        $unwind: '$unitplans',
      },
      {
        $group: {
          _id: '$_id.community_id',
          name: { $first: '$name' },
          thumbnail: { $first: '$thumbnail' },
          category: { $first: '$category' },
          totalBuildings: { $first: '$totalBuildings' },
          totalFloors: { $first: '$totalFloors' },
          totalAmenities: { $first: '$totalAmenities' },
          totalUnits: { $first: '$totalUnits' },
          uniqueUnitTypes: { $addToSet: '$unitplans.type' },
        },
      },
      // Stage 4: Project to reshape the output
      {
        $project: {
          _id: 1,
          name: 1,
          thumbnail: 1,
          category: 1,
          totalBuildings: { $size: '$totalBuildings' },
          totalFloors: { $sum: '$totalFloors' },
          totalAmenities: { $size: '$totalAmenities' },
          totalUnits: { $size: '$totalUnits' },
          uniqueUnitTypes: 1,
        },
      },
    ];

    const model_name = project_id + Models._COMMUNITIES;
    let community_model;
    if (mongoose.models[model_name]) {
      community_model = mongoose.model(model_name);
    } else {
      community_model = mongoose.model<Project>(model_name, communitySchema);
    }

    const result = await community_model.aggregate(aggregationPipeline);
    const obj = arrayToObject(result);
    logger.info('GetListOfCommunities Successful', { obj: obj });
    return obj;
  }

  /**
   *
   * @param project_id
   * @param query
   * @returns
   *
   * This API handles two cases for
   *  i) sorting of units
   *  ii) Filtering of units
   *
   */

  public async getFilteredUnits (project_id: string, query: unitFilterQuery)
    : Promise<Array<UnknownObject> | null> {
    logger.info('getFilteredUnits Called', {project_id: project_id, query: query});
    const pipeline: PipelineStage[] = [];

    const sortby = query.sortby as string;
    const orderby = query.orderby;

    if (sortby && orderby) {
      const sortField = `$${sortby}`;

      // Stage to convert price field from string to double (number)
      if (sortby==='price'){
        const addFieldsStage = {
          $addFields: {
            price: {
              $cond: {
                if: { $ne: [sortField, ''] }, // Check if the sort field is not empty
                then: { $toDouble: sortField }, // Convert to double if valid
                else: '', // Set null for invalid or empty values
              },
            },
          },
        };
        pipeline.push(addFieldsStage);
      } else if (sortby==='floor_id'){
        const addFieldsStage = {
          $addFields: {
            floor_id: {
              $cond: {
                if: { $ne: [sortField, ''] }, // Check if the sort field is not empty
                then: { $toDouble: sortField }, // Convert to double if valid
                else: '', // Set null for invalid or empty values
              },
            },
          },
        };
        pipeline.push(addFieldsStage);
      }
    }

    pipeline.push({
      $lookup: {
        from: project_id + Models._UNITPLANS,
        localField: 'unitplan_id',
        foreignField: '_id',
        as: 'unitplans',
      },
    });

    pipeline.push({$sort: {
      [sortby]: orderby === 'asc' ? 1 : -1,
    }});

    if (sortby==='price'){
      pipeline.push({
        $addFields: {
          // Convert the 'price' field back to string
          price: { $toString: '$price' },
        },
      });
    } else if (sortby==='floor_id'){
      pipeline.push({
        $addFields: {
          // Convert the 'floor_id' field back to string
          floor_id: { $toString: '$floor_id' },
        },
      });
    }

    pipeline.push({
      $unwind: '$unitplans',
    });

    pipeline.push({
      $addFields: {
        price: {
          $cond: {
            if: { $ne: ['$price', ''] }, // Check if the sort field is not empty
            then: { $toDouble: '$price' }, // Convert to double if valid
            else: '', // Set null for invalid or empty values
          },
        },
      },
    });

    let floors:Array<string>=[]; // Floors value need to be in string
    let unitplanTypes:Array<string>=[];  // UnitplayTypes values need to be in string
    let bedrooms:Array<string> =[]; // Bedrooms values need to be in number
    let buildingId:Array<string>=[];
    let styleTypes:Array<string>=[];
    if (query.floor){
      // Convert string into Array of strings
      const strWithBrackets:string = query.floor as string;
      const strWithoutBrackets = strWithBrackets.replace(/\[|\]/g, '');
      floors = strWithoutBrackets.split(',');
    }
    if (query.unitplan_type){
      // Convert string into Array of strings
      const strWithBrackets:string = query.unitplan_type as string;
      const strWithoutBrackets = strWithBrackets.replace(/\[|\]/g, '');
      unitplanTypes = strWithoutBrackets.split(',');
    }
    if (query.bedrooms){
      // Convert string into Array of strings
      const strWithBrackets:string = query.bedrooms as string;
      const strWithoutBrackets = strWithBrackets.replace(/\[|\]/g, '');
      bedrooms = strWithoutBrackets.split(',');
    }
    if (query.building_id){
      // Convert string into Array of strings
      const strWithBrackets:string = query.building_id as string;
      const strWithoutBrackets = strWithBrackets.replace(/\[|\]/g, '');
      buildingId = strWithoutBrackets.split(',');
    }
    if (query.style_types){
      // Convert string into Array of strings
      const strWithBrackets:string = query.style_types as string;
      const strWithoutBrackets = strWithBrackets.replace(/\[|\]/g, '');
      styleTypes = strWithoutBrackets.split(',');
    }
    pipeline.push({
      $match: {
        ...(query.floor && { 'floor_id': {$in: floors} }),
        ...(query.bedrooms && { 'unitplans.bedrooms': { $in: bedrooms } }),
        ...(query.unitplan_type && { 'unitplans.type': { $in: unitplanTypes } }),
        ...(query.style_types && { 'unitplans.style': { $in: styleTypes } }),
        ...(query.min_floor && query.max_floor &&
          {'floor_id': {$gte: query.min_floor.toString(), $lte: query.max_floor.toString()}}),
        ...(query.min_price && query.max_price &&
          { price: { $gte: parseInt(query.min_price), $lte: parseInt(query.max_price),
          }}),
        ...(query.min_area && query.max_area &&
          { 'unitplans.measurement': {$gte: parseInt(query.min_area),  $lte: parseInt(query.max_area),

          }}),
        ...(query.building_id && { building_id: {$in: buildingId}}),

        ...(query.status && { status: query.status }),
        ...(query.is_available && { is_available: query.is_available }),
        ...(query.unit_id && { _id: query.unit_id }),
        ...(query.unitplan_id && { unitplan_id: query.unitplan_id }),
      },
    });

    const projectStage = {
      $project: {
        _id: 1,
        unitplan_id: 1,
        name: 1,
        status: 1,
        price: 1,
        floor: 1,
        measurementType: '$unitplans.measurement_type',
        currency: 1,
        floor_id: 1,
        building_id: 1,
        tour_id: 1,
        measurement: '$unitplans.measurement',
        bedroom: '$unitplans.bedrooms',
        type: '$unitplans.type',
        style: '$unitplans.style',
      },
    };

    pipeline.push(projectStage);

    const units_model = mongoose.model<Project>(
      project_id+Models._UNITS,
      unitSchema,
    );

    // Check if the pipeline is not empty before executing aggregate
    if (pipeline.length > 0) {
      const result = await units_model.aggregate(pipeline);
      if (result.length > 0) {
        // Check if there are actually units in the result
        logger.info('getFilteredUnits Successfull', {result: result});
        return result;
      }
      // If no units match the criteria, return an empty result

      return null;
    }
    return null;
  }

  public async getFilterDataPoints (project_id:string, filterQueryData:filterQuery):Promise<object|null>{
    logger.info('getFilterDataPoints Called', {project_id: project_id});
    const unitplan_model = mongoose.model<Project>(
      project_id+Models._UNITPLANS,
      unitplanSchema,
    );

    const unitplanAggregationPipeline = [
      {
        $group: {
          _id: null,
          minMeasurement: { $min: '$measurement' },
          maxMeasurement: { $max: '$measurement' },
          measurementType: {$first: '$measurement_type'},
          uniqueBedrooms: { $addToSet: '$bedrooms' }, // Collect unique bedrooms values
          uniqueUnitPlanTypes: { $addToSet: '$type'},
          uniqueStyleTypes: { $addToSet: '$style'},
          unitPlan: {
            $push: {
              type: '$type',
              id: '$_id',
              name: '$name',
            },
          },

        },
      },
      {
        $project: {
          _id: 0,
          minMeasurement: 1,
          maxMeasurement: 1,
          measurementType: 1,
          uniqueBedrooms: 1,
          uniqueUnitPlanTypes: 1,
          unitPlan: 1,
          uniqueStyleTypes: 1,
        },
      },
    ];

    const building_model = mongoose.model<Project>(
      project_id+Models._BUILDINGS,
      buildingSchema,
    );

    const buildingAggregationPipeline = [
      {
        $project: {
          _id: 1,
          name: 1,
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
        },
      },
    ];

    const units_model = mongoose.model<Project>(
      project_id+Models._UNITS,
      unitSchema,
    );

    const unitsAggregationPipeline = [];
    if (filterQueryData.community_id){
      unitsAggregationPipeline.push({
        $match: { community_id: filterQueryData.community_id },
      });
    }
    if (filterQueryData.building_id && filterQueryData.floor_id) {
      unitsAggregationPipeline.push({
        $match: {
          building_id: filterQueryData.building_id,
          floor_id: filterQueryData.floor_id,
        },
      });
    }

    unitsAggregationPipeline.push({
      $addFields: {
        price: {
          $cond: {
            if: { $ne: ['$price', ''] }, // Check if the sort field is not empty
            then: { $toDouble: '$price' }, // Convert to double if valid
            else: '', // Set null for invalid or empty values
          },
        },
        // Floor_id: { $toInt: '$floor_id' },
        floor_id: '$floor_id',
      },
    },
    {
      $lookup: {
        from: project_id + Models._UNITPLANS,
        localField: 'unitplan_id',
        foreignField: '_id',
        as: 'unitplanDetails',
      },
    },
    {
      $unwind: {
        path: '$unitplanDetails',
        preserveNullAndEmptyArrays: false, // Only include units that have a matching unitplan
      },
    },
    {
      $group: {
        _id: null,
        minPrice: { $min: '$price'  },
        maxPrice: { $max: '$price' },
        currencyType: {$first: '$currency'},
        floor_id: {$addToSet: '$floor_id'},
        uniqueUnitPlanTypes: { $addToSet: '$unitplanDetails.type' },
        uniqueBedroomTypes: { $addToSet: '$unitplanDetails.bedrooms' },
        minMeasurement: { $min: '$unitplanDetails.measurement' },
        maxMeasurement: { $max: '$unitplanDetails.measurement' },
      },
    },
    {
      $project: {
        _id: 0,
        minPrice: 1,
        maxPrice: 1,
        minFloor: { $min: '$floor_id'  },
        maxFloor: { $max: '$floor_id' },
        currencyType: 1,
        uniqueBedroomTypes: 1,
        uniqueUnitPlanTypes: 1,
        minMeasurement: 1,
        maxMeasurement: 1,
      },
    });

    const unitplanResult = await unitplan_model.aggregate(unitplanAggregationPipeline);
    const buildingResult = await building_model.aggregate(buildingAggregationPipeline);
    const unitsResult = await units_model.aggregate(unitsAggregationPipeline);

    const formattedBuildingResponse:transformedFilterBuilding = {};
    buildingResult.forEach((item) => {
      const obj:FilterBuilding = {
        id: item._id,
        name: item.name,
        floors: item.floors,
      };
      formattedBuildingResponse[item.name] = obj;
    });

    const finalResult = {
      unitplanData: unitplanResult[0],
      buildingData: formattedBuildingResponse,
      unitData: unitsResult[0],
    };
    logger.info('getFilterDataPoints Successfull', {finalResult: finalResult});
    return finalResult;
  }

  public async processProjects (ProjectId: string):Promise<object>{
    console.log('id', ProjectId);
    const unitsModel = mongoose.model<Project>(
      `${ProjectId}${Models._UNITS}`,
      unitSchema,
    );
    const pipeline:PipelineStage[] = [

      {
        $addFields: {
          price: {
            $cond: {
              if: { $ne: ['$price', ''] },
              then: { $toDouble: '$price' },
              else: '',
            },
          },
        },
      },
      {
        $lookup: {
          from: `${ProjectId}${Models._UNITPLANS}`,
          localField: 'unitplan_id',
          foreignField: '_id',
          as: 'unitplanDetails',
        },
      },
      {
        $unwind: {
          path: '$unitplanDetails',
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $facet: {
          unitDetails: [
            {
              $group: {
                _id: null,
                minPrice: { $min: '$price' },
                currencyType: { $first: '$currency' },
                uniqueBedroomTypes: { $addToSet: '$unitplanDetails.bedrooms' },
              },
            },
          ],
          unitPlanTypeCounts: [
            {
              $group: {
                _id: '$unitplanDetails.unit_type',
                count: { $sum: 1 },
              },
            },
            {
              $match: {
                _id: { $ne: null },
              },
            },
            {
              $sort: { count: -1 },
            },
            {
              $limit: 1,
            },
            {
              $project: {
                type: '$_id',
                _id: 0,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$unitDetails',
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $addFields: {
          type: { $arrayElemAt: ['$unitPlanTypeCounts.type', 0] },
        },
      },
      {
        $project: {
          unitDetails: 1,
          type: 1,
        },
      },
    ];

    const aggregationResult = await unitsModel.aggregate(pipeline);

    logger.info(`Aggregation successful for project: ${ProjectId}`, { result: aggregationResult[0] });

    console.log(typeof aggregationResult[0]);

    return aggregationResult[0] ;

  }

}

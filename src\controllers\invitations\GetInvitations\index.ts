import {  Response } from 'express';
import {  ExtendedRequest } from '../../../types/extras';
import { InvitesModule } from '../../../modules/invites';

export async function GetInvitations (request: ExtendedRequest, response: Response):Promise<void> {
  const invitation = new InvitesModule;
  const IsAuthenticated = request.IsAuthenticated;
  const organization_id = request.organization_id;
  if (!organization_id){
    response.send({status: 0, error: 'no organization is found'});
    return ;
  }
  if (!IsAuthenticated){
    response.send({status: 0, error: 'Not authorized'});
    return ;
  }
  const invitations = await invitation.GetInvitations(organization_id);
  if (invitations) {
    response.send({ status: 1, data: invitations });
  } else {
    response.send({ status: 1, data: [] });
  }
  return ;
}

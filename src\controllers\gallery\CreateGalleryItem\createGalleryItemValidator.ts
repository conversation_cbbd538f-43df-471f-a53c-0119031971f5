import { Request, Response, NextFunction } from 'express';
import { check, validationResult } from 'express-validator';
import { MediaType } from '../../../types/amenity';
interface UploadedFiles {
  thumbnail: Express.Multer.File[],
  file: Express.Multer.File[]
}

const CreateGalleryItemValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;
  const requiredTextFields = [
    'project_id',
    'type',
    'category',
    'name',
  ];
  const missingTextFields = requiredTextFields.filter(
    (field) => !(field in req.body),
  );
  if (missingTextFields.length > 0) {
    res.status(400).json({
      error: `Missing text fields: ${missingTextFields.join(', ')}`,
    });
    return;
  }
  check('type', 'Invalid media type value. Please ensure that you are using a valid type value')
    .isIn(Object.values(MediaType)).run(req);
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    console.log(errors);
    res.status(400).json({ error: errors.array() });
    return;
  }
  if (req.body.type === MediaType.LINK) {
    console.log(req.body.link);
    if (!req.body.link) {
      res.status(400).json({ error: 'Link is required' });
      return;
    }
    next();
    return;
  }
  if (req.body.type === MediaType.VIRTUAL_TOUR) {
    console.log(req.body.tour_id);
    if (!req.body.tour_id) {
      res.status(400).json({ error: 'tour_id is required' });
      return;
    }
    next();
    return;
  }
  if (files) {
    if (!files.thumbnail) {
      res
        .status(400)
        .json({ error: 'Image Thumbnail field is required.' });
      return ;
    }
    if (!files.file) {
      res
        .status(400)
        .json({ error: 'Image field is required.' });
      return ;
    }
    next();
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }

  // If (req.body.type === MediaType.LINK) {
  //   Const requiredTextFields = [
  //     'project_id',
  //     'type',
  //     'category',
  //     'name',
  //     'link',
  //   ];
  //   Const missingTextFields = requiredTextFields.filter(
  //     (field) => !(field in req.body),
  //   );
  //   If (missingTextFields.length > 0) {
  //     Res.status(400).json({
  //       Error: `Missing text fields: ${missingTextFields.join(', ')}`,
  //     });
  //   } else {
  //     Check('type', 'Invalid media type value. Please ensure that you are using a valid type value')
  //       .isIn(Object.values(MediaType)).run(req);
  //     Const errors = validationResult(req);

  //     If (!errors.isEmpty()) {
  //       Console.log(errors);
  //       Res.status(400).json({ error: errors.array() });
  //     } else {
  //       Next();
  //     }
  //   }
  // } else {
  //   If (files) {
  //     If (!files.thumbnail) {
  //       Res
  //         .status(400)
  //         .json({ error: 'Image Thumbnail field is required.' });
  //     }
  //     If (!files.file) {
  //       Res
  //         .status(400)
  //         .json({ error: 'Image field is required.' });
  //     } else {
  //       Const requiredTextFields = [
  //         'project_id',
  //         'type',
  //         'category',
  //         'name',
  //       ];

  //       Const missingTextFields = requiredTextFields.filter(
  //         (field) => !(field in req.body),
  //       );

  //       If (missingTextFields.length > 0) {
  //         Res.status(400).json({
  //           Error: `Missing text fields: ${missingTextFields.join(', ')}`,
  //         });
  //       } else {
  //         Check('type', 'Invalid media type value. Please ensure that you are using a valid type value')
  //           .isIn(Object.values(MediaType)).run(req);
  //         Const errors = validationResult(req);
  //         If (!errors.isEmpty()) {
  //           Console.log(errors);
  //         } else {
  //           Next();
  //         }
  //       }
  //     }
  //   } else {
  //     Res.status(400).json({ error: 'Invalid file structure in the request.' });
  //   }
  // }
};

export default CreateGalleryItemValidate;

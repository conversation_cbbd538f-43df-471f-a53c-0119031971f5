import { Request, Response } from 'express';
import { SessionModule } from '../../../modules/sessions';
import { TaskModule } from '../../../modules/tasks';

export async function CancelSession (request: Request, response: Response): Promise<void> {
  const organization_id = request.headers.organization as string;
  const session = new SessionModule();
  const taskModule = new TaskModule();
  const session_id = request.body.session_id;

  if (!organization_id) {
    response.status(400).send({ status: 0, error: 'No organization found' });
    return;
  }

  try {
    const sessiondata = await session.CancelSession(session_id);

    if (!sessiondata) {
      response.status(404).send({ status: 0, error: 'Session not found' });
      return;
    }

    // Task IDs to cancel
    const cancelTaskIds = [
      `${session_id}_reminder`,
      `${session_id}_invite`,
      ...sessiondata.participants.map((lead) => `${lead._id}_invite`),
    ];

    // Cancel reminder tasks
    await Promise.all(cancelTaskIds.map((id) => taskModule.cancelTask(id)));

    // Schedule a task to notify cancellation
    const notifyCancelTimeURL = `${process.env.BASE_URL}mailer/notifyCancellation`;
    const payload = { session_id: sessiondata._id };
    const notifyCancelTime = new Date();
    notifyCancelTime.setMinutes(notifyCancelTime.getMinutes() + 0.5); // Schedule task after 30 seconds
    const notifyCancelTimeISO = notifyCancelTime.toISOString();
    const notifyCancelTaskId = `${sessiondata._id}_cancel`;
    const mailQueue = 'mails';
    const mailheaders = {};
    await taskModule.createTask(mailQueue, notifyCancelTimeURL, payload,
      notifyCancelTimeISO, notifyCancelTaskId, mailheaders);

    response.status(200).json({ status: 1, data: sessiondata });
  } catch (error) {
    response.status(500).json({ status: 0, error: `Error cancelling session: ${error}` });
  }
}

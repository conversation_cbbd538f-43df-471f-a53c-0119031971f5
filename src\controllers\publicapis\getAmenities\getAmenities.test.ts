import app  from '../../../app';
import request from 'supertest';
describe('Get Amenities Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/getAmenities?organization_id=8X3plU&project_id=65701718b5063acb09dbf6e4&category=drone')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual({});
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getAmenities'
      +'?organization_id=8X3plU&project_id=65701718b5063acb09dbf6')   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).toEqual({});
  });
});

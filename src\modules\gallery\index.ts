import mongoose from 'mongoose';
import { gallerySchema } from '../../schema/gallerySchema';
import { bulkUpdateType, createGalleryItem, galleryItem, updateGalleryItemType } from '../../types/gallery';
import { UnknownObject, arrayToObject } from '../../helpers/dataFormatHelper';
import { MediaType } from '../../types/amenity';
import logger from '../../config/logger';
import { trashModule } from '../trash/index';
import { trashType } from '../../types/trash';
import { Models } from '../../types/extras';
export class GalleryModule {
  private model: mongoose.Model<galleryItem>;
  public storagepath;
  constructor (project_id: string, organization_id: string) {
    this.model = mongoose.model<galleryItem>(
      `${project_id}${Models._GALLERY}`,
      gallerySchema,
    );
    this.storagepath='CreationtoolAssets/'+organization_id+'/projects/'+project_id+'/gallery/';
  }
  public async CreateGalleryItem (createGalleryItemData: createGalleryItem):Promise<galleryItem | void> {
    logger.info('CreateGalleryItem Called', {createGalleryItemData: createGalleryItemData});
    try {
      const newGalleryItem: galleryItem = {
        _id: createGalleryItemData.id,
        name: createGalleryItemData.name,
        type: createGalleryItemData.type,
        category: createGalleryItemData.category,
        thumbnail: createGalleryItemData.thumbnail,
        url: createGalleryItemData.url,
        link: createGalleryItemData.link,
        tour_id: createGalleryItemData.tour_id,
        modified: new Date().toISOString(),
      };
      const gallery = new this.model(newGalleryItem);
      const newGalleryItemData = await gallery.save();
      logger.info('CreateGalleryItem Successfull', {newGalleryItemData: newGalleryItemData});
      return newGalleryItemData;
    } catch (error) {
      logger.error('Unable to create this gallery item', {message: error});
      throw new Error('Unable to create this gallery item'+error);
    }
  }
  public async GetGallery (category:string, search?:string): Promise<object | null> {
    logger.info('GetGallery Called', {category: category});
    try {
      const matchStage: Record<string, any> = {};
      if (category) {
        matchStage.category = category;
      }
      if (search) {
        matchStage.$or = [
          { name: { $regex: search, $options: 'i' } },
          { category: { $regex: search, $options: 'i' } },
        ];
      }

      const pipeline = [
        ...(Object.keys(matchStage).length ? [{ $match: matchStage }] : []),
        // Add a temporary field to determine if 'order' is missing
        {
          $addFields: {
            hasOrder: {
              $cond: {
                if: { $eq: [{ $type: '$order' }, 'missing'] },
                then: 0,
                else: 1,
              },
            },
          },
        },
        // Group items by category
        {
          $group: {
            _id: '$category',
            items: {
              $push: {
                item: '$$ROOT',
                hasOrder: '$hasOrder',
              },
            },
          },
        },
        // Sort items within each category by 'hasOrder' and 'order'
        {
          $project: {
            items: {
              $sortArray: {
                input: '$items',
                sortBy: {
                  'hasOrder': -1,
                  'item.order': 1,
                },
              },
            },
          },
        },
        // Unwind sorted items
        {
          $unwind: '$items',
        },
        // Replace root and unset 'hasOrder'
        {
          $replaceRoot: {
            newRoot: {
              $mergeObjects: ['$items.item', { hasOrder: '$items.hasOrder' }],
            },
          },
        },
        {
          $unset: 'hasOrder',
        },
      ];
      const galleryItems:Array<UnknownObject> = await this.model.aggregate(pipeline);
      const galleryItemsObj = arrayToObject(galleryItems) as Record<string, galleryItem>;
      logger.info('GetGallery Successfull', {galleryItemsObj: galleryItemsObj});
      return galleryItemsObj;
    } catch (error) {
      logger.error('Error in getGallery', {message: error});
      return null;
    }
  }
  public async DeleteGalleryItem (item_id: string): Promise<galleryItem> {
    logger.info('DeleteGalleryItem Called', {item_id: item_id});
    try {
      const deletedItem: unknown = await this.model.findOneAndDelete({_id: item_id});
      logger.info('DeleteGalleryItem Successfull', {deletedItem: deletedItem});
      return deletedItem as galleryItem;
    } catch (error) {
      logger.error('Error in DeleteGalleryItem', {message: error});
      throw error; // Rethrow the error to reject the promise
    }
  }
  public async UpdateGalleryItem (item_id: string, updateGalleryItem: updateGalleryItemType)
  : Promise<galleryItem | void> {
    logger.info('UpdateGalleryItem Called', {item_id: item_id, updateGalleryItem: updateGalleryItem});
    try {
      const existingGalleryItem = await this.model.findOne({ _id: item_id });
      if (!existingGalleryItem) {
        logger.error('Unable to find existing gallery item');
        throw new Error('Unable to find existing gallery item');
      }

      // Check if type is changing
      if (updateGalleryItem.type) {
        if (updateGalleryItem.type === MediaType.LINK) {
          // Update for link type
          if (!updateGalleryItem.link) {
            logger.error('Link is required for type "embed_link"');
            throw new Error('Link is required for type "embed_link"');
          }
          updateGalleryItem.thumbnail = null;
          updateGalleryItem.url = null;
          updateGalleryItem.tour_id = null;
        } else if (updateGalleryItem.type === MediaType.VIRTUAL_TOUR){

          if (!updateGalleryItem.tour_id) {
            logger.error('tour_id is required for type "virtual tour"');
            throw new Error('tour_id is required for type "virtual tour"');
          }
          updateGalleryItem.thumbnail = null;
          updateGalleryItem.url = null;
          updateGalleryItem.link = null;
        } else {
          // Update for other types
          if (!updateGalleryItem.thumbnail || !updateGalleryItem.url) {
            logger.error('Thumbnail and URL are required for other types');
            throw new Error('Thumbnail and URL are required for other types');
          }
          updateGalleryItem.link = null;
          updateGalleryItem.tour_id = null;
        }
      }
      updateGalleryItem.modified = new Date().toISOString();
      // Update the gallery item
      const updatedGalleryItemData = await this.model.findOneAndUpdate(
        { _id: item_id },
        { $set: { ...updateGalleryItem } },
        { new: true },
      );

      if (!updatedGalleryItemData) {
        logger.error('Error in updating gallery item');
        throw new Error('Error in updating gallery item');
      }
      logger.info('UpdateGalleryItem Successfull', {updatedGalleryItemData: updatedGalleryItemData});
      return updatedGalleryItemData;

    } catch (error) {
      logger.error('Internal Server Error: in UpdateGalleryItem', {message: error});
      throw new Error('Internal Server Error: ' + error);
    }
  }

  public async getCategories (): Promise<string[] | null>{
    logger.info('getCategories Called');
    try {
      const galleryItems = await this.model.distinct('category');
      logger.info('getCategories Successfull', {galleryItems: galleryItems});
      return galleryItems;
    } catch (error) {
      logger.error('Internal Server Error: in getCategories', {message: error});
      return null;
    }
  }
  public async updateBulkGalleryItems (payload: bulkUpdateType): Promise<string> {
    logger.info('updateBulkGalleryItems Called', {payload: payload});
    return new Promise<string>((resolve, reject) => {
      const promises: Promise<galleryItem | null>[] = payload.query.map(async (item) => {
        return this.model.findOneAndUpdate(
          { _id: item.id },
          {
            $set: {
              order: item.order,
              category: item.category,
            },
          },
          { new: true }, // Return the updated document
        )
          .then((res) => {

            return res;
          })
          .catch((error) => {
            logger.error('Error in findOneAndUpdate', {message: error});
            return null;
          });
      });

      Promise.all(promises)
        .then((results) => {
          const allUpdated = results.every((result) => result !== null);
          if (allUpdated) {
            logger.info('Documents updated successfully', {results: results});
            resolve('Documents updated successfully');
          } else {
            logger.error('Error while updating sidebar option');
            reject('Error while updating sidebar option');
          }
        })
        .catch((error) => {
          logger.error('Internal Server Error: in updateBulkGalleryItems', {message: error});
          reject(error);
        });
    });
  }
  public async moveToTrash (
    galleryIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
  ): Promise<galleryItem | void> {
    logger.info('moveToTrash Successfull',
      {galleryIds: galleryIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: galleryIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('Gallerys corresponding to Gallery IDs provided not found');
      throw 'Gallerys corresponding to Gallery IDs provided not found';
    }
    const GALLERYdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._GALLERY}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(GALLERYdataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: galleryIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moceToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
  public async restoreGallery (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreGALLERY Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);
    if (restoredData) {
      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        item.modified = new Date().toISOString();
        const gallery = new this.model(item);
        await gallery.save();
      });

      await Promise.all([
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreGALLERY is Successfull');
        return 'GALLERY got restored';
      });
    } else {
      logger.error('Error in restoreGALLERY');
      throw new Error('Failed to restore gallery data from trash');
    }
  }
}

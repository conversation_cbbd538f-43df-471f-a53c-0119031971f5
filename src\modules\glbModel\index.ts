import { glbSchema } from '../../schema/glbmodelSchema';
import mongoose from 'mongoose';
import {
  createModel,
  createTexture,
  glbmodel,
  updateModel,
  updateTexture,
  updateLumaModel,
  updateDownloadUrl,
} from '../../types/glbModels';
import { arrayToObject } from '../../helpers/dataFormatHelper';
import logger from '../../config/logger';
import { Models } from '../../types/extras';

export class ModelModule {
  private model: mongoose.Model<glbmodel>;
  public storagepath;
  constructor (project_id: string, organization_id: string) {
    this.model = mongoose.model<glbmodel>(`${project_id}${Models._MODELS}`, glbSchema);
    this.storagepath =
      'CreationtoolAssets/' +
      organization_id +
      '/projects/' +
      project_id +
      '/models/';
  }
  public async CreateModel (payload: createModel): Promise<glbmodel | void> {
    logger.info('CreateModel Called', {payload: payload});

    try {
      const modelMod = new this.model(payload);
      const model = await modelMod.save();
      logger.info('CreateModel successfull', {model: model});
      return model;
    } catch (error) {
      logger.error('Unable to create model for this project', {message: error});
      throw new Error('Unable to create model for this project' + error);
    }
  }
  public async createTexture (payload: createTexture): Promise<glbmodel | void> {
    logger.info('createTexture Called', {payload: payload});
    return new Promise((resolve, reject) => {
      this.model
        .findOneAndUpdate(
          {
            _id: payload.model_id,
          },
          {
            $set: {
              [`meshes.${payload.mesh_id}.textures.${payload.id}`]: {
                id: payload.id,
                name: payload.name,
                actual_texture_thumbnail: payload.actual_texture_thumbnail,
                texture_thumbnail: payload.texture_thumbnail,
              },
            },
          },
          {
            new: true,
          },
        )
        .then((updatedDocument) => {

          if (updatedDocument) {
            logger.info('createTexture successfull', {updatedDocument: updatedDocument});
            resolve(updatedDocument);
          } else {
            reject(null);
          }
        })
        .catch((err) => {
          logger.error('Error in CreateTexture', {message: err});
          reject(err);
        });
    });
  }
  public async getModels (): Promise<object | void> {
    logger.info('getModels Called');
    const modelList: Array<glbmodel> = await this.model.find();
    const modelObject = arrayToObject(modelList) as Record<string, glbmodel>;
    logger.info('getModels successfull', {modelObject: modelObject});
    return modelObject;
  }
  public async updateModel (payload: updateModel): Promise<glbmodel | void> {
    logger.info('updateModel Called', {payload: payload});
    return new Promise((resolve, reject) => {
      this.model
        .findOneAndUpdate(
          {
            _id: payload.model_id,
          },
          {
            $set: {
              name: payload.name,
              thumbnail: payload.thumbnail,
              url: payload.url,
              description: payload.description,
            },
          },
          {
            new: true,
          },
        )
        .then((updatedDocument) => {

          if (updatedDocument) {
            logger.info('updateModel successfull', {updatedDocument: updatedDocument});
            resolve(updatedDocument);
          } else {
            reject(null);
          }
        })
        .catch((err) => {
          logger.error('Error in updateModel', {message: err});
          reject(err);
        });
    });
  }
  public async updateTexture (payload: updateTexture): Promise<glbmodel | void> {
    logger.info('updateTexture Called', {payload: payload});
    return new Promise((resolve, reject) => {
      const updateObject = {
        $set: {
          [`meshes.${payload.mesh_id}.textures.${payload.texture_id}.name`]:
            payload.name,
        },
      };

      if (payload.actual_texture_thumbnail !== undefined) {
        updateObject.$set[
          `meshes.${payload.mesh_id}.textures.${payload.texture_id}.actual_texture_thumbnail`
        ] = payload.actual_texture_thumbnail;
      }

      if (payload.texture_thumbnail !== undefined) {
        updateObject.$set[
          `meshes.${payload.mesh_id}.textures.${payload.texture_id}.texture_thumbnail`
        ] = payload.texture_thumbnail;
      }
      this.model
        .findOneAndUpdate(
          {
            _id: payload.model_id,
          },
          updateObject,
          {
            new: true,
          },
        )
        .then((updatedDocument) => {

          if (updatedDocument) {
            logger.info('updateTexture successfull', {updatedDocument: updatedDocument});
            resolve(updatedDocument);
          } else {
            logger.error('No Documents Found');
            reject(null);
          }
        })
        .catch((err) => {
          logger.error('Error in updateTexture', {message: err});
          reject(err);
        });
    });
  }
  public async createLumaModel (payload: createModel): Promise<glbmodel> {
    logger.info('createLumaModel Called', {payload: payload});
    try {
      const modelMod = new this.model(payload);
      const model = await modelMod.save();
      logger.info('createLumaModel successfull', {model: model});
      console.log(model);
      return model;
    } catch (error) {
      logger.error('Unable to create luma model for this project', {message: error});
      throw new Error('Unable to create luma model for this project' + error);
    }
  }
  public async updateProgress (
    payload: updateLumaModel,
  ): Promise<object | void> {
    logger.info('updateProgress Called', {payload: payload});
    return new Promise((resolve, reject) => {
      this.model
        .findOneAndUpdate(
          {
            _id: payload.model_id,
          },
          {
            $set: {
              progress: payload.progress,
              url: payload.url,
              status: payload.status,
              thumbnail: payload.thumbnail,
            },
          },
          {
            new: true,
          },
        )
        .then((updatedDocument) => {

          if (updatedDocument) {
            logger.info('updateProgress successfull', {updatedDocument: updatedDocument});
            resolve(updatedDocument);
          } else {
            reject(null);
          }
        })
        .catch((err) => {
          logger.error('Error in UpdateProgress', {message: err});
          reject(err);
        });
    });
  }
  public async getLumaModel (id: string): Promise<object | null> {
    logger.info('getLumaModel Called', {id: id});
    try {
      const lumaModel = await this.model.findOne({ _id: id });
      logger.info('getLumaModel successfull', {lumaModel: lumaModel});
      return lumaModel;
    } catch (error) {
      logger.error('Error in getLumaModel', {message: error});
      throw new Error('Unable to get luma model ' + error);
    }
  }
  //  Model_id: string,
  //     Download_url: string
  public async updateDownloadUrl (
    payload: updateDownloadUrl,
  ): Promise<glbmodel | void> {
    logger.info('updateDownloadUrl Called', {payload: payload});
    return new Promise((resolve, reject) => {

      this.model
        .findOneAndUpdate(
          { _id: payload.model_id },
          { $set: {
            download_url: payload.download_url,
            status: payload.status,
          } },
          { new: true },
        )
        .then((updatedDocument) => {
          if (updatedDocument) {
            logger.info('updateDownloadUrl successfull', {updatedDocument: updatedDocument});
            resolve(updatedDocument);
          } else {
            reject(null);
          }
        })
        .catch((err) => {
          logger.error('Error in updateDownloadUrl', {message: err});
          reject(err);

        });
    });
  }
  public async getLumaModelsWithTypeLuma (): Promise<Record<string, glbmodel>> {
    logger.info('getLumaModelsWithTypeLuma Called');
    try {
      const lumaModels = await this.model.find({ type: 'luma' });
      const lumaModelMap = lumaModels.reduce((acc, model) => {
        acc[model._id.toString()] = model;
        return acc;
      }, {} as Record<string, glbmodel>);
      logger.info('getLumaModelsWithTypeLuma successfull', {lumaModelMap: lumaModelMap});
      return lumaModelMap;
    } catch (error) {
      logger.error('Unable to get luma models', {message: error});
      throw new Error('Unable to get luma models ' + error);
    }
  }
}

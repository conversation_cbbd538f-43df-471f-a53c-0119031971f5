import { Request, Response, NextFunction } from 'express';
import { body, header, validationResult } from 'express-validator';
import { tourCategory, tourType } from '../../../types/virtualTour';

interface UploadedFiles {
  model: Express.Multer.File[];
  camera: Express.Multer.File[];
}

const CreateVirtualTourValidate = [
  header('organization', 'Organization is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),

  body('project_id', 'Project ID is required')
    .notEmpty()
    .isString()
    .trim(),

  body('name', 'Name is required and should be a string')
    .notEmpty()
    .isString()
    .trim(),

  body('description', 'Description is required and should be a string')
    .notEmpty()
    .isString()
    .trim(),

  body('category', 'Category is required')
    .notEmpty()
    .isIn(Object.values(tourCategory)),

  body('type', 'Type is required')
    .notEmpty()
    .isIn(Object.values(tourType)),

  body('unitplan_id', 'Unit Plan ID is optional')
    .optional()
    .isString()
    .trim(),

  body('link')
    .if(body('type').equals(tourType.EXTERNAL))
    .notEmpty()
    .withMessage('Link is required for external tour type')
    .isString()
    .withMessage('Link must be a string')
    .trim(),

  body('space_id')
    .if(body('type').equals(tourType.MATTERPORT))
    .notEmpty()
    .withMessage('Space ID is required for matterport tour type')
    .isString()
    .withMessage('Space ID must be a string')
    .trim(),

  body('images')
    .not()
    .exists()
    .withMessage('Images cannot be provided during tour creation'),

  body('groups')
    .not()
    .exists()
    .withMessage('Groups cannot be provided during tour creation'),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    const files = req.files as UploadedFiles | undefined;
    if (files) {
      if (req.body.type===tourType.MLE && (!files.model || !files.camera)){
        res
          .status(400)
          .json({ error: 'Both Model glb and Camera gltf files are required.' });
      } else if (!errors.isEmpty()) {
        res.status(400).json({ errors: errors.array() });
        return;
      }
      next();
    }
  },
];

export default CreateVirtualTourValidate;

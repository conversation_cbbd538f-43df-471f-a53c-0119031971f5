import { Request, Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';

const createInvitationValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('email', 'Email for invite is required').notEmpty(),
  body('role', 'Role for invite is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createInvitationValidate;

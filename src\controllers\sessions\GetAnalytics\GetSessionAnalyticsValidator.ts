import { Request, Response, NextFunction } from 'express';
import { validationResult, query, header } from 'express-validator';
import { SessionSource, SessionStatus, SessionType } from '../../../types/session';

const GetSessionAnalyticsValidate = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  query('user_id', 'User Id is required').optional(),
  query('type', 'Session Type is Invalid').optional().isIn(Object.values(SessionType)),
  query('source', 'Session Source is Invalid').optional().isIn(Object.values(SessionSource)),
  query('status', 'Session status is Invalid').optional().isIn(Object.values(SessionStatus)),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;

    }
    next();
  },
];

export default GetSessionAnalyticsValidate;

import { UnknownObject, arrayToObject } from '../../helpers/dataFormatHelper';
import { unitplanSchema } from '../../schema/unitplanSchema';
import { unitplanType, UnitplanInterface } from '../../types/unitplan';
import mongoose, { Types } from 'mongoose';
import { unitSchema } from '../../schema/UnitSchema';
import {
  Project,
} from '../../types/projects';
import { Models } from '../../types/extras';
import logger from '../../config/logger';
import { trashType } from '../../types/trash';
import { trashModule } from '../trash/index';
// Import fs from 'fs';
// Import { admin, bucketName } from '../../config/firebase';

interface measurement{
  maxMeasurement:string,
  minMeasurement:string
}
interface measurementMap{
  [key:string]: measurement
}

type ObjectWithIdKey = Record<string, unitplanType>;

export class unitplanModule {
  private urlObject: { [key: string]: string } = {};
  private model: mongoose.Model<unitplanType>;
  public storagepath;
  constructor (project_id: string, organization_id: string) {
    this.model = mongoose.model<unitplanType>(
      `${project_id}${Models._UNITPLANS}`,
      unitplanSchema,
    );
    this.storagepath =
      'CreationtoolAssets/' +
      organization_id +
      '/projects/' +
      project_id +
      '/unitplans/';
  }
  public async createUnitplan (payload: object): Promise<object> {
    logger.info('createUnitplan Called', {payload: payload});
    return new Promise<object>((resolve, reject) => {
      const masScene = new this.model(payload);
      masScene
        .save()
        .then(() => {
          logger.info('createUnitplan Successful', {payload: payload});
          resolve(payload);
        })
        .catch((err) => {
          logger.error('Error in createUnitplan', {message: err});
          reject(err);
        });
    });
  }

  public async getListOfUnitplan (): Promise<object | null> {
    logger.info('getListOfUnitplan Called');
    const unitplans: Array<UnknownObject> = await this.model.find().lean();

    const unitplansobj = arrayToObject(
      unitplans.map(({ is_residential: _is_residential, ...rest }) => rest), // Remove is_residential field
    ) as Record<string, unitplanType>;
    const getUpdatedBedroomsValue = (bedrooms: string): string => {
      if (bedrooms === 'studio' || bedrooms === 'penthouse' || bedrooms === 'townhouse' || bedrooms === 'duplex' || bedrooms === 'suite') {
        return bedrooms;
      }
      if (bedrooms === '0BHK') {
        return 'studio';
      }
      return bedrooms.includes('BHK') ? bedrooms : `${bedrooms}BHK`;
    };
    for (const key in unitplansobj) {
      if (key in unitplansobj) {
        const unitplan = unitplansobj[key];
        if (
          (unitplan.bedrooms && !unitplan.bedrooms.toString().includes('BHK') &&
          unitplan.bedrooms !== 'studio' &&
          unitplan.bedrooms !== 'penthouse' &&
            unitplan.bedrooms !== 'townhouse' &&
            unitplan.bedrooms !== 'duplex' &&
            unitplan.bedrooms !== 'suite'
          ) ||
          unitplan.bedrooms === '0BHK'
        ) {
          const newBedroomValue = getUpdatedBedroomsValue(unitplan.bedrooms as string) as unitplanType['bedrooms'];
          unitplan.bedrooms = newBedroomValue;
          await this.model.findOneAndUpdate(
            { _id: unitplan._id },
            {
              $set: {
                bedrooms: newBedroomValue,
              },
            },
            { new: true },
          );
        }
      }
    }

    logger.info('getListOfUnitplan Successful', {unitplansobj: unitplansobj});
    return unitplansobj;
  }

  public async getUnitPlanDataWithMeasurementRange (projectId:string):Promise<object> {
    logger.info('getUnitPlanDataWithMeasurementRange Called', {projectId: projectId});
    try {
      // Find all unitplans
      const unitPlans = await this.model.find().lean();

      // Find unitplans with measurement = 0
      const project_id=projectId as string;
      const units_model = mongoose.model<Project>(
        project_id+Models._UNITS,
        unitSchema,
      );

      const zeroMeasurementUnitPlans = await this.model.find({ measurement: 0 }).lean();
      const unitPlanIds = zeroMeasurementUnitPlans.map((unitPlan) => (unitPlan?._id));

      // Aggregate to calculate min and max measurements for units with matching unitplan_ids
      const measurementRangeMap:measurementMap = {};
      const aggregationResult = await units_model.aggregate([
        {
          $match: {
            unitplan_id: { $in: unitPlanIds },
          },
        },
        {
          $group: {
            _id: '$unitplan_id',
            minMeasurement: { $min: '$measurement' },
            maxMeasurement: { $max: '$measurement' },
          },
        },
      ]);// Process aggregation result to populate measurementRangeMap

      for (const result of aggregationResult) {
        const unitPlanId = result._id.toString();
        measurementRangeMap[unitPlanId] = {
          minMeasurement: result.minMeasurement,
          maxMeasurement: result.maxMeasurement,
        };
      }

      // Combine unitPlans data with measurement range for zero measurement unitplans
      const combinedResult = unitPlans.map((unitPlan) => {
        const unitPlanIdString = unitPlan._id.toString();
        if (unitPlanIdString in measurementRangeMap) {
          const { minMeasurement, maxMeasurement } = measurementRangeMap[unitPlanIdString];
          return {
            ...unitPlan,
            minMeasurement: minMeasurement,
            maxMeasurement: maxMeasurement,
          };
        }
        return unitPlan;

      });

      const resultantObject = combinedResult.reduce((acc, obj) => {
        acc[obj._id] = obj;
        return acc;
      }, {} as ObjectWithIdKey);
      logger.info('getUnitPlanDataWithMeasurementRange Successful', {resultantObject: resultantObject});
      return resultantObject;
    } catch (error) {
      logger.info('Error in getUnitPlanDataWithMeasurementRange', {message: error});
      throw error;
    }
  }

  public async updateUnitBedrooms (combinedResult: object): Promise<void> {
    for (const [key, unit] of Object.entries(combinedResult)) {
      console.log('key', key);
      const specialCases = ['studio', 'penthouse', 'townhouse', 'duplex', 'suite'];
      if (
        !specialCases.includes(unit.bedrooms.toString().toLowerCase()) &&
          !unit.bedrooms.toString().includes('BHK')
      ) {
        unit.bedrooms = `${unit.bedrooms}BHK`;
        await this.model.findOneAndUpdate(
          { _id: key },
          { $set: { bedrooms: unit.bedrooms } },
          { new: true },
        );
      }
    }
  }

  public async getUnitplan (
    unitplan_id: string,
  ): Promise<unitplanType[] | null> {
    logger.info('getUnitplan Called', {unitplan_id: unitplan_id});
    const query = {
      _id: unitplan_id,
    };
    const unitplan = await this.model.findOne(query);
    logger.info('getUnitplan Successful', {unitplan: unitplan});
    return unitplan as unitplanType[] | null;
  }
  public async editUnitplan (
    unitplan_id: string,
    updatedData: UnitplanInterface,
    action:string,
  ): Promise<object> {
    logger.info('editUnitplan Called', {unitplan_id: unitplan_id, updatedData: updatedData, action: action});
    return new Promise<object>((resolve, reject) => {
      const { floor_unitplans, type, ...setData } = updatedData;
      const updateObj: any = action==='append'?{
        $set: setData,
        $push: {floor_unitplans: floor_unitplans},
      }:{
        $set: {...setData, floor_unitplans},
      };

      // If type is 'null' or null, add $unset to the update operation
      if (type === 'null' || type === null) {
        updateObj.$unset = { type: 1 };
      } else {
        // Otherwise, set the type field
        updateObj.$set.type = type;
      }

      this.model
        .findOneAndUpdate(
          { _id: unitplan_id },
          updateObj,
          { new: true },
        )
        .then((updatedUnitplan) => {
          if (!updatedUnitplan) {
            logger.info('editUnitplan is empty');
            resolve({});
          } else {
            logger.info('editUnitplan Successful', {updatedUnitplan: updatedUnitplan});
            resolve(updatedUnitplan.toObject());
          }
        })
        .catch((err) => {
          logger.error('Error in editUnitplan', {message: err});
          reject(err);
        });
    });
  }
  public async getStyles (): Promise<string[] | null>{
    logger.info('getStyles Called');
    try {
      const unitPlanItems = await this.model.distinct('style');
      logger.info('getStyles Successfull', {unitPlanItems: unitPlanItems});
      return unitPlanItems as string[] || null;
    } catch (error) {
      logger.error('Internal Server Error: in getStyles', {message: error});
      return null;
    }
  }
  public async createHotspots (unitplan_id: string, hotspots: object): Promise<object> {
    logger.info('createHotspots Called', { unitplan_id, hotspots });
    try {
      const hotspotData = {
        _id: new Types.ObjectId().toString(),
        ...hotspots,
      };
      const updatedHotspots = await this.model.findByIdAndUpdate(
        { _id: unitplan_id },
        {
          $set: {
            [`hotspots.${hotspotData._id}`]:
                  hotspotData,
          },
        },
        { new: true },
      );

      if (updatedHotspots){
        return updatedHotspots;
      }
      throw new Error('Error create hotspots');
    } catch (err) {
      throw new Error('' + err);
    }

  }

  public async editHotspots (unitplan_id: string, hotspot_id:string, hotspots: object): Promise<object> {
    logger.info('editHotspots Called', { unitplan_id, hotspot_id, hotspots });
    try {
      const previousHotspotData = <Record<string, string | number> | null> await this.model.findOne(
        {
          _id: unitplan_id,
          [`hotspots.${hotspot_id}`]: { $exists: true },
        },
      );

      logger.info('previousHotspotData', previousHotspotData);

      // Check tour data
      if (!previousHotspotData) {
        throw new Error('Document not found ');
      }

      const query = { _id: unitplan_id };

      // Create the update object
      const update: { $set: Record<string, string | number | null>, $unset?: Record<string, 1> } = {
        $set: {},
      };

      // Loop over hotspots to update existing keys dynamically
      for (const [key, value] of Object.entries(hotspots)) {
        const fieldPath = `hotspots.${hotspot_id}.${key}`;
        if (value === null) {
          if (!update.$unset) update.$unset = {};
          update.$unset[fieldPath] = 1;
        } else {
          update.$set[fieldPath] = value;
        }
      }

      if ('type' in hotspots){
      // Handle type-based field removal
        switch (hotspots.type) {
          case 'default':
            update.$unset = {
              ...(update.$unset || {}),
              [`hotspots.${hotspot_id}.group_id`]: 1,
              [`hotspots.${hotspot_id}.subgroup_id`]: 1,
              [`hotspots.${hotspot_id}.label_id`]: 1,
            };
            break;
          case 'label':
            update.$unset = {
              ...(update.$unset || {}),
              [`hotspots.${hotspot_id}.image_id`]: 1,
            };
            break;
          case 'group':
            update.$unset = {
              ...(update.$unset || {}),
              [`hotspots.${hotspot_id}.image_id`]: 1,
              [`hotspots.${hotspot_id}.subgroup_id`]: 1,
              [`hotspots.${hotspot_id}.label_id`]: 1,
            };
            break;

          case 'subgroup':
            update.$unset = {
              ...(update.$unset || {}),
              [`hotspots.${hotspot_id}.image_id`]: 1,
              [`hotspots.${hotspot_id}.label_id`]: 1,
            };
            break;

        }
      }

      const updatedHotspot = await this.model.findOneAndUpdate(query, update, {
        new: true,
      });
      if (updatedHotspot) {
        return updatedHotspot;
      }
      throw new Error('Error updating hotspot ');
    } catch (err) {
      throw new Error('' + err);
    }

  }

  public async deleteHotspots (unitplan_id: string, hotspot_id: string): Promise<object> {
    logger.info('deleteHotspots Called', { unitplan_id, hotspot_id });
    try {

      // Find and check existing
      const query = {
        _id: unitplan_id,
        [`hotspots.${hotspot_id}`]: { $exists: true },
      };

      // Removal key
      const update = {
        $unset: { [`hotspots.${hotspot_id}`]: '' },
      };

      const updatedHotspot = await this.model.findOneAndUpdate(query, update, {
        new: true,
      },
      );

      if (updatedHotspot) {
        return updatedHotspot;
      }

      throw new Error('Error in deleting hotspots');
    } catch (err) {
      throw new Error('Error in deleting hotspots' + err);
    }
  }

  public async getTypes (): Promise<string[] | null>{
    logger.info('getTypes Called');
    try {
      const unitPlanTypeItems = await this.model.distinct('type');
      logger.info('getTypes Successfull', {unitPlanTypeItems: unitPlanTypeItems});
      return unitPlanTypeItems as string[] || null;
    } catch (error) {
      logger.error('Internal Server Error: in getTypes', {message: error});
      return null;
    }
  }

  public async moveToTrash (
    svgIds: Array<string>,
    project_id: string,
    organization_id: string,
    timeStamp: number,
    parentID?: string,
  ): Promise<unitplanType | void> {
    logger.info('moveToTrash Successfull',
      {svgIds: svgIds, project_id: project_id, organization_id: organization_id, timeStamp: timeStamp, parentID: parentID});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: svgIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    if (parentID) {
      await this.model.updateOne(
        { '_id': parentID },
        { $pull: { 'floor_unitplans': {$in: svgIds} } },
      );
    }
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    const UnitplandataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${project_id.toLowerCase()}${Models._UNITPLANS}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(UnitplandataToInsertObj),
    ])
      .then(async () => {
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: svgIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moveToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }

  public async restoreUnitplan (
    organization_id: string,
    project_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreSVG Called',
      {organization_id: organization_id, project_id: project_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      const createScenePromise = Object.values(restoredData.data).map(async (item) => {
        await this.createUnitplan(item);
      });

      await Promise.all([
        createScenePromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreSVG is Successfull');
        return 'SVG got restored';
      });
    } else {
      logger.error('Error in restoreSVG');
      throw new Error('Failed to restore svg data from trash');
    }
  }
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';
import { measurementType, priceCurrency, unitStatus } from '../../../types/units';

const createUnitValidate = [
  body('name', 'Building Name is required').notEmpty(),
  body('project_id', 'Building Type is required').notEmpty(),
  body('unitplan_id', 'Floor Count is required').notEmpty(),
  body('currency').isIn(Object.values(priceCurrency)),
  body('measurement').isNumeric().optional(),
  body('balcony_measurement').optional({ checkFalsy: true }).isNumeric(),
  body('balcony_measurement_type').optional().isIn(Object.values(measurementType)),
  body('suite_area').optional({ checkFalsy: true }).isNumeric(),
  body('suite_area_type').optional().isIn(Object.values(measurementType)),
  body('status', 'Invalid Status. Please ensure that you are using a valid status value')
    .isIn(Object.values(unitStatus))
    .notEmpty(),
  body('cta_link').optional().isString(),

  body('building_id')
    .optional({ checkFalsy: true })
    .isString()
    .withMessage('building_id must be a string'),

  // Validate floor_id with proper dependency on building_id
  body('floor_id')
    .custom((value, { req }) => {
      if (req.body.building_id && !value) {
        throw new Error('floor_id is required when building_id is provided');
      }
      return true;
    }),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createUnitValidate;

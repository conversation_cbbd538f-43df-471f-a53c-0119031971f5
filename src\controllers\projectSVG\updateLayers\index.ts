import { Request, Response } from 'express';
import { ProjectSVGModule } from '../../../modules/projectSVG';
import logger from '../../../config/logger';

export default async function updateLayers (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  const projectSVG = new ProjectSVGModule(
    request.body.project_id,
    organization_id,
  );

  projectSVG
    .updateLayers(request.body)
    .then((res) => {
      response.send({ status: 1, data: res });
    })
    .catch((err) => {
      logger.error('Error in updateLayers', {message: err});
      response.status(0).send({status: 0, message: err});
    });
  return;
}

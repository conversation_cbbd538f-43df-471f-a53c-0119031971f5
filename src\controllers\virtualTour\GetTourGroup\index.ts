import {  Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function GetTourGroup (
  req: ExtendedRequest,
  res: Response,
): Promise<void> {
  try {
    const organization_id = req.organization_id;
    const { project_id, tour_id } = req.params;

    if (!organization_id) {
      res.status(400).json({ status: 0, error: 'Organization ID is required' });
      return;
    }

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const groups = await tourModule.GetGroups(tour_id);

    if (!groups) {
      res.status(404).json({ status: 0, error: 'No groups found' });
      return;
    }

    res.status(200).json({ status: 1, data: groups });
  } catch (error) {
    logger.error('Error in GetGroups', { error });
    res.status(500).json({ status: 0, error: `Error fetching groups: ${error}` });
  }
}

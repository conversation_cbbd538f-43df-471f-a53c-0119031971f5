import { LeadsModule } from './../../../modules/leads/index';
import { MailerModule } from '../../../modules/mailer';
import { ProjectModule } from '../../../modules/projects';
import { UserModule } from '../../../modules/user';
import { Request, Response } from 'express';
import { mailUserData } from '../../../types/mailer';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';
import { NotificationModule } from '../../../modules/notification';
import { UserDetailsWithFCM } from '../../../types/meetings';
export default async function sendReminder (
  request: Request,
  response: Response,
):Promise<void> {
  try {
    const mailer = new MailerModule();
    const session_id = request.body.session_id;
    const sessionModule = new SessionModule();
    const sessionData = await sessionModule.getSessionById(session_id);
    const notificationModule = new NotificationModule(sessionData.user_id);
    console.log('SessionData from sendReminder---check if thread_id is ther', sessionData );
    if (!sessionData) {
      response.status(404).send({ status: 0, message: 'Session Data not found' });
      return;
    }
    const leadsModule = new LeadsModule(sessionData.organization_id);
    const leadsData = await leadsModule.GetLeadsBySessionId(session_id);

    if (!leadsData) {
      response.status(404).send({ status: 0, message: 'No Leads found for this session' });
      return;
    }

    const projectModule = new ProjectModule(sessionData.organization_id);
    const project = await projectModule.getProjectById(sessionData.project_id as string);

    if (!project) {
      response.status(404).send({ status: 0, message: 'Project Data not found' });
      return;
    }

    const userModule = new UserModule();
    const userDetail = await userModule.GetUserDetails(sessionData.user_id) as UserDetailsWithFCM;
    const username = await userModule.getUsername(sessionData.user_id);
    const userEmail = await userModule.getUserEmail(sessionData.user_id);
    if (!username || !userEmail) {
      response.status(404).send({ status: 0, message: 'User Data not found' });
      return;
    }
    const userData: mailUserData = {
      username: username,
      useremail: userEmail,
    };
    await mailer.sendHostReminderMail(sessionData, project, userData);
    if (leadsData.length > 0) {
      await mailer.SendGuestReminderMail(sessionData, leadsData, project, userData);
    }
    const tokens = userDetail.fcmToken;
    if (!tokens || (Array.isArray(tokens) && tokens.length === 0)) {
      response.status(400).json({
        status: 0,
        message: 'No FCM tokens available',
        error: 'User has no registered FCM tokens',
      });
      return;
    }
    notificationModule.CreateNotification(sessionData.user_id, sessionData.organization_id, sessionData.invite_link);
    const payload = {
      _id: sessionData.user_id,
      notification: {
        title: 'Meeting Reminder',
        body: 'Your meeting is scheduled to start soon.',
      },
      data: {
        meeting_id: sessionData._id.toString(),
        type: 'meeting_reminder',
        user_id: sessionData.user_id,
        organization: sessionData.organization_id,
      },
      tokens,
    };
    notificationModule.sendNotification(payload);
    response.status(200).send({ status: 1, data: 'Reminder Sent Successfully' });
  } catch (error) {
    logger.error('Error in sendReminder', {message: error});
    response.status(500).send({ status: 0, message: error });
  }

}

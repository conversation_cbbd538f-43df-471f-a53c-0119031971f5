import { ExtendedRequest } from '../../../types/extras';
import { GalleryModule } from '../../../modules/gallery';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreGallery (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.params;
  const gallery = new GalleryModule(project_id, organization_id);
  const trash_id = request.body.trash_id;
  await gallery
    .restoreGallery(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Gallery got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreGallery', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while deleting restore Gallery : '+ error });
    });
}

import { UserRecord } from 'firebase-admin/lib/auth/user-record';
import { UserRole } from './organization';
import { User } from '@firebase/auth';

export type StsTokenMananger = {
  refreshToken: string;
  accessToken: string;
  expirationTime: number;
};
export interface UserCred extends User {
  stsTokenManager?: StsTokenMananger | undefined;
}

export type UserClaims = {
  oganization_id: string;
  role: UserRole;
}[];

export type UserDetails = {
  auth_data: UserRecord;
  user_data: {
    first_name: string;
    last_name?: string;
    email: string;
    phone_number?: string;
    profilePicture?: string;
    role?: string;
  };
};

export type Users = {
  user_id: string;
  role: UserRole;
  email: string;
}

export type CreateUserInput = {
  first_name: string,
  last_name?:string,
  email:string,
  uid:string;
  role?: string;
}

export interface UpdateUserDetailsRequest {
  _id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number?: string;
  profilePicture?:string;
}

export type assignRoleObj = {
  user_id: string,
  organizationId: string,
  roleId: UserRole,
  email: string
}

export type userObj = {
  _id: string;
  email: string;
  organization_id: string[];
  phone_number?: string;
  profilePicture?: string;
};

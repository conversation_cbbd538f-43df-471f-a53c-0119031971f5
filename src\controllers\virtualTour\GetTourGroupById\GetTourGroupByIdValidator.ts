import { Request, Response, NextFunction } from 'express';
import { validationResult, header, param } from 'express-validator';

const GetTourGroupByIdValidator = [
  header('organization', 'Organization is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),

  // Validate params
  param('project_id', 'Invalid Project ID').notEmpty().isString(),
  param('tour_id', 'Invalid Tour ID').notEmpty().isString(),
  param('group_id', 'Invalid Group ID').notEmpty().isString(),

  // Middleware to handle validation errors
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default GetTourGroupByIdValidator;

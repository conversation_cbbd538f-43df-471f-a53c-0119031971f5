import { VirtualTourModule } from '../../../modules/virtualTour';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function GetStructuredTourById (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;

  if (!organization_id) {
    logger.error('Organization ID not found in request', { headers: request.headers });
    response.status(500).json({ status: 0, error: 'Organization ID not found' });
    return;
  }

  const { project_id, tour_id } = request.params;

  try {
    const virtualTour = new VirtualTourModule(organization_id, project_id);

    const virtualTourData = await virtualTour.GetTourById(tour_id);

    if (!virtualTourData) {
      logger.warn('Tour not found', { organization_id, project_id, tour_id });
      response.status(404).json({ status: 0, error: 'Tour not found' });
      return;
    }

    const images = virtualTourData.images ?? {};
    const groups = virtualTourData.groups ?? {};

    Object.values(images).forEach((image) => {
      const { groupId, subGroupId, id } = image;
      console.log(image, groupId, subGroupId);
      if (!groupId || !groups[groupId]) {
        return;
      }
      if (subGroupId && groups[groupId].subgroups?.[subGroupId]) {
        // Image belongs to a subgroup only
        if (!groups[groupId].subgroups[subGroupId].images) {
          groups[groupId].subgroups[subGroupId].images = {};
        }
        groups[groupId].subgroups[subGroupId].images[id] = image;
      } else if (!subGroupId) {
        // Image belongs to group only
        if (!groups[groupId].images) {
          groups[groupId].images = {};
        }
        groups[groupId].images[id] = image;
      }
    });

    response.status(200).json({ status: 1, data: virtualTourData });
  } catch (error) {
    logger.error('Error fetching virtual tour', {error});
    response.status(500).json({ status: 0, error: `Error fetching virtual tour: ${error}` });
  }
}

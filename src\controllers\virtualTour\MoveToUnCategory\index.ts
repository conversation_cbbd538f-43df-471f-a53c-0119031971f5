import {  Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function MoveToUnCategory (
  req: ExtendedRequest,
  res: Response,
): Promise<void> {
  try {
    const organization_id = req.organization_id;
    const { project_id, tour_id, subgroup_id } = req.body;

    if (!organization_id) {
      res.status(400).json({ status: 0, error: 'Organization ID is required' });
      return;
    }

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updatedTour = await tourModule.MoveToUnCategory(tour_id, subgroup_id);

    if (!updatedTour) {
      res.status(404).json({ status: 0, error: 'Tour or Subgroup is not found' });
      return;
    }

    res.status(200).json({ status: 1, data: updatedTour });
  } catch (error) {
    logger.error('Error in Move images to Uncategory', { error });
    res.status(500).json({ status: 0, error: `Error: ${error}` });
  }
}

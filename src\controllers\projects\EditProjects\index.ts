import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { ProjectModule } from '../../../modules/projects';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
interface UploadedFiles {
  [fieldname: string]: Express.Multer.File[]
}

export async function Editproject (
  request: ExtendedRequest,
  response: Response,
):Promise<void>{
  const organization_id = request.organization_id as string;
  const project = new ProjectModule(organization_id);
  const requestFiles = request.files as UploadedFiles | undefined;

  let uploadedThumbnail = '' as string;
  console.log(request.files);
  const experienceArray = request.body.experience?.split(',');
  if (requestFiles?.project_thumbnail){
    console.log('11111111111');
    await UploadUnitplanFiles(requestFiles, project.storagepath+request.body.id)
      .then((urlObject: { [key: string]: string }) => {
        uploadedThumbnail = urlObject.project_thumbnail;
      })
      .catch((error) => {
        logger.error('Error:', {message: error});
      });
  }
  console.log('2222222');

  const editProjectObj = {
    id: request.body.id,
    name: request.body.name,
    experience: experienceArray,
    property_type: request.body.property_type,
    city: request.body.city,
    country: request.body.country,
    project_thumbnail: uploadedThumbnail.length > 0? uploadedThumbnail:undefined,
  };
  console.log('editProjectObj2', editProjectObj);

  project.editProject(editProjectObj)
    .then((res) => {
      response.json({ status: 1, data: res });
    })
    .catch((error) => {
      if (error instanceof Error){
        logger.error('Error:', {message: error});
        response.json({ status: 0, message: error.message });
      }

    });

}

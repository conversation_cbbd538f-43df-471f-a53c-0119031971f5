import app  from '../../../app';
import request from 'supertest';
describe('Get Unit Plan Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/getUnitplan/'
      +'65701718b5063acb09dbf6e4/657c19334af78d6fdae8f2e0?organization_id=8X3plU')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual({});
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getUnitplan/'
      +'65701718b5063acb09dbf64/657c19334af78d6fdae8f2e0?organization_id=8X3plU')   // Invalid project Id
      .send();
    expect(res.statusCode).toEqual(500);
    expect(res.body).toHaveProperty('error');
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getUnitplan/'
      +'65701718b5063acb09dbf64/657c19334af78d6fdae8f2e?organization_id=8X3plU')   // Invalid unitplan Id
      .send();
    expect(res.statusCode).toEqual(500);
    expect(res.body).toHaveProperty('error');
  });

});

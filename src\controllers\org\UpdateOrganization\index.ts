import { Response } from 'express';

import { OrganizationModule } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import {
  Organization,
  UpdateOrganizationInput,
} from '../../../types/organization';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import logger from '../../../config/logger';
import {generateFontLink } from '../../../helpers/projects';
import { FontType } from '../../../types/projects';
export default async function UpdateOrganization (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization = new OrganizationModule();
  const organization_id = request.organization_id;
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }
  const requestFiles = request.files;
  if (requestFiles) {
    UploadUnitplanFiles(requestFiles, 'CreationtoolAssets/'+organization_id)
      .then((urlObject: { [key: string]: string }) => {
        const updateFields: UpdateOrganizationInput = {
          ...request.body,
        };
        updateFields.thumbnail = urlObject.thumbnail;
        updateFields.font_url = urlObject.font_url;
        organization
          .UpdateOrganization(organization_id, updateFields)
          .then((organizations: Organization) => {
            response.send({ status: 1, data: organizations });
          })
          .catch((error) => {
            logger.error('error while updating ', {message: error});
            response.send({ status: 0, error: 'error while updating' + error });
          });
      });
  } else {
    const updateFields: UpdateOrganizationInput = {
      ...request.body,
    };
    const font_type = request.body.font_type;
    if (font_type as FontType) {
      const fontLink = await generateFontLink(font_type);
      updateFields.font_url = fontLink;
    }
    organization
      .UpdateOrganization(organization_id, updateFields)
      .then((organizations: Organization) => {
        response.send({ status: 1, data: organizations });
      })
      .catch((error) => {
        logger.error('error while updating ', {message: error});
        response.send({ status: 0, error: 'error while updating' + error });
      });
  }

}

import { Request, Response } from 'express';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';
export async function AssignSession (
  request: Request,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const organization_id = await session.getOrganizationId(request.body.session_id);
  const user_id = request.body.user_id as string;
  const session_id = request.body.session_id as string;
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }

  session
    .AssignSession(user_id, session_id)
    .then((sessionData) => {
      response.status(200).send({ status: 1, data: sessionData });
    })
    .catch((error) => {
      logger.error('Error in AssignSession', {message: error});
      response.status(409).send({ status: 0, error: 'Error while updating session' + error });
    });
}

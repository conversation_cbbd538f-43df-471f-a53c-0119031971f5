import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { Link } from '../../../types/virtualTour';
import { VirtualTourModule } from '../../../modules/virtualTour';
import logger from '../../../config/logger';

export async function UpdateLink (req: ExtendedRequest, res: Response): Promise<void> {
  try {
    const organization_id = req.organization_id as string;
    const project_id = req.body.project_id as string;
    const tour_id = req.body.tour_id as string;
    const image_id = req.body.image_id as string;
    const link_id = req.body.link_id as string;

    const updateData: Partial<Link> = {};

    if (req.body.position) {
      updateData.position = {
        x: req.body.position.x,
        y: req.body.position.y,
        z: req.body.position.z,
      };
    }
    if (req.body.text) {
      updateData.text = req.body.text;
    }
    if (req.body.destination_img_id) {
      updateData.destination_img_id = req.body.destination_img_id;
    }

    const tourModule = new VirtualTourModule(organization_id, project_id);
    const updatedTour = await tourModule.UpdateLink(tour_id, image_id, link_id, updateData);

    res.status(200).json({
      status: 1,
      message: 'Link updated successfully',
      data: updatedTour,
    });
  } catch (error) {
    logger.error('Error in UpdateLink', { error });
    res.status(500).json({
      status: 0,
      error: `Error updating link: ${error instanceof Error ? error.message : error}`,
    });
  }
}

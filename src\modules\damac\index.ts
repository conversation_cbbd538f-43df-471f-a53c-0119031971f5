import axios from 'axios';
import logger from '../../config/logger';

export class ExternalUnitModule {
  private readonly authApi: string;

  constructor () {
    this.authApi = process.env.DAMAC_API || '';
  }

  private async getInitialToken (): Promise<string> {
    try {
      const response = await axios.post(this.authApi+'/auth/get-token', {
        email: process.env.API_USERNAME,
        password: process.env.API_PASSWORD,
      });
      logger.info('Initial token obtained successfully');
      return response.data.data.refreshToken;
    } catch (error) {
      logger.error('Error getting initial token', { error });
      throw new Error('Failed to get initial token');
    }
  }

  private async refreshToken (initialToken: string): Promise<string> {
    try {
      const response = await axios.post(this.authApi+'/auth/refresh-token', {
        token: initialToken,
      });

      logger.info('Token refreshed successfully');
      return response.data.data.jwtToken;
    } catch (error) {
      logger.error('Error refreshing token', { error });
      throw new Error('Failed to refresh token');
    }
  }

  private async fetchUnitsList (
    refreshedToken: string,
    id: string,
    offset?: number,
    isRecursive: boolean = false,
  ): Promise<object[] | null> {
    // Construct URL based on whether it's a recursive call
    const url = isRecursive
      ? this.authApi + `/ct/metaverse/${id}?offset=${offset}`
      : this.authApi + `/ct/metaverse/${id}`;

    console.log(url);
    logger.info('End point:-', url);
    try {
      const response = await axios.get(url, {
        headers: {
          Authorization: `Bearer ${refreshedToken}`,
          'Content-Type': 'application/json',
        },
      });

      console.log(response.data.data.Inventory.Units.length);
      logger.info(`Units list fetched successfully for offest ${offset}`);

      const units = response.data.data.Inventory.Units;

      // If the length of the data is 500, fetch the next batch recursively
      if (units.length === 500) {
        // Calculate next offset (501, 1001, 1501, etc.)
        const nextOffset = offset ? offset + 500 : 501;
        const nextUnits = await this.fetchUnitsList(refreshedToken, id, nextOffset, true);

        if (nextUnits) {
          return [...units, ...nextUnits];
        }
        return units;

      }

      logger.info(`Total ${units.length} Units fetched successfully`);

      return units; // Return the final batch of units
    } catch (error) {
      logger.error('Error fetching units list', { error });
      throw new Error('Failed to fetch units list');
    }
  }

  public async getUnitsList (id:string): Promise<object | null> {
    try {
      // Step 1: Get initial token
      const initialToken = await this.getInitialToken();

      // Step 2: Refresh token
      const refreshedToken = await this.refreshToken(initialToken);

      // Step 3: Get units list (without offset initially)
      const unitsList = await this.fetchUnitsList(refreshedToken, id);
      return unitsList;
    } catch (error) {
      logger.error('Error in getUnitsList flow', { error });
      throw error;
    }
  }
}

import logger from '../../../config/logger';
import { Request, Response } from 'express';
import { TranslationModule } from '../../../modules/translation';

export async function getTranslations (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.params.organization_id as string;
  const translations = new TranslationModule(organization_id);
  const translationData = await translations.GetAllTranslation();

  if (translationData) {
    response.status(200).json({ status: 1, data: translationData });
  } else {
    logger.error('No Translations found');
    response.status(404).json({ status: 0, error: 'No Translations found' });
  }
}

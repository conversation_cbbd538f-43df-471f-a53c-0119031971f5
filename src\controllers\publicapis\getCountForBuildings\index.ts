import { Request, Response } from 'express';
import { PublicApis } from '../../../modules/publicapis';
import logger from '../../../config/logger';

export async function getCountforBuilding (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.params.organization_id as string;
  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'organization_id not found'});
    return;
  }
  const project_id = request.params.project_id as string;
  //   Pass organization_id in this ProjectModule
  const projectModule = new PublicApis(organization_id);

  const projectData = await projectModule.GetCountForBuildings(project_id);

  if (projectData) {
    response.status(200).json({ status: 1, data: projectData });
  } else {
    logger.error('Projects not found');
    response.status(404).json({ status: 0, error: 'Projects not found' });
  }
}

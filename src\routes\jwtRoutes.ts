import express from 'express';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { JWTAuthControl } from '../middlewares/jwtController';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { CreateJWT } from '../controllers/jwt/CreateJWT';
import { GetJWT } from '../controllers/jwt/GetJWT';
import CreateJWTValidator from '../controllers/jwt/CreateJWT/CreateJWTValidate';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import { updateJWT } from '../controllers/jwt/updateJWT';
import updateJWTValidate from '../controllers/jwt/updateJWT/updateJWTValidator';
import { accessToken } from '../controllers/jwt/CreateAccessToken';
import AccessTokenValidator from '../controllers/jwt/CreateAccessToken/AccessTokenValidator';
import { getRefreshTokens } from '../controllers/jwt/GetRefreshTokens';
import getRefreshTokenValidator from '../controllers/jwt/GetRefreshTokens/getRefreshTokenValidator';
import { createRefToken } from '../controllers/jwt/CreateRefreshToken';
import createRefTokenValidator from '../controllers/jwt/CreateRefreshToken/createRefTokenValidator';

const router = express.Router();

router.post('/createjwt',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  CreateJWTValidator,
  CreateJWT,
);

router.post('/auth',
  JWTAuthControl,
  GetJWT,
);

router.post('/updateJWT',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  updateJWTValidate,
  updateJWT,
);
router.post('/accesstoken',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  AccessTokenValidator,
  accessToken,
);
router.get('/getRefreshtokens/:organization',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  getRefreshTokenValidator,
  getRefreshTokens,
);

router.post('/createRefToken',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  createRefTokenValidator,
  createRefToken,
);

export default router;

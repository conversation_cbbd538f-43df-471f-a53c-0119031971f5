import { Request, Response } from 'express';
import { masterLandmarkModule } from '../../../modules/masterLandmark';
import logger from '../../../config/logger';

export async function getListofLandmark (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.params.organization_id as string;
  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'organization_id not found'});
    return;
  }
  const masterLandmark = new masterLandmarkModule(organization_id);

  const masterLandmarkData = await masterLandmark.getListofLandmark();
  if (masterLandmarkData) {
    response.status(200).json({ status: 1, data: masterLandmarkData});
  } else {
    logger.error('landmark not found');
    response.status(500).json({ status: 0, error: 'landmark not found' });
  }
}

import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { TranslationModule } from '../../../modules/translation';
import { SupportedLanguages } from '../../../types/translation';
import logger from '../../../config/logger';

export async function AddNewLanguage (request: ExtendedRequest, response: Response): Promise<void> {
  const { targetLanguageCode } = request.query;
  const organization_id = request.organization_id as string;
  const translations = new TranslationModule(organization_id);

  try {
    await translations.AddNewLanguage(targetLanguageCode as SupportedLanguages);
    response.status(200).send({ status: 1,
      data: `Language '${targetLanguageCode}' added successfully in all documents.` });
  } catch (error) {
    logger.error('Error adding language', error);
    console.error(error);
    response.status(500).json({ message: 'Error adding language', error: error });
  }
}

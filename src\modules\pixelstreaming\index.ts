import mongoose, {Types} from 'mongoose';
import { ScaleSet, ScaleSetVerison, accesstokenType } from '../../types/pixelstreaming';
import { scalesetSchema } from '../../schema/pixelstreamingSchema';
import logger from '../../config/logger';
import axios from 'axios';
import https from 'https';
import { Models } from '../../types/extras';
export class ScaleSetModule {
  private model: mongoose.Model<ScaleSet>;
  private clientId = process.env.AZURE_CLIENT_ID as string;
  private clientSecret = process.env.AZURE_CLIENT_SECRET as string;
  private subscriptionId = process.env.AZURE_SUBSCRIPTION_ID as string;
  private tenantId = process.env.AZURE_TENANT_ID as string;
  private resourceGroupName: string;
  private vmScalesetName: string;
  private tokenEndpoint = `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`;
  constructor (resourceGroup: string, vmScalesetName: string) {
    this.resourceGroupName =  resourceGroup;
    this.vmScalesetName =  vmScalesetName;
    this.model = mongoose.model<ScaleSet>(`${Models.SCALESET}`, scalesetSchema);
  }
  public async addScaleSetVersion (data: ScaleSetVerison, project_id:string, organization_id:string): Promise<boolean> {
    logger.info('addScaleSetVersion Called', {data: data, project_id: project_id, organization_id: organization_id});
    try {

      const lastScaleSet = await this.getLastScaleSetVersion(project_id, organization_id);
      const lastVersion: ScaleSetVerison = lastScaleSet;

      if (!lastVersion) {
        return false;
      }

      const newVersion = {
        ...lastVersion,
        ...data,
        version: lastVersion.version + 1,
      };

      // Two filter conditions
      const filter = {
        project_id: project_id,
        organization_id: organization_id,
      };

      const result = await this.model.updateOne(
        filter,
        {
          $push: { versions: newVersion },
        },
      );
      if (result.modifiedCount===1){
        logger.info('addScaleSetVersion Successfully');
        return true;
      }
      return false;
    } catch (err){
      logger.error('Error in addScaleSetVersion', {message: err});
      return false;
    }
  }

  public async getLastScaleSetVersion (project_id:string, organization_id:string): Promise<ScaleSetVerison> {
    logger.info('getLastScaleSetVersion Called', {project_id: project_id, organization_id: organization_id});
    try {
      const lastScaleSet = await this.model.aggregate(
        [
          {
            $match: {
              project_id: project_id,
              organization_id: organization_id,
            },
          },
          {
            $unwind: '$versions', // Unwind the "versions" array
          },
          {
            $sort: {
              'versions.version': -1, // Sort by the "version" field in ascending order
            },
          },
          {
            $limit: 1,
          },
        ],
        {
          versions: 1,
        },
      );
      if (lastScaleSet[0]){
        logger.info('getLastScaleSetVersion Successfully', {lastScaleSet: lastScaleSet[0].version});
        return lastScaleSet[0].versions;
      }
      logger.error('Error getting latest version');
      throw new Error('Error getting latest version');

    } catch (err) {
      logger.error('Error in getLastScaleSetVersion', {message: err});
      throw new Error();
    }
  }

  public async addScaleSet (data:ScaleSetVerison, project_id:string, organization_id:string):Promise<ScaleSet | string>{
    logger.info('addScaleSet Called', {data: data, project_id: project_id, organization_id: organization_id});
    try {

      const scaleSetData = await this.model.find({project_id: project_id, organization_id: organization_id});
      if (scaleSetData.length>0){
        logger.error('Scaleset already exists for this project');
        throw new Error('Scaleset already exists for this project');
      }

      const newScaleSet = {
        _id: new Types.ObjectId().toString(),
        versions: [data],
        project_id: project_id,
        organization_id: organization_id,
      };
      const scaleSet = new this.model(newScaleSet);
      const latestScaleSet = await scaleSet.save();
      logger.info('addScaleSet Successfully', {latestScaleSet: latestScaleSet});
      return latestScaleSet;
    } catch (error) {
      logger.error('Unable to create scaleset for this project', {message: error});
      throw new Error('Unable to create scaleset for this project '+error);
    }
  }
  public async getAccessToken (): Promise<accesstokenType> {
    try {
      const response = await axios.post(
        this.tokenEndpoint,
        new URLSearchParams({
          client_id: this.clientId,
          client_secret: this.clientSecret,
          scope: 'https://management.azure.com/.default',
          grant_type: 'client_credentials',
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
      console.log('Access token obtained successfully.');
      return response.data as accesstokenType;
    } catch (error) {
      console.error('Error obtaining access token:', error);
      throw error;
    }
  }
  // Function to get the current capacity of the VM Scale Set
  public async getCurrentCapacity (accesstoken: accesstokenType): Promise<number> {
    const vmssEndpoint = `https://management.azure.com/subscriptions/${this.subscriptionId}`
  + `/resourceGroups/${this.resourceGroupName}`
  + `/providers/Microsoft.Compute/virtualMachineScaleSets/${this.vmScalesetName}`
  + '?api-version=2022-11-01';

    try {
      console.log('Fetching VM Scale Set details...');
      const response = await axios.get(vmssEndpoint, {
        headers: {
          Authorization: `Bearer ${accesstoken.access_token}`,
        },
      });

      console.log('VM Scale Set details retrieved successfully.');
      return response.data.sku.capacity;
    } catch (error) {
      console.error('Error fetching VM Scale Set details:', error);
      throw error;
    }
  }
  // Function to update the VM Scale Set capacity
  public async updateVmScaleSetCapacity (accessToken: accesstokenType, newCapacity: number): Promise<void> {
    const vmssEndpoint = `https://management.azure.com/subscriptions/${this.subscriptionId}`
  + `/resourceGroups/${this.resourceGroupName}`
  + `/providers/Microsoft.Compute/virtualMachineScaleSets/${this.vmScalesetName}`
  + '?api-version=2022-11-01';

    try {
      const updateParams = {
        sku: {
          capacity: newCapacity,
        },
      };

      console.log(`Updating VM Scale Set capacity to ${newCapacity}...`);
      await axios.patch(vmssEndpoint, updateParams, {
        headers: {
          Authorization: `Bearer ${accessToken.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      console.log(`VM Scale Set updated to ${newCapacity} instances.`);
    } catch (error) {
      console.error('Error updating VM Scale Set capacity:', error);
    }
  }
  public async getRunningVMsWithNoConnectedDevices (sourceURL: string): Promise<object> {
    const agent = new https.Agent({
      rejectUnauthorized: false,
    });
    try {
      console.log(`${sourceURL}/getstats`);
      const response = await axios.get(`${sourceURL}/getstats`, { httpsAgent: agent });
      console.log('Full Response:', response);
      const stats = response.data;
      if (Array.isArray(stats.cirrus)) {
        interface vmType {
          vmName: string,
          connectedDevices: number
        }
        // Filter the cirrus array based on specific condition, e.g., connectedDevices === 0
        const filteredVMs = stats.cirrus.filter((vm: vmType) => {
          // Assuming the structure of each VM object in cirrus has a 'connectedDevices' property
          return vm.connectedDevices === 0;
        });
        // Extract and return the names or any other relevant details of filtered VMs
        const vmNames = filteredVMs.map((vm: vmType) => vm.vmName); // Assuming 'vmName' exists in the VM object
        console.log('Filtered VMs with no connected devices:', vmNames);
        return vmNames;
      }
      console.error('Cirrus data is missing or not an array');
      return [];

    } catch (error) {
      console.log(error);
      return {};
      // Logger.error('Error fetching VM stats:', error);
    }
  }
}

import { Request, Response } from 'express';
import { buildingModule } from '../../../modules/building';
import { Building } from '../../../types/building';
import logger from '../../../config/logger';

export async function DeleteBuilding (request: Request, response: Response): Promise<Building | void> {
  const {project_id} = request.body;
  const {building_id}  = request.params;
  const {organization_id}  = request.params;
  const building = new buildingModule(project_id, organization_id);
  building.DeleteBuilding(building_id).then((res) => {
    response.send({status: 1, data: res});
  })
    .catch((err) => {
      logger.error('Error', {message: err});
      response.send({status: 0, message: err});
    });
  return;
}

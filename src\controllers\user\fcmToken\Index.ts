import { Request, Response } from 'express';
import { UserModule } from '../../../modules/user';
import logger from '../../../config/logger';

export async function updateFcmTokenController (req: Request, res: Response): Promise<void> {
  logger.info('updateFcmTokenController called', { body: req.body });

  try {
    const { _id, fcmToken, action } = req.body;

    if (!action || (action !== 'add' && action !== 'remove')) {
      res.status(400).json({ status: 0, error: 'Valid action (add or remove) is required' });
      return;
    }

    const userModule = new UserModule();
    const result = await userModule.updateFcmToken(_id, fcmToken, action);

    if (!result) {
      res.status(404).json({ status: 0, error: 'User not found' });
      return;
    }

    res.status(200).json({
      status: 1,
      data: result,
    });
  } catch (error) {
    logger.error('Error in updateFcmTokenController', { error });
    res.status(500).json({ status: 0, error: 'Failed to update FCM token' });
  }
}

import { SessionModule } from '../../../modules/sessions';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
export async function GetAnonymousSessions (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const session = new SessionModule();
  const sessionData = await session.getAnonymousSessions(organization_id);
  if (sessionData){
    response.status(200).json({ status: 1, data: sessionData });
  } else {
    logger.error('Not Authorised');
    response.status(404).json({ status: 0, error: 'Not Authorised' });
  }
}

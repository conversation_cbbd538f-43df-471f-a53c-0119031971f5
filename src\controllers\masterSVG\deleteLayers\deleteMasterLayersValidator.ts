import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const deleteMasterLayersValidator = [
  body('layer_id', 'Layer ID is required').notEmpty(),
  body('svg_id', 'SVG ID is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default deleteMasterLayersValidator;

import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { ModelModule } from '../../../modules/glbModel';
import { glbmodel, meshes } from '../../../types/glbModels';
import { Request, Response } from 'express';
import mongoose from 'mongoose';
export async function CreateModel (
  request: Request,
  response: Response,
): Promise<glbmodel | void> {
  const project_id = request.body.project_id;
  const organization_id = request.headers.organization as string;
  const model = new ModelModule(project_id, organization_id);
  const requestFiles = request.files;
  const id = new mongoose.Types.ObjectId();
  const mesh_ids = Array.from({ length: request.body.meshcount }, () => new mongoose.Types.ObjectId().toString());
  if (requestFiles) {
    const mesh:Record<string, meshes>  = {};
    if (mesh_ids){
      mesh_ids.forEach((item) => {
        mesh[item] = {
          id: item,
        };
      });
    }
    UploadUnitplanFiles(requestFiles, model.storagepath+id)
      .then((urlObject: { [key: string]: string }) => {
        const modelData = {
          name: request.body.name,
          description: request.body.description,
          thumbnail: urlObject.thumbnail,
          url: urlObject.file,
          meshes: mesh,
          id: id,
        };
        model
          .CreateModel(modelData)
          .then((modelDataRes) => {
            response.status(201).json({ status: 1, data: modelDataRes });
          })
          .catch((error: Error) => {
            logger.error('Error while creating model', {message: error});
            response
              .status(500)
              .json({ status: 0, error: 'Error while creating model'+ error });
          });
      }).catch((error) => {
        logger.error('Error uploading files', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error uploading files'+ error });
      });
  }
}

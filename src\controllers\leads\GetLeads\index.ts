import { ExtendedRequest } from '../../../types/extras';
import { LeadsModule } from '../../../modules/leads';
import { Response } from 'express';
import { UserRole } from '../../../types/organization';
import logger from '../../../config/logger';

export async function GetLeads (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const leadModule = new LeadsModule(organization_id);
  const user_id_query = request.query.user_id as string;
  const user_id = request.IsAuthenticated?.uid as string;
  const userRole = request.UserRole;

  // Convert comma-separated strings to arrays for multi-selection
  const query = {
    ...request.query,
    project_id: request.query.project_id
      ? String(request.query.project_id).split(',')
      : undefined,
    status: request.query.status
      ? String(request.query.status).split(',')
      : undefined,
  };

  let leadsData;

  if (userRole?.role === UserRole.ADMIN) {
    leadsData = await leadModule.GetLeads(organization_id, query);
  } else if (userRole?.role === UserRole.EDITOR || userRole?.role === UserRole.READER) {
    if (!user_id_query) {
      logger.error('Pass user_id to retrieve sessions');
      response.status(404).json({ status: 0, error: 'Pass user_id to retrieve sessions' });
      return;
    }
    if (user_id_query === user_id) {
      leadsData = await leadModule.GetLeads(organization_id, query);
    } else {
      logger.error('Permission Denied');
      response.status(404).json({ status: 0, error: 'Permission Denied' });
      return;
    }
  }

  if (leadsData) {
    response.status(200).json({
      status: 1,
      data: leadsData,
      length: Object.values(leadsData).length,
    });
  } else {
    logger.error('No Leads Found');
    response.status(404).json({ status: 0, error: 'No Leads Found' });
  }
}

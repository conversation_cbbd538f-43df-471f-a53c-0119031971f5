import logger from '../../../config/logger';
import { ProjectSVGModule } from '../../../modules/projectSVG';
import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
export async function updateBulkSVGs (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.headers.organization as string;
  const projectSVGMod = new ProjectSVGModule(project_id, organization_id);
  projectSVGMod.bulkUpdateSVGs(request.body)
    .then((res) => {
      response.status(201).json({ status: 1, message: res });
    })
    .catch((error: Error) => {
      logger.error('Errors:', {message: error});
      response
        .status(500)
        .json({ status: 0, error: error});
    });
}

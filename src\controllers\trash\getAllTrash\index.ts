import { Request, Response } from 'express';
import { trashModule } from '../../../modules/trash';
import logger from '../../../config/logger';

export async function getAllTrash (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.headers.organization as string;
  const type = request.query.type as string;
  const project_id = request.query.project_id as string;
  const organization = request.query.organization_id as string;
  const page = Number(request.query.page) || 1;
  const limit = 10;

  try {
    const trash = new trashModule(organization_id);
    const result = await trash.getAllTrash(type, project_id, organization, page, limit);

    const { items, total } = result;

    response.status(200).json({
      status: 1,
      data: {total, items},
    });
  } catch (error) {
    logger.error('Error fetching trash data', { error });
    response.status(500).json({ status: 0, error: 'Internal Server Error' });
  }
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';
import { convertSceneTypeAction } from '../../../types/projectScene';

const converSceneTypeValidate = [
  body('high_resolution', 'timeStamp is required').notEmpty(),
  body('scene_id', 'Scene ID is required').notEmpty(),
  body('toType', 'To type is required').notEmpty(),
  body('fromType', 'From type is required').notEmpty(),
  body('project_id', 'Project ID is required').notEmpty(),
  body('action', 'Action is required').notEmpty().isIn(Object.values(convertSceneTypeAction)),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default converSceneTypeValidate;

import {Response, Request} from 'express';
import {ProjectLandmarkModule} from '../../../modules/projectLandmark';
import logger from '../../../config/logger';

export default async function getRoutes (request:Request, response:Response):Promise<void> {
  const organization_id = request.headers.organization as string;
  const projectLandmark = new ProjectLandmarkModule(request.body.project_id, organization_id);
  projectLandmark.getRoutes(request.body).then(async (res) => {
    response.send({status: 1, data: res});
    return;
  })
    .catch((error) => {
      logger.error('Error in getRoutes', {message: error});
      response.send({status: 0, message: error});
      return;
    });
}

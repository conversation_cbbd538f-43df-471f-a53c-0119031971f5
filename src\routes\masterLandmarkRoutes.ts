import express from 'express';
import createLandmark from '../controllers/masterLandmarks/createLandmark';
import { getListofLandmark } from '../controllers/masterLandmarks/getListofLandmark';
import getListofLandmarksValidate from '../controllers/masterLandmarks/getListofLandmark/getListofLandmarks';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import CreateLandmarkValidate from '../controllers/masterLandmarks/createLandmark/createLandmarkValidator';
import uploadHandler from '../helpers/uploadHandler';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
const router = express.Router();

router.post(
  '/createLandmark',
  authMiddleware, organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  uploadHandler([{ name: 'thumbnail' }], 'masterLandmarks/'),
  CreateLandmarkValidate,
  createLandmark,
);
router.get('/getListofLandmark',
  getListofLandmarksValidate,
  authMiddleware,
  organizationAccessMiddleware,  getListofLandmark);

export default router;

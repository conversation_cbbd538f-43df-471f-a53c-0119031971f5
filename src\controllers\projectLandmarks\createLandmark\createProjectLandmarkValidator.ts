import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { landmarkCategory  } from '../../../types/projectLandmark';

interface UploadedFiles {
  thumbnail: Express.Multer.File[]
}

const CreateProjectLandmarkValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {
  const files = req.files as UploadedFiles | undefined;
  if (files) {
    if (!files.thumbnail) {
      res
        .status(400)
        .json({ error: 'Thumbnail image field is required.' });
    } else {
      const requiredTextFields = [
        'project_id',
        'name',
        'category',
      ];

      const missingTextFields = requiredTextFields.filter(
        (field) => !(field in req.body),
      );

      if (missingTextFields.length > 0) {
        res.status(400).json({
          error: `Missing text fields: ${missingTextFields.join(', ')}`,
        });
      } else {
        body('category', 'Invalid type value. Please ensure that you are using a valid type value')
          .isIn(Object.values(landmarkCategory)).run(req);
        const errors = validationResult(req);

        if (!errors.isEmpty()) {
          console.log(errors);
        } else {
          next();
        }
      }
    }
  } else {
    res.status(400).json({ error: 'Invalid file structure in the request.' });
  }
};

export default CreateProjectLandmarkValidate;

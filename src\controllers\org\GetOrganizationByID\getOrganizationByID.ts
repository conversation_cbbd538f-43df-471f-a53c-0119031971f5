import { Response } from 'express';
import { OrganizationModule } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import { Organization } from '../../../types/organization';

export default async function getOrganizationByID (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organizationModule = new OrganizationModule();

  const organizationId = request.params.id;

  if (!organizationId) {
    response.send({ status: 0, error: 'No organization ID provided' });
    return;
  }

  try {
    const organization: Organization | null =
      await organizationModule.FindOrganizationById(organizationId);

    if (organization) {
      response.send({ status: 1, data: organization });
    } else {
      response.send({ status: 0, error: 'Organization not found' });
    }
  } catch (error) {
    console.error('Error while finding organization', { message: error });
    response.send({
      status: 0,
      error: `Error while finding organization: ${error}`,
    });
  }
}

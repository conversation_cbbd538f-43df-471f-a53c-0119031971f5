import { ExtendedRequest } from '../../../types/extras';
import { masterScene } from '../../../types/masterScene';
import { MasterSceneModule } from '../../../modules/masterScene';
import { Response } from 'express';
import mongoose from 'mongoose';
import logger from '../../../config/logger';
export async function CreateCoordinate (
  request: ExtendedRequest,
  response: Response,
): Promise<masterScene | void> {
  const organization_id = request.organization_id as string;
  const scene = new MasterSceneModule(organization_id);
  const coordinate_id = new mongoose.Types.ObjectId();
  const coord = {
    _id: coordinate_id,
    lat: request.body.lat,
    lng: request.body.lng,
    name: request.body.name,
    linkType: request.body.linkType,
    ...(request.body.link && { link: request.body.link}),
    ...(request.body.scene_id && { scene_id: request.body.scene_id}),
    ...(request.body.project_id && { project_id: request.body.project_id}),
    active: request.body.active || false,
  };
  const targetSceneId = request.body.masterSceneId;
  await scene
    .createCoordinate(coord, targetSceneId)
    .then((sceneData) => {
      response.status(201).json({ status: 1, data: sceneData });
    })
    .catch((error: Error) => {
      logger.error('Error while creating coordinates', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while creating coordinates'+ error });
    });
}

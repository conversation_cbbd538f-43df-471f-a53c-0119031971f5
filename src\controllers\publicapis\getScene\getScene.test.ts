import app  from '../../../app';
import request from 'supertest';
describe('Get Scene Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/getScene/656d6bc71555c0e410e73f8e?organization_id=8X3plU')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('sceneData');
    expect(res.body).toHaveProperty('svgData');
    expect(res.body.sceneData).not.toEqual({});
    expect(res.body.svgData).not.toEqual({});
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getScene/656d6bc71555c0e410e73f8?organization_id=8X3plU')   // Invalid scene Id
      .send();
    expect(res.statusCode).toEqual(500);
    expect(res.body).toHaveProperty('error');
    expect(res.body.error).toEqual('scene not found');
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getScene/656d6bc71555c0e410e73f8e?organization_id=8X3pl')   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(500);
    expect(res.body).toHaveProperty('error');
    expect(res.body.error).toEqual('scene not found');
  });

});

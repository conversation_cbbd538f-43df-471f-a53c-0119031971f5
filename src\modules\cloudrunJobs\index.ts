import { google } from 'googleapis';
import { JWT } from 'google-auth-library';
import { EnvVar } from '../../types/projectSVG';

export class cloudRunJobModule{

  private jwtClient!: JWT;
  private projectId = process.env.FIREBASE_PROJECT_ID;
  private location = 'us-central1';
  constructor (){
    this.jwtClient = new JWT({
      email: process.env.FIREBASE_CLIENT_EMAIL,
      key: (process.env.FIREBASE_PRIVATE_KEY || '').replace(/\\n/g, '\n'),
      scopes: process.env.JWT_AUTH_URL,
    });

  }
  async runJobExecution (jobId: string, envVars: EnvVar[]):Promise<void> {
    // Create the Google API client

    const cloudRun = google.run({
      version: 'v2',
      auth: this.jwtClient,
    });

    // Construct the fully qualified job name
    const jobName = `projects/${this.projectId}/locations/${this.location}/jobs/${jobId}`;

    // Execute the job
    try {
      const res = await cloudRun.projects.locations.jobs.run({
        name: jobName,
        requestBody: {
          overrides: {
            containerOverrides: [
              {
                args: ['ARGS'],
                env: envVars,
              },
            ],
          },
        },
      });

      console.log('Job executed:', res.data.name);
    } catch (error) {
      console.error('Error executing job:', error);
    }
  }
}

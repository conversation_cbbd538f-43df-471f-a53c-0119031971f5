import { ExtendedRequest } from '../../../types/extras';
import { Response } from 'express';
import { PublicApis } from '../../../modules/publicapis';
import { UnknownObject, arrayToObject } from '../../../helpers/dataFormatHelper';
import logger from '../../../config/logger';

export async function getFilteredUnits (
  request:ExtendedRequest,
  response:Response,
):Promise<void>{
  const { project_id } = request.params;
  const query = { ...request.query };
  const unit = new PublicApis(project_id);

  const unitDataArray : Array<UnknownObject> | null = await unit.getFilteredUnits(project_id, query);
  let unitData;
  if (unitDataArray){
    unitData = await arrayToObject(unitDataArray);
  }
  if (unitData) {
    response.status(200).json({ status: 1, data: unitData });
  } else {
    logger.error('No Units found');
    response.status(404).json({ status: 0, error: 'No Units found' });
  }

}

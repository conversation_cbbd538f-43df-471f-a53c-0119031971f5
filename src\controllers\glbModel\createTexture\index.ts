import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { ModelModule } from '../../../modules/glbModel';
import { glbmodel } from '../../../types/glbModels';
import { Request, Response } from 'express';
import mongoose from 'mongoose';
export async function createTexture (
  request: Request,
  response: Response,
): Promise<glbmodel | void> {
  const project_id = request.body.project_id;
  const organization_id = request.headers.organization as string;
  const model = new ModelModule(project_id, organization_id);
  const requestFiles = request.files;
  const id = new mongoose.Types.ObjectId().toString();
  if (requestFiles) {
    UploadUnitplanFiles(requestFiles, model.storagepath+request.body.model_id+'/meshes/'+id)
      .then((urlObject: { [key: string]: string }) => {
        const createTextureData = {
          texture_thumbnail: urlObject.texture_thumbnail,
          actual_texture_thumbnail: urlObject.actual_texture_thumbnail,
          name: request.body.name,
          mesh_id: request.body.mesh_id,
          model_id: request.body.model_id,
          id: id,
        };
        model
          .createTexture(createTextureData)
          .then((texture) => {
            response.status(201).json({ status: 1, data: texture });
          })
          .catch((error: Error) => {
            logger.error('Error while creating texture', {message: error});
            response
              .status(500)
              .json({ status: 0, error: 'Error while creating texture'+ error });
          });
      }).catch((error) => {
        logger.error('Error uploading files', {message: error});
        response
          .status(500)
          .json({ status: 0, error: 'Error uploading files'+ error });
      });
  }
}

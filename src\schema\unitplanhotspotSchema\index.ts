import mongoose, {Types} from 'mongoose';
import { hotspotType, scaleType } from '../../types/unitplan';

export const unitplanhotspotSchema = new mongoose.Schema({
  _id: Types.ObjectId,
  text: String,
  x: Number,
  y: Number,
  scale: {
    type: String,
    enum: scaleType,
    default: scaleType.SMALL,
  },
  type: {
    type: String,
    enum: hotspotType,
    default: hotspotType.DEFAULT,
  },
  image_id: String,
  label_id: String,
  group_id: String,
  subGroup_id: String,
});

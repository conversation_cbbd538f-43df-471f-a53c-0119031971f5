import { Request, Response, NextFunction } from 'express';
import { validationResult, body, header } from 'express-validator';

const CheckAvailableSessionSlotsValidate = [
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('start_time', 'startTime is required').notEmpty(),
  body('end_time', 'endTime is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default CheckAvailableSessionSlotsValidate;

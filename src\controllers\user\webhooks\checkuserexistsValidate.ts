import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';

const checkuserexistsValidate = [
  body('email', 'Invalid email').isEmail().notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default checkuserexistsValidate;

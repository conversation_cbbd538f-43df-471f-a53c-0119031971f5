import mongoose from 'mongoose';
import { MediaType } from '../../types/amenity';
export const gallerySchema = new mongoose.Schema({
  _id: {
    immutable: true,
    type: String,
  },
  name: {
    immutable: false,
    type: String,
  },
  type: {
    immutable: false,
    type: String,
    enum: MediaType,
  },
  category: {
    immutable: false,
    type: String,
  },
  url: {
    immutable: false,
    type: String,
  },
  thumbnail: {
    immutable: false,
    type: String,
  },
  link: {
    immutable: false,
    type: String,
  },
  tour_id: {
    immutable: false,
    type: String,
  },
  order: {
    immutable: false,
    type: Number,
  },
  modified: { type: Date, required: true},
});

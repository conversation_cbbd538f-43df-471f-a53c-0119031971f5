import { Request, Response, NextFunction } from 'express';
import { validationResult, query, param } from 'express-validator';

const getFloorDetailsValidate = [
  param('organization_id', 'Organization ID is required').notEmpty(),
  param('project_id', 'Project ID is required').notEmpty(),
  query('building_id', 'Building ID is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default getFloorDetailsValidate;

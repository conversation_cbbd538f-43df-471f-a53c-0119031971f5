import { Request, Response } from 'express';
import { ProjectModule } from '../../../modules/projects';
import logger from '../../../config/logger';

export async function GetProject (
  request: Request,
  response: Response,
): Promise<void> {
  const  project_id  = request.params.project_id;
  const organization_id = request.params.organization_id as string;
  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'organization_id not found'});
    return;
  }
  //   Pass organization_id in this ProjectModule
  const project = new ProjectModule(organization_id);

  const projectData = await project.getProjectById(project_id);

  if (projectData) {
    response.status(200).json({ status: 1, data: projectData });
  } else {
    logger.error('Project not found');
    response.status(500).json({ status: 0, error: 'Project not found' });
  }
}

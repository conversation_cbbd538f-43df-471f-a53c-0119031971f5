import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { ModelModule } from '../../../modules/glbModel';
import { glbmodel } from '../../../types/glbModels';
import { Request, Response } from 'express';
export async function updateTexture (
  request: Request,
  response: Response,
): Promise<glbmodel | void> {
  const project_id = request.body.project_id;
  const organization_id = request.headers.organization as string;
  const model = new ModelModule(project_id, organization_id);
  const requestFiles = request.files;
  try {
    let updateTextureData = request.body;

    if (requestFiles) {
      const urlObject = await UploadUnitplanFiles(
        requestFiles,
        model.storagepath+request.body.model_id+'/meshes/'+request.body.texture_id);
      updateTextureData = {
        actual_texture_thumbnail: urlObject.actual_texture_thumbnail,
        texture_thumbnail: urlObject.texture_thumbnail,
        ...request.body,
      };
    }

    const texture = await model.updateTexture(updateTextureData);
    response.status(201).json({ status: 1, data: texture });
  } catch (error) {
    logger.error('Error while updating texture', {message: error});
    response.status(500).json({ status: 0, error: 'Error while updating texture' + error });
  }
}

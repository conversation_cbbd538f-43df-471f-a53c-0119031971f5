import mongoose from 'mongoose';
export const glbSchema = new mongoose.Schema({
  _id: {
    immutable: true,
    type: String,
  },
  name: {
    immutable: false,
    type: String,
  },
  description: {
    immutable: false,
    type: String,
  },
  thumbnail: {
    immutable: false,
    type: String,
  },
  url: {
    immutable: false,
    type: String,
  },
  meshes: {
    immutable: false,
    type: Object,
  },
  type: {
    immutable: false,
    type: String,
  },
  status: {
    immutable: false,
    type: String,
  },
  luma_id: {
    immutable: false,
    type: String,
  },
  progress: {
    immutable: false,
    type: Number,
  },
  download_url: {
    immutable: false,
    type: String,
  },
});

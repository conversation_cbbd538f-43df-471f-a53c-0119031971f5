import { mastersvgSchema } from '../../schema/mastersvgSchema';
import mongoose from 'mongoose';
import { masterSVG, transformedSVG, updatePayload } from '../../types/masterSVG';
import {storageUpload} from '../../helpers/storageUpload';
import fs from 'fs';
import logger from '../../config/logger';
import { Layers } from '../../types/projectSVG';
import { trashModule } from '../trash';
import { arrayToObject, UnknownObject } from '../../helpers/dataFormatHelper';
import { trashType } from '../../types/trash';
import { Models } from '../../types/extras';
interface layersObject {
  g?: string;
  x?: number;
  y?: number;
  width?: number;
  height?: number;
  layer_id?: string;
  placement?: string;
  reSize?: boolean;
  zIndex?: number;
  maxZoomLevel?: number;
  minZoomLevel?: number;
  type?: string;
}
export class MasterSVGModule {
  public model: mongoose.Model<masterSVG>;
  public storagepath : string;
  constructor (organization_id: string) {
    this.model = mongoose.model<masterSVG>(
      `${organization_id}${Models._MASTER_SVG}`,
      mastersvgSchema,
    );
    this.storagepath='CreationtoolAssets/'+organization_id+'/master_svgs/';
  }

  public async UploadFiles (
    filename: string,
    modifiedSvg: string | object,
    scene_id:string,
  ): Promise<string> {
    logger.info('UploadFiles Called', {filename: filename, modifiedSvg: modifiedSvg, timscene_ideStamp: scene_id});
    return new Promise((resolve, reject) => {
      fs.writeFile('../output.svg', modifiedSvg.toString(), async (err) => {
        if (err) {
          logger.error('Error before uploading', {message: err});
          reject(err);
        }
        const uploadOptions = {
          destination: this.storagepath+scene_id+'/'+ filename,
        };
        storageUpload(uploadOptions, '../output.svg').then((thumbnailUrl) => {
          logger.info('UploadFiles Successfull', {thumbnailUrl: thumbnailUrl});
          resolve(thumbnailUrl);
        })
          .catch((error) => {
            logger.error('Error in UploadFiles:', {message: err});
            reject(error);
          });
      });
    });
  }
  public async createSVG (payload: object): Promise<masterSVG | void> {
    logger.info('createSVGMongo Called', {payload: payload});
    return new Promise((resolve, reject) => {
      const svg = new this.model(payload);
      svg
        .save()
        .then((res) => {
          logger.info('createSVGMongo Successfull', {Response: res});
          resolve(res);
        })
        .catch((err: string) => {
          logger.error('Error in createSVGMongo:', {message: err});
          reject(err);
        });
    });
  }

  public async updateLayers (payload: { [key: string]: string | updatePayload })
  :Promise<object | null | string> {
    logger.info('updateLayers Called', {payload: payload});
    const query = (payload.query as updatePayload);
    const g = query.g
      ? await this.uploadGtagToFirebase(query as updatePayload, payload.layer_id as string)
      : undefined;
    const data =
      query.type === 'landmarks'
        ? {
          [`layers.${payload.layer_id}.landmark`]: {
            route_id: query.route_id,
            landmark_id: query.landmark_id,
          },
        }
        : query.type === 'project'
          ? {
            [`layers.${payload.layer_id}.project_id`]: query.project_id,
            [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
          }
          : query.type === 'image'
            ? {
              [`layers.${payload.layer_id}.image_id`]: query.image_id,
            }
            : query.type === 'scene' || query.type === 'pin'
              ? {
                [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
              }
              : query.type === 'amenity'
                ? {
                  [`layers.${payload.layer_id}.amenity_id`]: query.amenity_id,
                }
                : query.type === 'community'
                  ? {
                    [`layers.${payload.layer_id}.community_id`]: query.community_id,
                    [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
                  }
                  : query.type === 'tower' || query.type === 'building'
                    ? {
                      [`layers.${payload.layer_id}.building_id`]: query.building_id,
                      [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
                    }
                    : query.type === 'toweroverlay'
                      ? {
                        [`layers.${payload.layer_id}.floor_id`]: query.floor_id,
                        [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
                        [`layers.${payload.layer_id}.building_id`]: query.building_id,
                      }
                      : query.type === 'floor'
                        ? {
                          [`layers.${payload.layer_id}.floor_id`]: query.floor_id,
                          [`layers.${payload.layer_id}.building_id`]: query.building_id,
                          [`layers.${payload.layer_id}.scene_id`]: query.scene_id,
                        }
                        : query.type === 'units'
                          ? {
                            [`layers.${payload.layer_id}.units`]: query.units,
                          }
                          : query.type === 'label'
                            ? {
                              [`layers.${payload.layer_id}.title`]: query.title,
                              [`layers.${payload.layer_id}.category`]: query.category,
                            }
                            : query.type === 'amenitycategory'
                              ? {
                                [`layers.${payload.layer_id}.amenity_category`]: query.amenity_category,
                              }
                              : {};
    let scaleX, scaleY;
    if (typeof query.scale === 'object' && query.scale !== null) {
      scaleX = query.scale.x;
      scaleY = query.scale.y;
    }
    try {
      const updatedDocument = await this.model.findOneAndUpdate(
        {
          _id: payload.svg_id,
        },
        {
          $set: {
            ['viewbox']: payload.viewbox,
            [`layers.${payload.layer_id}.type`]: query.type,
            [`layers.${payload.layer_id}.rotation`]: payload.rotation,
            [`layers.${payload.layer_id}.position`]: payload.position,
            [`layers.${payload.layer_id}.scale.x`]: scaleX,
            [`layers.${payload.layer_id}.scale.y`]: scaleY,
            [`layers.${payload.layer_id}.g`]: g,
            [`layers.${payload.layer_id}.x`]: query.x,
            [`layers.${payload.layer_id}.y`]: query.y,
            [`layers.${payload.layer_id}.placement`]: query.placement,
            [`layers.${payload.layer_id}.reSize`]: query.reSize,
            [`layers.${payload.layer_id}.zIndex`]: query.zIndex,
            [`layers.${payload.layer_id}.maxZoomLevel`]: query.maxZoomLevel,
            [`layers.${payload.layer_id}.minZoomLevel`]: query.minZoomLevel,
            [`layers.${payload.layer_id}.name`]: query.name,
            [`layers.${payload.layer_id}.height`]: query.height,
            [`layers.${payload.layer_id}.width`]: query.width,
            ...data,
          },
        },
        {
          new: true,
        },
      );

      logger.info('updateLayers Successful', { updatedDocument: updatedDocument });
      return updatedDocument;
    } catch (err) {
      logger.error('Error in updateLayers', { response: err });
      return err as string;
    }

  }
  public async deleteMasterLayers (
    svg_id:string,
    layer_id: string,
    organization_id: string,
  ): Promise<object | void | null> {
    logger.info('deleteLayers Called',
      {svg_idz: svg_id, layer_id: layer_id, organization_id: organization_id});
    try {
      const svg = await this.model.findOne({_id: svg_id});
      if (!svg){
        throw new Error('Invalid SVG ID');
      }
      const updatedDocument = await this.model.findOneAndUpdate(
        {
          _id: svg_id,
          [`layers.${layer_id}`]: { $exists: true },
        },
        {
          $unset: {
            [`layers.${layer_id}`]: '',
          },
        },
        { new: true },
      );
      if (!updatedDocument){
        throw new Error('Invalid Layer ID');
      }
      console.log('uuuu', updatedDocument);
      logger.info('deleteLayers Successful', { updatedDocument: updatedDocument });
      return updatedDocument;
    } catch (err){
      logger.error('Error in deleteLayers', {message: err});
      throw new Error(`${err}`);
    }
  }
  public async getSvgById (scene_id: string): Promise<transformedSVG | null> {
    logger.info('getSvgById Called', {scene_id: scene_id});
    const query = {
      scene_id: scene_id,
    };
    const project = await this.model.find(query);
    logger.info('getSvgById Successfull', {project: project});
    return project.reduce((acc, svg) => {
      acc[svg._id] = svg;
      return acc;
    }, {} as transformedSVG);
  }
  public async uploadGtagToFirebaseSvg (
    layersData: updatePayload,
  ): Promise<updatePayload | layersObject > {
    try {
      logger.info('uploadGtagToFirebaseSvg Called', {layersData: layersData});

      const data: { [key: string]: layersObject } = {};

      for (const key in layersData) {
        if (Object.prototype.hasOwnProperty.call(layersData, key)) {
          const layersObject: layersObject = layersData[key] as layersObject;

          // Const storagePath = `/layers/${layersObject.layer_id}`;

          if (layersObject.g){
            const svgContent = layersObject.g.toString();
            const thumbnailUrl = await this.UploadFiles(
              'gTag.svg',
              svgContent,
                layersObject.layer_id as string,
            );
            layersObject.g = thumbnailUrl;
            console.log('linkkkkkkkkkkkkk', thumbnailUrl);
            data[key] = layersObject;
          }
        }
      }

      return data;
    } catch (error) {
      console.error('Error uploading to Firebase Storage:', error);
      throw error;
    }
  }
  public async validateLayers (payload: object): Promise<object> {
    const arrayOfLayers = Object.values(payload);
    arrayOfLayers.map((item, index) => {
    // Setting Default Values
      if (!Object.prototype.hasOwnProperty.call(item, 'minZoomLevel')) {
        Object.assign(item, { minZoomLevel: 0 });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'maxZoomLevel')) {
        Object.assign(item, { maxZoomLevel: 100 });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'reSize')) {
        console.log('reSize---------------------------------------');
        Object.assign(item, { reSize: true }, { placement: null });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'name')) {
        console.log('item', item);
        console.log('arrayofLayers', arrayOfLayers.length);
        Object.assign(item, { name: 'layer ' + (index + 1) });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'zIndex')) {
        Object.assign(item, { zIndex: 1 });
      }
      if (!Object.prototype.hasOwnProperty.call(item, 'scale')) {
        Object.assign(item, { scale: {x: 1, y: 1} });
      }
      return item;
    });

    const layersObject = arrayOfLayers.reduce((obj: object, item: Layers) => {
      const randomIdForLayers = item.layer_id;
      return Object.assign(obj, {
        [randomIdForLayers]: item,
      });
    }, {});

    return layersObject;
  }
  public async uploadGtagToFirebase (
    data: updatePayload,
    layer_id: string,
  ): Promise<string> {
    logger.info('uploadGtagToFirebase Called', {data: data, layer_id: layer_id});
    const storagePath = `layers/${layer_id}`;
    try {
      if (typeof data.g !== 'string') {
        throw new Error('Invalid data: g must be a string');
      }
      const svgContent = data.g.toString();
      const thumbnailUrl = await this.UploadFiles(
        'gTag.svg',
        svgContent,
        storagePath,
      );
      if (thumbnailUrl){
        logger.info('uploadGtagToFirebase Successfull', {thumbnailUrl: thumbnailUrl});
      }
      return thumbnailUrl as string ;
    } catch (error) {
      console.error('Error uploading to Firebase Storage:', error);
      throw error;
    }
  }
  public async createLayers (
    payload: { [key: string]: string | updatePayload },
    randomId: string,
    svgurl?: string,
  ): Promise<object | null> {
    return new Promise((resolve, reject) => {
      console.log('Eroooooooo', randomId);
      const query = payload.query
        ? JSON.parse(payload.query as string)
        : undefined;
      const rotation = payload.rotation
        ? JSON.parse(payload.rotation as string)
        : undefined;
      const position = payload.position
        ? JSON.parse(payload.position as string)
        : undefined;
      const scale = payload.scale
        ? JSON.parse(payload.scale as string)
        : undefined;
      const type = query ? query.type : undefined;
      let data;
      if (query) {
        data =
          query.type === 'landmark'
            ? {
              [`layers.${randomId}.landmark`]: {
                route_id: query.route_id,
                landmark_id: query.landmark_id,
              },
            }
            : query.type === 'project'
              ? {
                [`layers.${randomId}.project_id`]: query.project_id,
              }
              : query.type === 'image'
                ? {
                  [`layers.${randomId}.image_id`]: query.image_id,
                }
                : query.type === 'scene' || query.type === 'pin'
                  ? {
                    [`layers.${randomId}.scene_id`]: query.scene_id,
                  }
                  : query.type === 'amenity'
                    ? {
                      [`layers.${randomId}.amenity_id`]: query.amenity_id,
                    }
                    : query.type === 'community'
                      ? {
                        [`layers.${randomId}.community_id`]: query.community_id,
                        [`layers.${randomId}.scene_id`]: query.scene_id,
                      }
                      : query.type === 'tower' || query.type === 'building'
                        ? {
                          [`layers.${randomId}.building_id`]: query.building_id,
                          [`layers.${randomId}.scene_id`]: query.scene_id,
                        }
                        : query.type === 'toweroverlay'
                          ? {
                            [`layers.${randomId}.floor_id`]: query.floor_id,
                            [`layers.${randomId}.scene_id`]: query.scene_id,
                            [`layers.${randomId}.building_id`]: query.building_id,
                          }
                          : query.type === 'floor'
                            ? {
                              [`layers.${randomId}.floor_id`]: query.floor_id,
                              [`layers.${randomId}.building_id`]: query.building_id,
                              [`layers.${randomId}.scene_id`]: query.scene_id,
                            }
                            : query.type === 'units'
                              ? {
                                [`layers.${randomId}.units`]: query.units,
                              }
                              : query.type === 'label'
                                ? {
                                  [`layers.${randomId}.title`]: query.title,
                                  [`layers.${randomId}.category`]: query.category,
                                }
                                : query.type === 'amenitycategory'
                                  ? {
                                    [`layers.${payload.layer_id}.amenity_category`]:
                  query.amenity_category,
                                  }
                                  : {};
      }
      this.model
        .findOneAndUpdate(
          {
            _id: payload.svg_id,
          },
          {
            $set: {
              [`layers.${randomId}.type`]: type,
              [`layers.${randomId}.name`]: payload.name,
              [`layers.${randomId}.rotation`]: rotation,
              [`layers.${randomId}.position`]: position,
              [`layers.${randomId}.scale`]: scale,
              [`layers.${randomId}.layer_id`]: randomId,
              [`layers.${randomId}.svg_url`]: svgurl,
              ...data,
            },
          },
          {
            new: true,
          },
        ).then((updatedDocument) => {
          logger.info('createLayers successfull', {updatedDocument: updatedDocument});
          resolve(updatedDocument);
        })
        .catch((err) => {
          logger.error('Error in createLayers', {message: err});
          reject(err);
        });
    });
  }
  public async moveToTrash (
    svgIds: Array<string>,
    organization_id: string,
    timeStamp: number,
  ): Promise<masterSVG | void> {
    logger.info('moveToTrash Successfull',
      {svgIds: svgIds, organization_id: organization_id, timeStamp: timeStamp});
    const trash = new trashModule(organization_id);
    const documents: Array<UnknownObject> = await this.model.find({
      _id: { $in: svgIds.map((id) => new mongoose.Types.ObjectId(id)) },
    });
    const documentsObj = arrayToObject(documents) as Record<string, trashType>;
    if (documents.length === 0) {
      logger.error('SVGs corresponding to SVG IDs provided not found');
      throw 'SVGs corresponding to SVG IDs provided not found';
    }
    const SVGdataToInsertObj: trashType = {
      _id: new mongoose.Types.ObjectId(),
      type: `${organization_id.toLowerCase()}${Models._MASTER_SVG}`,
      timeStamp: timeStamp,
      data: documentsObj,
      linked_trashes: [],
      root: true,
    };
    await Promise.all([
      trash.addtoTrash(SVGdataToInsertObj),
    ])
      .then(async (res) => {
        console.log('**********Moved to Trash*********', res);
        await Promise.all([
          this.model.deleteMany({
            _id: { $in: svgIds.map((id) => new mongoose.Types.ObjectId(id)) },
          }),
        ])
          .then(() => {
            logger.info('moveToTrash Successfull');
            return 'Data moved to trash';
          })
          .catch((err) => {
            logger.error('Error:', {message: err});
            throw err;
          });
      })
      .catch((err) => {
        logger.error('Error in moveToTrash', {message: err});
        throw err;
      });
  }
  public async restoreSVG (
    organization_id: string,
    trash_Id: string,
  ): Promise<void> {
    logger.info('restoreSVG Called',
      {organization_id: organization_id, trash_Id: trash_Id});
    const trash = new trashModule(organization_id);
    const trash_ids:Array<string> =[];
    trash_ids.push(trash_Id);
    const restoredData = await trash.restoreData(trash_Id);

    if (restoredData) {
      const createSVGPromise = Object.values(restoredData.data).map(async (item) => {
        await this.createSVG(item);
      });

      await Promise.all([
        createSVGPromise,
      ]).then(async () => {
        await trash.deleteTrash(trash_ids);
        logger.info('restoreSVG is Successfull');
        return 'SVG got restored';
      });
    } else {
      logger.error('Error in restoreSVG');
      throw new Error('Failed to restore svg data from trash');
    }
  }
  public async uploadVideoFiles (
    filename: string,
    videoPath: string,
    scene_id: string,
  ):Promise<string>{
    return new Promise((resolve, reject) => {
      const uploadOptions = {
        destination: this.storagepath + scene_id + '/' + filename,
      };
      storageUpload(uploadOptions, videoPath).then((thumbnailUrl) => {
        logger.info('UploadFiles Successfull', {thumbnailUrl: thumbnailUrl});
        resolve(thumbnailUrl);
      })
        .catch((error) => {
          logger.error('Error in storageUpload', {error: error});
          reject(error as string);
        });
    });
  }
  public async updateLayersVideo (
    payload: { [key: string]: string | updatePayload },
    videoTag : string | null,
  ): Promise<object | null | string>{
    logger.info('updateLayersVideo Called', { payload: payload });
    try {
      const updatedDocument = await this.model.findOneAndUpdate(
        {
          _id: payload.svg_id,
        },
        {
          $set: {
            [`layers.${payload.layer_id}.video_tag`]: videoTag,
          },
        },
        {
          new: true,
        },
      );

      logger.info('updateLayersVideo Successful', { updatedDocument: updatedDocument });
      return updatedDocument;
    } catch (err) {
      logger.error('Error in updateLayersVideo', { response: err });
      return err as string;
    }
  }
}

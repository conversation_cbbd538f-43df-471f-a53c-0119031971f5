import { BetaAnalyticsDataClient } from '@google-analytics/data';
import { ReportParams, ReportRow, EventDetailsParams, EventDetailsRow } from '../../types/analyticsModel';

export class AnalyticsModule {
  private analyticsDataClient: BetaAnalyticsDataClient;
  private propertyId: string;

  constructor () {
    this.analyticsDataClient = new BetaAnalyticsDataClient({
      credentials: {
        client_email: process.env.FIREBASE_CLIENT_EMAIL,
        private_key: (process.env.FIREBASE_PRIVATE_KEY || '').replace(/\\n/g, '\n'),
      },
    });
    this.propertyId = process.env.FIREBASE_PROPERTY_ID || '';
  }

  public async runReport ({ startDate, endDate, orgId, projectId }: ReportParams): Promise<ReportRow[]> {
    try {
      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: [
          { name: 'country' },
          { name: 'region' },
          { name: 'city' },
          { name: 'browser' },
          { name: 'operatingSystem' },
          { name: 'deviceCategory' },
          { name: 'sessionSource' },
          { name: 'eventName' },
        ],
        metrics: [
          { name: 'activeUsers' },
          { name: 'sessions' },
          { name: 'totalUsers' },
          { name: 'newUsers' },
          { name: 'engagementRate' },
          { name: 'userEngagementDuration' },
          { name: 'screenPageViews' },
          { name: 'eventCount' },
        ],
        dimensionFilter: {
          filter: {
            fieldName: 'fullPageUrl',
            stringFilter: {
              matchType: 'CONTAINS',
              value: `/${orgId}/projectscene/${projectId}`,
            },
          },
        },
      });

      if (!response.rows) {
        return [];
      }

      return response.rows.map((row): ReportRow => {
        const country = row.dimensionValues?.[0]?.value || 'Unknown';
        const region = row.dimensionValues?.[1]?.value || 'Unknown';
        const city = row.dimensionValues?.[2]?.value || 'Unknown';
        const browser = row.dimensionValues?.[3]?.value || 'Unknown';
        const operatingSystem = row.dimensionValues?.[4]?.value || 'Unknown';
        const deviceCategory = row.dimensionValues?.[5]?.value || 'Unknown';
        const sessionSource = row.dimensionValues?.[6]?.value || 'Unknown';
        const eventName = row.dimensionValues?.[7]?.value || 'Unknown';

        const activeUsers = Number(row.metricValues?.[0]?.value || '0');
        const sessions = Number(row.metricValues?.[1]?.value || '0');
        const totalUsers = Number(row.metricValues?.[2]?.value || '0');
        const newUsers = Number(row.metricValues?.[3]?.value || '0');
        const engagementRate = Number(row.metricValues?.[4]?.value || '0');
        const userEngagementDuration = Number(row.metricValues?.[5]?.value || '0');
        const screenPageViews = Number(row.metricValues?.[6]?.value || '0');
        const eventCount = Number(row.metricValues?.[7]?.value || '0');

        const newUserPercentage = ((newUsers / totalUsers) * 100).toFixed(2);
        const pageviewsPerUser = (screenPageViews / totalUsers).toFixed(2);

        return {
          country,
          region,
          city,
          browser,
          operatingSystem,
          deviceCategory,
          sessionSource,
          eventName,
          activeUsers,
          sessions,
          totalUsers,
          newUsers,
          engagementRate,
          userEngagementDuration,
          screenPageViews,
          eventCount,
          newUserPercentage,
          pageviewsPerUser,
        };
      });
    } catch (error) {
      console.error('Error running report:', error);
      throw error;
    }
  }

  public async fetchEventDetails ({
    startDate,
    endDate,
    orgId,
    projectId,
    eventName,
    customFields,
  }: EventDetailsParams): Promise<EventDetailsRow[]> {
    try {
      const dimensions = [
        { name: 'eventName' },
        ...customFields.map((field) => ({ name: `customEvent:${field}` })),
      ];

      const [response] = await this.analyticsDataClient.runReport({
        property: `properties/${this.propertyId}`,
        dateRanges: [{ startDate, endDate }],
        dimensions: dimensions,
        metrics: [{ name: 'eventCount' }],
        dimensionFilter: {
          andGroup: {
            expressions: [
              {
                filter: {
                  fieldName: 'eventName',
                  stringFilter: {
                    matchType: 'EXACT',
                    value: eventName,
                  },
                },
              },
              {
                filter: {
                  fieldName: 'fullPageUrl',
                  stringFilter: {
                    matchType: 'CONTAINS',
                    value: `/${orgId}/projectscene/${projectId}`,
                  },
                },
              },
            ],
          },
        },
      });

      if (!response.rows) {
        return [];
      }

      return response.rows.map((row): EventDetailsRow => {
        const result: EventDetailsRow = {
          eventName: row.dimensionValues?.[0]?.value || 'Unknown',
          eventCount: row.metricValues?.[0]?.value || '0',
        };

        customFields.forEach((field, index) => {
          result[field] = row.dimensionValues?.[index + 1]?.value || '';
        });

        return result;
      });
    } catch (error) {
      console.error('Error fetching event details:', error);
      throw error;
    }
  }
}

import { Request, Response, NextFunction } from 'express';
import { validationResult, header } from 'express-validator';
const allowedFields = [
  'layer_id',
  'g',
  'x',
  'y',
  'width',
  'height',
  'building_id',
  'scene_id',
  'type',
  'image_id',
  'amenity_id',
  'amenity_category',
  'community_id',
  'floor_id',
  'units',
  'title',
  'category',
  'placement',
  'reSize',
  'zIndex',
  'minZoomLevel',
  'maxZoomLevel',
  'name',
  'rotation',
  'position',
  'scale',
  'svg_url',
  'landmark',
  'landmark_id',
  'project_id',
  'route_id',
];
const updateLayerValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    const requiredTextFields = ['query', 'svg_id', 'layer_id'];

    const missingTextFields = requiredTextFields.filter(
      (field) => !(field in req.body),
    );

    if (missingTextFields.length > 0) {
      res.status(400).json({
        error: `Missing text fields: ${missingTextFields.join(', ')}`,
      });
    } else {
      const query = req.body.query;
      const invalidFields = Object.keys(query).filter(
        (field) => !allowedFields.includes(field),
      );

      if (invalidFields.length > 0) {
        res.status(400).json({
          error: `Invalid fields in query: ${invalidFields.join(', ')}`,
        });
      }
      next();
    }
  },
];

export default updateLayerValidate;

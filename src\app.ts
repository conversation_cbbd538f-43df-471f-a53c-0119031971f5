import express from 'express';
import cors from 'cors';
import orgRoutes from './routes/orgRoutes';
import userRoutes from './routes/userRoutes';
import projectRoutes from './routes/projectRoutes';
import { connectToDatabase } from './mongoose';
import unitplanRoutes from './routes/unitplanRoutes';
import masterSceneRoutes from './routes/masterSceneRoutes';
import masterSVGRoutes from './routes/masterSVGRoutes';
import masterLandmarkRoutes from './routes/masterLandmarkRoutes';
import ScaleSetRoutes from './routes/pixelStreamingRoutes';
import projectSceneRoutes from './routes/projectSceneRoutes';
import projectSVGRoutes from './routes/projectSVGRoutes';
import projectLandmarkRoutes from './routes/projectLandmarkRoutes';
import unitRoutes from './routes/unitRoutes';
import buildingRoutes from './routes/buildingRoutes';
import sessionRoutes from './routes/sessionRoutes';
import leadRoutes from './routes/leadsRoutes';
import invitationRoutes from './routes/invitationRoutes';
import breadCrumbRoutes from './routes/breadCrumbRoutes';
import virtualTourRoutes from './routes/virtualTourRoutes';
import amenityRoutes from './routes/amenityRoutes';
import miniMapRoutes from './routes/miniMapRoutes';
import communityRoutes from './routes/communityRoutes';
import publicApiRoutes from './routes/publicapisRoutes';
import webhooks from './routes/webhooks';
import jwtRoutes from './routes/jwtRoutes';
import customTourRoutes from './routes/customTourRoutes';
import { restResponseTimeHistogram } from './utilis/metrics';
import responseTime from 'response-time';
import { ExtendedRequest } from './types/extras';
import metricsRouter from './utilis/metrics';
import { PrometheusRequestTimer } from './middlewares/Prometheus';
import sidebarRoutes from './routes/sidebarRoutes';
import galleryRouter from './routes/galleryRoutes';
import shortUrlRouter from './routes/shortUrlRoutes';
import glbModelRouter from './routes/glbModelRoutes';
import storageRouter from './routes/storageRoutes';
import mailerRoutes from './routes/mailerRoutes';
import loggerMiddleware from './middlewares/LoggerMiddleware';
import analyticsRoutes from './routes/analyticsRoutes';
import trashRoutes from './routes/trashRoutes';
// Import { encryptResponseMiddleware } from './middlewares/encryptMiddleware';
import iconLibraryRoutes from './routes/iconLibraryRoutes';
import { v4 as uuidv4 } from 'uuid';
import asyncLocalStorage from './config/asyncLocalStorage';
import logger from './config/logger';
import translationRoutes from './routes/translationRoutes';
import notificationRoutes from './routes/notification';
import larkxrRoutes from './routes/larkxrRoutes';
import assetRoutes from './routes/assetsRoutes';
import kafdRoutes from './routes/kafdRoutes';
const port = process.env.PORT || 3000;
const app = express();

// Const storage = multer.memoryStorage();
// Const upload = multer({
//   Storage: storage,
// });

// Cors
app.use((cors as (options: cors.CorsOptions) => express.RequestHandler)({}));

// Middleware
app.use(express.json());
// App.use(upload.fields([{ name: 'name' }, { name: 'project_id' }]));
// App.use(express.urlencoded({ extended: true }));
app.use(express.urlencoded({ extended: true }));

app.use((req, res, next) => {
  const randomId = uuidv4();
  const apiEndpoint = req.path.split('/')[1] ? req.path.split('/')[1].toUpperCase() : 'NoEndpoint';
  const controllerEndpoint = req.path.split('/')[2] ? req.path.split('/')[2].toUpperCase() : 'NoControllerEndpoint';
  // Store the request ID in AsyncLocalStorage
  asyncLocalStorage.run(new Map<string, string>(), () => {
    const store = asyncLocalStorage.getStore();
    store?.set('requestId', `${randomId}`);
    store?.set('apiEndpoint', `/${apiEndpoint}`);
    store?.set('controllerEndpoint', `/${controllerEndpoint}`);
    next();
  });
});

// Prometheus Metrics middleware
app.use(
  responseTime((req: ExtendedRequest, res, time) => {
    if (req?.route?.path) {
      restResponseTimeHistogram.observe(
        {
          method: req.method,
          route: req.route.path,
          status_code: res.statusCode,
        },
        time / 1000,
      );
    }
  }),
);

app.use(PrometheusRequestTimer);
// Console.log('Winston Environemnt', process.env.WINSTON_ENV);
if (process.env.WINSTON_ENV === 'production'){
  console.log = (...messages: string[]) => {
    const formattedMessages = messages.map((msg) =>
      (typeof msg === 'object' ? JSON.stringify(msg) : msg),
    ).join(' ');
    logger.info(formattedMessages);
  };

  console.error = (...messages: string[]) => {
    const formattedMessage = messages.map((msg) =>
      (typeof msg === 'object' ? JSON.stringify(msg) : msg),
    ).join(' ');
    logger.error(formattedMessage);
  };

  app.use(loggerMiddleware);
}
app.use('/org', orgRoutes);
app.use('/user', userRoutes);
app.use('/project', projectRoutes);
app.use('/masterScene', masterSceneRoutes);
app.use('/masterSVG', masterSVGRoutes);
app.use('/masterLandmark', masterLandmarkRoutes);
app.use('/scaleSets', ScaleSetRoutes);
app.use('/projectScene', projectSceneRoutes);
app.use('/projectSVG', projectSVGRoutes);
app.use('/projectLandmark', projectLandmarkRoutes);
app.use('/unitplan', unitplanRoutes);
app.use('/unit', unitRoutes);
app.use('/building', buildingRoutes);
app.use('/session', sessionRoutes);
app.use('/lead', leadRoutes);
app.use('/invitations', invitationRoutes);
app.use('/breadCrumbs', breadCrumbRoutes);
app.use('/virtualTour', virtualTourRoutes);
app.use('/amenity', amenityRoutes);
app.use('/minimap', miniMapRoutes);
app.use('/community', communityRoutes);
app.use('/publicapis', (req, res, next) => {
  // Set Cache-Control header for this route
  res.set('Cache-Control', 'public, max-age=600, s-maxage=1800'); // Adjust max-age and s-maxage as needed
  next();
}, publicApiRoutes);
app.use('/webhooks', webhooks);
app.use('/jwt', jwtRoutes);
app.use('/customTour', customTourRoutes);
app.use('/grafana', metricsRouter);
app.use('/sidebar', sidebarRoutes);
app.use('/gallery', galleryRouter);
app.use('/tiny', shortUrlRouter);
app.use('/model', glbModelRouter);
app.use('/storage', storageRouter);
app.use('/mailer', mailerRoutes);
app.use('/analytics', analyticsRoutes);
app.use('/trash', trashRoutes);
app.use('/icon', iconLibraryRoutes);
app.use('/translation', translationRoutes);
app.use('/assets', assetRoutes);
app.use('/notification', notificationRoutes);
app.use('/larkxr', larkxrRoutes);
app.use('/kafd', kafdRoutes);
if (!process.env.MONGO_CONNECTION_STRING) {
  throw new Error('Mongo Connection string is not found');
}
connectToDatabase(process.env.MONGO_CONNECTION_STRING)
  .then(() => {
    console.log('MongoDB successfully');
    app.listen(port, () => {
      console.log(`Server running on port ${port}`);
    });
  })
  .catch((error) => {
    console.error('MongoDB error: ', error);
  });

export default app;

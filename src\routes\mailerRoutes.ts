import express from 'express';
import sendReminder from '../controllers/mailer/sendReminder';
import notifyCancellation from '../controllers/mailer/notifyCancellation';
import sendInviteHost from '../controllers/mailer/sendHostInvite';
import sendInviteGuest from '../controllers/mailer/sendGuestInvite';
const router = express.Router();
router.post('/sendHostInvite',
  sendInviteHost);
router.post('/sendGuestInvite',
  sendInviteGuest);
router.post('/sendReminder',
  sendReminder);
router.post('/notifyCancellation',
  notifyCancellation);
export default router;

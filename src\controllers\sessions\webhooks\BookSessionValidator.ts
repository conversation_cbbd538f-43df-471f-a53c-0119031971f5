import { Request, Response, NextFunction } from 'express';
import { validationResult, body } from 'express-validator';
import { SessionSource, SessionType } from '../../../types/session';

const BookSessionValidate = [
  body('project_id', 'Project ID  is required').notEmpty(),
  body('source', 'Source is required').notEmpty(),
  body('source', 'Invalid session source. Please ensure that you are using a valid session source value')
    .isIn(Object.values(SessionSource)),
  body('is_scheduled', 'Is Scheduled parameter is required').isBoolean(),
  body('type', 'type is required').notEmpty(),
  body('type', 'Invalid session type value. Please ensure that you are using a valid type value')
    .isIn(Object.values(SessionType)),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default BookSessionValidate;

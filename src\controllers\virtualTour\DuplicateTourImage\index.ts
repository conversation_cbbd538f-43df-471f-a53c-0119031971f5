import { Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function DuplicateTourImage (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const { project_id, tour_id, image_id, targetorder } = request.body;

  try {
    const virtualTourModule = new VirtualTourModule(organization_id, project_id);

    const duplicateTour = await virtualTourModule.DuplicateTourImage(tour_id, image_id, targetorder);

    if (!duplicateTour) {
      response.status(404).json({ status: 0, error: 'Duplication Error' });
    } else {
      response.status(200).json({ status: 1, message: 'Duplication created successfully', data: duplicateTour });
    }
  } catch (error) {
    logger.error('Error in duplicateTour controller', { message: error });
    response.status(500).json({ status: 0, error: `Error duplicate tour: ${error}` });
  }
}

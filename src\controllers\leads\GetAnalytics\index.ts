import { ExtendedRequest } from '../../../types/extras';
import { LeadsModule } from '../../../modules/leads';
import { Response } from 'express';
import logger from '../../../config/logger';

export async function GetAnalytics (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const organization_id = request.organization_id as string;
  const leads = new LeadsModule(organization_id);
  const query = { ...request.query };
  const leadAnalyticsData = await leads.getAnalytics(query, organization_id);

  if (leadAnalyticsData) {
    response.status(200).json({ status: 1, data: leadAnalyticsData });
  } else {
    logger.error('No Analytics found ');
    response.status(404).json({ status: 0, error: 'No Analytics found' });
  }
}

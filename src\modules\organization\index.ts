import {
  CreateOrganizationInput,
  Organization,
  Role,
  UpdateOrganizationInput,
  User,
} from '../../types/organization';
import { generateOrganizationId } from '../../helpers/organization';
import { getUserByEmail } from '../../helpers/authUser';
import { assignRoleObj } from '../../types/user';
import { getUser } from '../../helpers/authUser';
import { organizationSchema } from '../../schema/organizationsSchema';
import { userSchema } from '../../schema/userSchema';
import mongoose from 'mongoose';
import { UserModule } from '../user';
import { sendMessageToChannel } from '../../helpers/slackMessenger';
import logger from '../../config/logger';
// Import { admin } from '../../config/firebase';
const OrganizationModel = mongoose.model('Organization', organizationSchema);
const UserModel = mongoose.model('User', userSchema);
export class OrganizationModule {
  public async GenerateOrganizationId (): Promise<string> {
    logger.info('GenerateOrganizationId Called');
    const organizationId = await generateOrganizationId();
    const isExists = await this.IsOrganizationIdExists(organizationId);
    if (isExists) {
      // If the ID already exists, recursively call the function again
      return this.GenerateOrganizationId();
    }
    logger.info('GenerateOrganizationId Successfull');
    return organizationId;
  }

  public async CreateOrganization (
    new_org: CreateOrganizationInput,
  ): Promise<Organization> {
    logger.info('CreateOrganization Called');
    const {
      name,
      founding_date,
      contact_email,
      phone_number,
      address,
      website,
      max_users,
      organizationId,
      thumbnail,
      unique_org_id,
      theme,
      primary,
      secondary,
      primary_text,
      secondary_text,
      font_type,
      font_url,
      measurement_id,
      baseCurrency,
    } = new_org;

    const organization: Organization = {
      _id: organizationId,
      name,
      founding_date,
      contact_email,
      phone_number,
      address,
      website,
      max_users,
      thumbnail,
      theme,
      primary,
      secondary,
      primary_text,
      secondary_text,
      font_type,
      font_url,
      roles: [],
      unique_org_id,
      measurement_id,
      baseCurrency,
    };

    const orga = new OrganizationModel(organization);
    const org = await orga.save();
    return org as Organization;
  }

  public async IsOrganizationIdExists (
    organizationId: string,
  ): Promise<boolean> {
    logger.info('IsOrganizationIdExists Called');
    const organization = await OrganizationModel.findById(organizationId);
    return organization !== null && organization !== undefined;
  }

  public async GetOrganization (organizationId: string): Promise<Organization> {
    logger.info('GetOrganization Called');
    const org = await OrganizationModel.findById(organizationId);
    if (org) {
      logger.info('GetOrganization Successfull');
      return org as Organization;
    }
    logger.error(`Organization with ID ${organizationId} not found`);
    throw new Error(`Organization with ID ${organizationId} not found`);
  }
  public async FindOrganizationById (
    organizationId: string,
  ): Promise<Organization | null> {
    console.info('FindOrganizationById Called');
    try {
      const organization = await OrganizationModel.findById(organizationId);
      if (!organization) {
        console.warn(`Organization with ID ${organizationId} not found`);
        return null;
      }
      console.info('FindOrganizationById Successful');
      return organization as Organization;
    } catch (error) {
      console.error(`Error in FindOrganizationById: ${error}`);
      throw new Error('An error occurred while finding the organization');
    }
  }
  public async FindOrganizationByunique_org_id (
    unique_org_id: string,
  ): Promise<Organization | null> {
    console.info('FindOrganizationByunique_org_id Called', unique_org_id);
    try {
      const organization = await OrganizationModel.aggregate([
        {
          $match: {
            unique_org_id: unique_org_id,
          },
        },
        {
          $project: {
            roles: 0,
          },
        },
      ]);

      if (!organization) {
        console.warn(
          `Organization with unique_org_id ${unique_org_id} not found`,
        );
        return null;
      }
      console.info('FindOrganizationByunique_org_id Successful');
      return organization[0] as Organization;
    } catch (error) {
      console.error(`Error in FindOrganizationByunique_org_id: ${error}`);
      throw new Error('An error occurred while finding the organization');
    }
  }

  public async UpdateOrganization (
    organizationId: string,
    updatedData: UpdateOrganizationInput,
  ): Promise<Organization> {
    logger.info('UpdateOrganization Called');
    try {
      // Update object which stores data in <key,value> pairs
      const update: { $set: Record<string, string> } = { $set: {} };

      // Dynamic updatation of Tour irrespective of the fields
      for (const [key, value] of Object.entries(updatedData)) {
        update.$set[key] = value as string;
      }
      const updatedOrganization = await OrganizationModel.findOneAndUpdate(
        { _id: organizationId },
        update,
        { new: true },
      );
      if (updatedOrganization) {
        logger.info('UpdateOrganization Successfull');
        return updatedOrganization as Organization;
      }
      logger.error('Organization missing after update');
      throw new Error('Organization missing after update');
    } catch (error) {
      logger.error('Error updating organization', { message: error });
      throw new Error('Error updating organization');
    }
  }

  public async Isunique_org_idExists (unique_org_id: string): Promise<boolean> {
    try {
      logger.info('Isunique_org_idExists Called for:', unique_org_id);

      const user = await OrganizationModel.findOne({
        unique_org_id: {
          $exists: true,
          $regex: `^${unique_org_id}$`,
          $options: 'i',
        },
      });

      const exists = !!user;
      logger.info(
        exists
          ? `unique_org_id "${unique_org_id}" exists`
          : `unique_org_id "${unique_org_id}" does not exist`,
      );
      console.log('Query Result:', user, exists);
      return exists;
    } catch (error) {
      logger.error('Error checking unique_org_id', { message: error });
      throw new Error('Database error while checking unique_org_id');
    }
  }

  public async AddUserToOrganization (
    organizationId: string,
    user: User,
  ): Promise<object | null> {
    logger.info('AddUserToOrganization Called');
    try {
      const IsInOrganization = await this.checkAndCreateUser(user.user_id);
      console.log(user.user_id, IsInOrganization);
      if (
        IsInOrganization === null ||
        IsInOrganization.organization_id === undefined ||
        !IsInOrganization.organization_id.includes(organizationId)
      ) {
        await OrganizationModel.updateOne(
          { _id: organizationId },
          { $push: { roles: user } },
        );
        const updatedUser = await UserModel.findOneAndUpdate(
          { _id: user.user_id },
          {
            $push: { organization_id: organizationId },
          },
          {
            new: true,
          },
        );
        logger.info('AddUserToOrganization Successfull');
        return updatedUser;
      }
      if (
        IsInOrganization &&
        IsInOrganization !== null &&
        IsInOrganization.organization_id !== undefined &&
        IsInOrganization.organization_id.includes(organizationId)
      ) {
        logger.error('User already in this organisation');
        throw new Error('User already in this organisation');
      }
      return null;
    } catch (error) {
      logger.error('Error adding user to organization', { message: error });
      throw new Error('Error adding user to organization' + error);
    }
  }

  public async RemoveUserFromOrganization (
    organizationId: string,
    email: string,
  ): Promise<boolean> {
    logger.info('RemoveUserFromOrganization Called');
    try {
      const userRecords = await getUserByEmail(email);
      if (!userRecords) {
        logger.error('User Record not found');
        throw new Error('User Record not found');
      }

      const IsInOrganization = await this.checkAndCreateUser(userRecords.uid);
      const webhookUrl = process.env.SLACK_WEBHOOKURL as string;
      sendMessageToChannel(
        webhookUrl,
        'removed user',
        JSON.stringify(IsInOrganization),
      );
      if (
        IsInOrganization !== null &&
        IsInOrganization.organization_id !== undefined &&
        IsInOrganization.organization_id.includes(organizationId)
      ) {
        await UserModel.updateOne(
          { _id: userRecords.uid },
          { $pull: { organization_id: organizationId } },
        );
        await OrganizationModel.updateOne(
          { _id: organizationId },
          { $pull: { roles: { user_id: userRecords.uid } } },
        );

        // Console.log('User removed from organization successfully');
        logger.info('RemoveUserFromOrganization Successfull');
        return true;
      }
      logger.error('User not in this organization');
      throw new Error('User not in this organization');
    } catch (error) {
      logger.error('Error removing user from organization:', {
        message: error,
      });
      throw new Error('Error removing user from organization:' + error);
    }
  }
  public async CountUsersWithRole (organizationId: string): Promise<number> {
    logger.info('CountUsersWithRole Called');
    try {
      const count = await OrganizationModel.aggregate([
        {
          $match: {
            _id: organizationId,
          },
        },
        {
          $project: {
            adminCount: {
              $size: {
                $filter: {
                  input: '$roles',
                  as: 'role',
                  cond: { $eq: ['$$role.role', 'admin'] },
                },
              },
            },
          },
        },
      ]);

      const adminCount = count.length > 0 ? count[0].adminCount : 0;
      logger.info('CountUsersWithRole Successfull', { adminCount: adminCount });
      return adminCount;
    } catch (error) {
      logger.error('Error counting users with role:', { message: error });
      throw new Error('Error counting users with role: ' + error);
    }
  }

  public async AssignRoleToUser (userRoleObj: assignRoleObj): Promise<boolean> {
    logger.info('AssignRoleToUser Called');
    try {
      const previousRole = await this.GetUserRole(
        userRoleObj.user_id,
        userRoleObj.organizationId,
      );
      const adminUsersCount = await this.CountUsersWithRole(
        userRoleObj.organizationId,
      );

      if (previousRole && previousRole.role === userRoleObj.roleId) {
        logger.error('User already has the requested access');
        throw new Error('User already has the requested access');
      }

      if (previousRole?.role === 'admin' && adminUsersCount === 1) {
        logger.error(
          'At least one admin should be present in the organization',
        );
        throw new Error(
          'At least one admin should be present in the organization',
        );
      }
      this.checkAndCreateUser(userRoleObj.user_id).then((IsInOrganization) => {
        if (
          !IsInOrganization ||
          IsInOrganization.organization_id === undefined
        ) {
          logger.error('User is not associated with this organization');
          throw new Error('User is not associated with this organization');
          return false;
        }
        if (
          !IsInOrganization.organization_id.includes(userRoleObj.organizationId)
        ) {
          logger.error('User is not associated with this organization');
          throw new Error('User is not associated with this organization');
          return false;
        }

        const time = new Date().toISOString();
        const role: Role = {
          role: userRoleObj.roleId,
          user_id: userRoleObj.user_id,
          created_time: time,
          email: userRoleObj.email,
        };
        OrganizationModel.updateOne(
          {
            _id: userRoleObj.organizationId,
            'roles.user_id': userRoleObj.user_id,
          },
          // Match organization and user_id in roles array
          { $set: { 'roles.$': role } }, // Set the entire matched object in the roles array
        ).then(() => {
          logger.info('AssignRoleToUser Successfull');
          return true;
        });
        return false;
      });
      return false;
    } catch (error) {
      logger.error('Error Assigning Role to User', { message: error });
      throw new Error('Error Assigning Role to User' + error);
    }
  }

  public async GetUserRole (
    user_id: string,
    organizationId: string,
  ): Promise<Role | null> {
    logger.info('GetUserRole Called');
    try {
      const organization = await OrganizationModel.find(
        { _id: organizationId },
        { roles: { $elemMatch: { user_id: user_id } } },
      ).lean();
      console.log(user_id);
      if (organization) {
        const rolesArray = organization[0].roles;

        if (rolesArray && rolesArray.length > 0) {
          const userRole = rolesArray[0];
          if (userRole) {
            logger.info('GetUserRole Successfull', { userRole: userRole });
            return userRole as Role;
          }
        } else {
          return null;
          logger.error('User not in this organization');
          throw new Error('User not in this organization');
        }
      }
      return null;
    } catch (error) {
      return null;
      logger.error('User does not exist in organization', { message: error });
      throw new Error('User does not exist in organization: ' + error);
    }
  }

  public async checkAndCreateUser (userId: string): Promise<User | null> {
    logger.info('checkAndCreateUser Called');
    const userData: User | null = await UserModel.findOne({
      _id: userId,
    }).lean();
    if (userData && userData.organization_id) {
      logger.info('checkAndCreateUser Successfull');
      return userData;
    }
    const authUser = await getUser(userId);
    if (authUser) {
      const userModule = new UserModule();
      userModule.migrateFromFirebase(authUser);
      const migratedUserData: User | null = await UserModel.findOne({
        _id: userId,
      }).lean();
      return migratedUserData || null;
    }
    return null; // Return null if no organization found
  }

  public async uploadThumbnail (
    organizationId: string,
    thumbnail: string,
  ): Promise<void> {
    logger.info('uploadThumbnail Called');
    if (!thumbnail) {
      logger.error('Thumbnail image is missing', { thumbnail: thumbnail });
      throw new Error('Thumbnail image is missing');
    }

    try {
      const organization = await OrganizationModel.findOneAndUpdate(
        { _id: organizationId },
        { $set: { thumbnail: thumbnail } },
        { new: true },
      );
      logger.info('uploadThumbnail Successfull');

      if (!organization) {
        logger.error('Organization not found');
        throw new Error('Organization not found');
      }
    } catch (error) {
      logger.error('Organization not found', { message: error });
      throw new Error('Error uploading thumbnail');
    }
  }
}

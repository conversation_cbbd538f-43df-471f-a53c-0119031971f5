import express from 'express';
import { UpdateUnitByMeta, uploadcsv } from '../controllers/units/webhooks';
import { JWTAuthControl } from '../middlewares/jwtController';
import uploadHandler from '../helpers/uploadHandler';
import {uploadCsvValidator, UpdateUnitByMetaValidator} from '../controllers/units/webhooks/webhookValidator';
import {createUser, CheckUserExists, singleSignOn, SSO} from '../controllers/user/webhooks';
import checkuserexistsValidate from '../controllers/user/webhooks/checkuserexistsValidate';
import createUserValidate from '../controllers/user/webhooks/createUserValidate';
import SSOValidate from '../controllers/user/webhooks/SSOValidate';
import checkLogValidate from '../controllers/user/webhooks/checkLogValidator';
import BookSessionValidate from '../controllers/sessions/webhooks/BookSessionValidator';
import GetAvailableSlotsValidate from '../controllers/sessions/webhooks/GetAvailableSlotsValidate';
import AssignSessionValidator from '../controllers/sessions/webhooks/AssignSessionValidator';
import GetAnonymousSessionsValidator from '../controllers/sessions/webhooks/GetAnonymousSessionValidator';
import { GetAvailableSlots, BookSession, GetAnonymousSessions, AssignSession,
  GetMonthlySlots } from '../controllers/sessions/webhooks';
import GetProjectValidate from '../controllers/projects/GetListOfProjects/GetProjectValidate';
import { GetListOfProjects } from '../controllers/projects/webhooks';
import GetMonthlySlotsValidate from '../controllers/sessions/webhooks/GetMonthlySlotsValidate';
const router = express.Router();

router.post('/ppg/updateUnit',
  UpdateUnitByMetaValidator,
  JWTAuthControl,
  UpdateUnitByMeta,
);
router.post('/ppg/uploadcsv',
  JWTAuthControl,
  uploadHandler([{ name: 'file' }],  'output/'),
  uploadCsvValidator,
  uploadcsv,
);
router.post('/checkUserExists', JWTAuthControl, checkuserexistsValidate, CheckUserExists);
router.post('/createUser', JWTAuthControl, createUserValidate, createUser);
router.post('/singleSignOn', JWTAuthControl, SSOValidate, singleSignOn);
router.post('/sso', JWTAuthControl, checkLogValidate, SSO);
router.post('/session/GetMonthlySlots', GetMonthlySlotsValidate, JWTAuthControl, GetMonthlySlots);
router.post('/session/GetAvailableSlots', GetAvailableSlotsValidate, JWTAuthControl, GetAvailableSlots);
router.post('/session/BookSession', BookSessionValidate, JWTAuthControl, BookSession);
router.post('/session/GetAnonymousSessions',
  GetAnonymousSessionsValidator, JWTAuthControl, GetAnonymousSessions);
router.post('/session/AssignSession', AssignSessionValidator, JWTAuthControl, AssignSession);
router.get(
  '/projects/GetListOfProjects',
  GetProjectValidate,
  JWTAuthControl,
  GetListOfProjects,
);
export default router;

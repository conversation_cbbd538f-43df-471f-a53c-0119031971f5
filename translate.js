const {TranslationServiceClient} = require('@google-cloud/translate');
const dotenv = require('dotenv');
const fs = require('fs');

dotenv.config();
// Instantiates a client
const translationClient = new TranslationServiceClient({
    credentials: {
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      private_key: (process.env.FIREBASE_PRIVATE_KEY || '').replace(/\\n/g, '\n'),
    },
  });
console.log({
    credentials: {
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      private_key: (process.env.FIREBASE_PRIVATE_KEY || '').replace(/\\n/g, '\n'),
    },
  })
async function translateText(sourceLanguageCode,targetLanguageCode,text) {
  // Construct request
  const request = {
    parent: `projects/${'propvr-in-31420'}/locations/${'us-central1'}`,
    contents: [text],
    mimeType: 'text/plain', // mime types: text/plain, text/html
    sourceLanguageCode: sourceLanguageCode,
    targetLanguageCode: targetLanguageCode,
  };
// AIzaSyBso7DjwuLXXIQ8fF8beOUoIiccoOe76Hc
  // Run request
  const [response] = await translationClient.translateText(request);

  for (const translation of response.translations)
  {
    console.log(`Translation: ${translation.translatedText}`);
    fs.writeFile('translated.txt', translation.translatedText, (err) => {
        if (err) throw err;
    });
  }
}

translateText("en","ta","hello");
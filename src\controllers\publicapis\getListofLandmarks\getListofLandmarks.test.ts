import app  from '../../../app';
import request from 'supertest';
describe('Get List of Landmarks Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/getListofLandmarks?organization_id=HIPUat')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual({});
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getListofLandmarks?organization_id=HIPUt')   // Invalid organization Id
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).toEqual({});
  });
});

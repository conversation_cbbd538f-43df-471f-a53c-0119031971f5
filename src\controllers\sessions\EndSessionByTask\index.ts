import { Request, Response } from 'express';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';
import { TaskModule } from '../../../modules/tasks';
import { SessionStatus } from '../../../types/session';

export async function EndSessionByTask (request: Request, response: Response): Promise<void> {
  const session = new SessionModule();
  const task = new TaskModule();
  const session_id = request.body.session_id;

  try {
    const current_session = await session.getSessionById(session_id);

    // Check if session status is invalid for ending
    const invalidStatuses = [SessionStatus.CANCELLED, SessionStatus.ONHOLD, SessionStatus.ENDED];
    if (invalidStatuses.includes(current_session.status)) {
      logger.info(`Session ${session_id} is already in '${current_session.status}' status. No action will be taken.`);
      response.send({ status: 0, error: 'Session already in invalid status.' });
      return;
    }

    // Parse last interaction and current time
    const lastInteractionTime = new Date(current_session.last_interaction_time);
    const currentTime = new Date();
    const timestamp = Date.now();
    // Calculate idle time in seconds
    const idleTime = (currentTime.getTime() - lastInteractionTime.getTime()) / 1000;
    const idleThreshold = 60 * 30; // 30 minutes in seconds

    if (idleTime > idleThreshold) {
      // End the session if idle time exceeds the threshold
      const sessionData = await session.EndSession(session_id);
      logger.info(`Session ${session_id} ended due to inactivity.`);
      response.send({ status: 1, data: sessionData });
    } else {
      // Reschedule the task if idle time does not exceed the threshold
      logger.info(`Idle time for session ${session_id} does not exceed threshold. Rescheduling task.`);
      const rescheduleTime = new Date(currentTime.getTime() + (15 * 60 * 1000)); // Reschedule 15 minutes later
      const rescheduleTimeISO = rescheduleTime.toISOString();
      const payload = { session_id: session_id };
      const mailQueueName = 'session-ender';
      const rescheduleTaskId = `${session_id}_${timestamp}_reschedule`;

      await task.createTask(mailQueueName,
        `${process.env.BASE_URL}session/EndSessionbyTask`, payload, rescheduleTimeISO, rescheduleTaskId, {});
      response.send({ status: 0, error: 'Session has not been idle long enough. Task rescheduled.' });
    }
  } catch (error) {
    logger.error(`Error in EndSessionByTask for session ${session_id}`, { message: error });
    response.send({ status: 0, error: 'Error processing session end task.' });
  }
}

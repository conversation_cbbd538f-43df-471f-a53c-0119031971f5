import { Request, Response } from 'express';
import { ProjectModule } from '../../../modules/projects';
import logger from '../../../config/logger';
export async function GetProject (
  request: Request,
  response: Response,
): Promise<void> {
  try {
    logger.info('GetProject called', { params: request.params, organization: request.headers.organization });
    const { project_id } = request.params;
    const organization_id = request.headers.organization as string;
    //   Pass organization_id in this ProjectModule
    const project = new ProjectModule(organization_id);

    const projectData = await project.getProjectById(project_id);

    if (projectData) {
      // Logger.info('Project found', { project_id, organization_id });
      response.status(200).json({ status: 1, data: projectData });
    } else {
      // Logger.warn('Project not found in organization', { project_id, organization_id });
      response.status(404).json({ status: 0, error: 'Project not found' });
    }
  } catch (error) {
    logger.error('Error in GetProject', { message: error });
    response.status(404).json({ status: 0, error: 'Project not found' });
  }
}

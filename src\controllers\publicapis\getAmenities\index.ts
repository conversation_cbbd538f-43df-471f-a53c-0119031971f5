import {  Request, Response } from 'express';
import { AmenityModule } from '../../../modules/amenity';

export async function GetAmenities (request: Request, response: Response):Promise<void> {
  const project_id = request.params.project_id as string;
  const organization_id = request.params.organization_id as string;
  const category = request.query.category as string;
  console.log(project_id, organization_id);

  if (!organization_id){
    response.send({status: 400, error: 'no organization is found'});
    return ;
  }
  const amenity = new AmenityModule(project_id, organization_id);
  const amenities = await amenity.GetAmenities(category);
  if (amenities) {
    response.send({ status: 1, data: amenities });
  } else {
    response.send({ status: 1, data: [] });
  }
  return ;
}

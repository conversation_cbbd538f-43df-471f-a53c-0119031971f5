import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { NotificationModule } from '../../../modules/notification';
import logger from '../../../config/logger';

export async function GetNotifications (
  request:ExtendedRequest,
  response:Response,
): Promise<void> {
  try {
    const organization_id = request.headers.organization as string;
    const query = { ...request.query };
    query.organization_id = organization_id;

    const notifcations = new NotificationModule(query.user_id as string);
    let notificationData;
    if (query.user_id) {
      if (query.viewed === 'false'){
        delete query.viewed;
        notificationData = await notifcations.GetNotifications(query, false);
      } else {
        notificationData = await notifcations.GetNotifications(query);
      }
    } else {
      response.status(404).json({ status: 0, error: 'Permission Denied' });
      return;
    }
    if (notificationData){
      response.status(200).json({ status: 1, data: notificationData });
    } else {
      response.status(200).json({ status: 0, data: {} });
    }
  } catch (error){
    logger.error('Error retrieving meetings:', error);
    response.status(500).json({ status: 0, error: 'Internal server error' });
  }
}

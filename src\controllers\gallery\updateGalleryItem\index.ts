import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { Request, Response } from 'express';
import { GalleryModule } from '../../../modules/gallery';
import logger from '../../../config/logger';

export async function updateGalleryItem (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id as string;
  const organization_id = request.body.organization;
  const gallery = new GalleryModule(project_id, organization_id);
  const item_id = request.body.item_id as string;
  const requestFiles = request.files;
  if (requestFiles) {
    UploadUnitplanFiles(requestFiles, gallery.storagepath+item_id)
      .then((urlObject: { [key: string]: string }) => {
        const { ...updateFields } = request.body;
        updateFields.thumbnail = urlObject.thumbnail;
        updateFields.url = urlObject.file;
        gallery.UpdateGalleryItem(item_id, updateFields).then((galleryItem) => {
          response.status(200).json({ status: 1, data: galleryItem });
        }).catch((error) => {
          logger
            .error('Gallery Item not found:', {message: error});
          response.status(404).json({ status: 0, error: 'Gallery Item not found'+ error });
        });

      });
  } else {
    const { ...updateFields } = request.body;
    gallery.UpdateGalleryItem(item_id, updateFields).then((galleryItem) => {
      response.status(200).json({ status: 1, data: galleryItem });
    }).catch((error) => {
      logger
        .error('Gallery Item not found:', {message: error});
      response.status(404).json({ status: 0, error: 'Gallery Item not found'+ error });
    });
  }
}

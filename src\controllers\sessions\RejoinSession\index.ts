import { Request, Response } from 'express';
import { SessionModule } from '../../../modules/sessions';
import logger from '../../../config/logger';

export async function RejoinSession (
  request: Request,
  response: Response,
): Promise<void> {
  const session = new SessionModule();
  const session_id = request.body.session_id;

  try {
    const existingSession = await session.getSessionById(session_id);
    const organization_id = existingSession.organization_id;

    if (!organization_id) {
      response.status(400).send({ status: 0, error: 'No organization found for this session' });
      return;
    }

    if (existingSession.status !== 'ended') {
      response.status(400).send({ status: 0, error: 'Session cannot be rejoined' });
      return;
    }

    const updatedSession = await session.RejoinSessionStatus(session_id);
    response.send({ status: 1, data: updatedSession });
  } catch (error) {
    logger.error('Error in RejoinSession', { message: error });
    response.status(500).send({ status: 0, error: 'Unable to rejoin session' });
  }
}

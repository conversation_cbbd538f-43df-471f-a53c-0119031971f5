import { Response } from 'express';
import { FileRequest } from '../../../types/extras';
import { AmenityModule } from '../../../modules/amenity';
// Import multer from 'multer';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { MediaType } from '../../../types/amenity';
import logger from '../../../config/logger';

export async function UpdateMedia (
  request: FileRequest,
  response: Response,
): Promise<object | void> {
  const organization_id = request.organization_id;
  if (!organization_id) {
    logger.error('Not authorized:');
    response.send({ status: 0, error: 'Not authorized' });
    return;
  }

  const project_id = request.body.project_id;
  const amenity_id = request.body.amenity_id;
  const media_id = request.body.media_id;
  const amenity = new AmenityModule(project_id, organization_id);
  const requestFiles = request.files;
  if (requestFiles) {
    const mediaUrl = await UploadUnitplanFiles(requestFiles, amenity.storagepath+amenity_id);

    const mediaObj = {
      file: mediaUrl.thumbnail,
      media_type: request.body.media_type,
      ...(request.body.media_type === MediaType.LINK && { link: request.body.link }),
      ...(request.body.media_type === MediaType.VIRTUAL_TOUR && { tour_id: request.body.tour_id }),
    };
    amenity.UpdateMedia(amenity_id, media_id, mediaObj).then((amenityData) => {
      response.status(201).json({ status: 1, data: amenityData });
    }).catch((error) => {
      logger.error('Error:', {message: error});
      response.send({ status: 0, error: 'Unable to create media' + error });
    });
  } else {
    const mediaObj = {
      media_type: request.body.media_type,
      ...(request.body.media_type === MediaType.LINK && { link: request.body.link }),
      ...(request.body.media_type === MediaType.VIRTUAL_TOUR && { tour_id: request.body.tour_id }),
    };
    amenity.UpdateMedia(amenity_id, media_id, mediaObj).then((amenityData) => {
      response.status(201).json({ status: 1, data: amenityData });
    }).catch((error) => {
      logger.error('Unable to create media:', {message: error});
      response.send({ status: 0, error: 'Unable to create media' + error });
    });
  }

  // Const upload = multer({ dest: 'output/amenities/' }).single('file');
  // Upload(request, response, async () => {

  // });
}

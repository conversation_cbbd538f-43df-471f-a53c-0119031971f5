import client from 'prom-client';
import express from 'express';
const router = express.Router();

// Prometheus configuration
const collectDefaultMetrics = client.collectDefaultMetrics;
collectDefaultMetrics();

const customRegister = new client.Registry();
customRegister.setDefaultLabels({
  app: 'monitoring-article',
});

export const restResponseTimeHistogram = new client.Histogram({
  name: 'rest_response_time_duration_seconds',
  help: 'REST API response time in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.5, 1, 2, 5],
});

export const databaseResponseTimeHistogram = new client.Histogram({
  name: 'db_response_time_duration_seconds',
  help: 'Database response time in seconds',
  labelNames: ['operation', 'success'],
});

export const httpRequestTimer = new client.Histogram({
  name: 'http_request_duration_ms',
  help: 'Duration of HTTP requests in ms',
  labelNames: ['method', 'route', 'code'],
  // Buckets for response time from 0.1ms to 1s
  buckets: [0.1, 5, 15, 50, 100, 200, 300, 400, 500, 1000],
});

router.get('/metrics', async (req, res) => {
  // Get metrics from default registry
  const defaultMetrics = await client.register.metrics();

  // Get metrics from custom registry
  const customMetricsPromise =  customRegister.metrics();

  const customMetrics = await customMetricsPromise;

  // Merge metrics from both registries
  const allMetrics = defaultMetrics+'\n'+(customMetrics);

  // Send merged metrics in the response
  res.set('Content-Type', client.register.contentType);
  res.send(allMetrics);

});

export default router;

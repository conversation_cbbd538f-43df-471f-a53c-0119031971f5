import app  from '../../../app';
import request from 'supertest';
describe('Get Floor Details Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/getFloorDetails'
      +'?organization_id=8X3plU&project_id=65701718b5063acb09dbf6e4&building_id=6597b5cb9e0eb570117d4bc9')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual([]);
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getFloorDetails?organization_id=8X3plU'
      +'&project_id=65701718b5063acb09dbf6e&building_id=6597b5cb9e0eb570117d4bc9')   // Invalid project Id
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).toEqual({});
  });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getFloorDetails?organization_id=8X3plU'
      +'&project_id=65701718b5063acb09dbf6e&building_id=6597b5cb9e0eb570117d4bc9')   // Invalid project Id
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).toEqual({});
  });

});

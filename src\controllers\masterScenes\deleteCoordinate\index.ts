import { ExtendedRequest } from '../../../types/extras';
import { masterScene } from '../../../types/masterScene';
import { MasterSceneModule } from '../../../modules/masterScene';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function deleteCoordinate (
  request: ExtendedRequest,
  response: Response,
): Promise<masterScene | void> {
  const organization_id = request.organization_id as string;
  const scene = new MasterSceneModule(organization_id);
  const targetSceneId = request.body.masterSceneId;
  const coordinateId = request.body.coordinateId;
  await scene
    .deleteCoordinate(targetSceneId, coordinateId)
    .then((sceneData) => {
      response.status(201).json({ status: 1, data: sceneData });
    })
    .catch((error: Error) => {
      logger.error('Error while updating coordinate', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while updating coordinate'+ error });
    });
}

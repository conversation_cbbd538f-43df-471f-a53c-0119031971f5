import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';
import { Response } from 'express';
import { VirtualTourModule } from '../../../modules/virtualTour';

export async function AddTourLabel (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  try {
    const organization_id = request.headers.organization as string;
    const { project_id, tour_id } = request.body;
    const tourModule = new VirtualTourModule(organization_id, project_id);
    const addLabelPayload = { ...request.body };
    const tourData = await tourModule.AddTourLabel(tour_id, addLabelPayload);
    response.status(200).json({ status: 1, data: tourData });
  } catch (error) {
    logger.error('Error Addding tour label', { message: error });
    response.status(404).json({ status: 0, error: 'Error Addding tour label' + error });
  }
}

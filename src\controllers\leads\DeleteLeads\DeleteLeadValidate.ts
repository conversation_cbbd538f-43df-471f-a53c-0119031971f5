import { Request, Response, NextFunction } from 'express';
import { validationResult, header, param } from 'express-validator';

const DeleteLeadValidate = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  param('lead_id', 'Lead Id is required').notEmpty(),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default DeleteLeadValidate;

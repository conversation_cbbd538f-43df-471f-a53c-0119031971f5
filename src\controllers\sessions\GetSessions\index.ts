import { SessionModule } from '../../../modules/sessions';
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { UserRole } from '../../../types/organization';
import logger from '../../../config/logger';
export async function GetSessions (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const query = { ...request.query };
  const organization_id = request.organization_id as string;
  const session = new SessionModule();
  const userRole =request.UserRole;
  let sessionData;
  const user_id = request.IsAuthenticated?.uid as string;
  console.log(userRole);
  if (userRole?.role === UserRole.ADMIN) {
    sessionData = await session.getSessions(organization_id, query);
  } else if (userRole?.role === UserRole.EDITOR || userRole?.role === UserRole.READER) {
    if (!query.user_id){
      query.user_id = request.IsAuthenticated?.uid;
    }
    if (query.user_id === user_id) {
      sessionData = await session.getSessions(organization_id, query);
    } else {
      response.status(404).json({ status: 0, error: 'Permission Denied' });
      return;
    }
  }
  if (sessionData){
    response.status(200).json({ status: 1, data: sessionData });
  } else {
    logger.error('Not Authorised');
    response.status(404).json({ status: 0, error: 'Not Authorised' });
  }
}

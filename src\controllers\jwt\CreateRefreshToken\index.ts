import { Response } from 'express';
import { JWTModule } from '../../../modules/jwt';
import { ExtendedRequest } from '../../../types/extras';
import logger from '../../../config/logger';

export async function createRefToken (
  request:ExtendedRequest,
  response:Response,
):Promise<void> {
  try {
    const organization = request.body.organization_id;
    console.log('****', organization);

    const jwtModule = new JWTModule();
    const result = await jwtModule.createRefreshToken(organization);
    if (result){
      response.status(200).send({status: 1, data: result});
    }
  } catch (err){
    logger.error('Internal server error', {message: err});
    response.status(400).send({status: 0, message: 'Internal server error'});
  }
}

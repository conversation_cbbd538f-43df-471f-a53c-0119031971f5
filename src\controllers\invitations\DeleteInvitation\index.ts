import {  Response } from 'express';
import {  ExtendedRequest } from '../../../types/extras';
import { InvitesModule } from '../../../modules/invites';

export async function DeleteInvitation (request: ExtendedRequest, response: Response):Promise<void> {
  const invitation = new InvitesModule;
  const IsAuthenticated = request.IsAuthenticated;
  const organization_id = request.organization_id;
  const invite_id = request.body.invitation_id;
  if (!organization_id){
    response.send({status: 0, error: 'no organization is found'});
    return ;
  }
  if (!IsAuthenticated){
    response.send({status: 0, error: 'Not authorized'});
    return ;
  }
  const deletedInvitation = await invitation.DeleteInvitation(invite_id);
  if (deletedInvitation) {
    response.send({ status: 1, data: deletedInvitation });
  } else {
    response.send({ status: 1, data: [] });
  }
  return ;
}

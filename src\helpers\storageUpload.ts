import { admin, bucketName} from '../config/firebase';
import fs from 'fs';
type UploadOptions ={
  destination:string
}
export async function storageUpload (
  uploadOptions:UploadOptions, path:string,
):Promise<string> {
  return new Promise((resolve, reject) => {
    const timestamp = Date.now();
    const destination = uploadOptions.destination;
    const extension = destination.substring(destination.lastIndexOf('.'));

    // Destination + timestamp
    const updatedDestination = destination.substring(0, destination.lastIndexOf('.')) + '_' + timestamp + extension;

    admin.storage().bucket(bucketName)
      .upload(path, {...uploadOptions, destination: updatedDestination}, async (error, uploadedFile) => {
        if (error || uploadedFile===undefined || uploadedFile === null) {
          reject(error);
        }
        if (fs.existsSync(path)) {
          await fs.promises.unlink(path);
        }
        const thumbnailUrl =
          'https://firebasestorage.googleapis.com/v0/b/' +
          bucketName +
          '/o/' +
          encodeURIComponent(
            uploadedFile?uploadedFile.name:'',
          ) +
          '?alt=media';
        resolve(thumbnailUrl);
      });
  });
}

import express from 'express';
import createOptionsValidate from '../controllers/sidebar/createOptions/createOptionsValidator';
import { createOptions } from '../controllers/sidebar/createOptions';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { updateOptions } from '../controllers/sidebar/updateOptions';
import updateOptionsValidate from '../controllers/sidebar/updateOptions/updateOptionsValidator';
import { getOptions } from '../controllers/sidebar/getOptions';
import getOptionsValidate from '../controllers/sidebar/getOptions/getOptionsValidator';
import { deleteOption } from '../controllers/sidebar/deleteOptions';
import deleteOptionValidate from '../controllers/sidebar/deleteOptions/deleteOptionsValidator';
import updateBulkOptionsValidate from '../controllers/sidebar/updateBulkOptions/updateBulkOptionsValidator';
import { updateBulkOptions } from '../controllers/sidebar/updateBulkOptions';
import moveToTrashValidate from '../controllers/sidebar/moveToTrash/moveToTrashValidator';
import { moveToTrash } from '../controllers/sidebar/moveToTrash';
import restoreSidebarValidate from '../controllers/sidebar/restoreSidebar/restoreSidebarValidator';
import { restoreSidebar } from '../controllers/sidebar/restoreSidebar';
import { accessControlMiddleware } from '../middlewares/AccessControl';

const router = express.Router();

router.post(
  '/createOptions',
  authMiddleware,
  organizationAccessMiddleware,
  createOptionsValidate,
  createOptions,
);
router.post(
  '/updateOptions',
  authMiddleware,
  organizationAccessMiddleware,
  updateOptionsValidate,
  updateOptions,
);
router.get(
  '/getOptions/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  getOptionsValidate,
  getOptions,
);
router.post(
  '/deleteOption',
  authMiddleware,
  organizationAccessMiddleware,
  deleteOptionValidate,
  deleteOption,
);
router.post(
  '/updateBulkOptions',
  authMiddleware,
  organizationAccessMiddleware,
  updateBulkOptionsValidate,
  updateBulkOptions,
);
router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreSidebar/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreSidebarValidate,
  restoreSidebar,
);
export default router;

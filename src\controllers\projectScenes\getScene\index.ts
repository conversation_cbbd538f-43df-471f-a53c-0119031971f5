import { Request, Response } from 'express';
import { ProjectSceneModule } from '../../../modules/projectScene';
import { ProjectSVGModule } from '../../../modules/projectSVG';
import logger from '../../../config/logger';

export async function getScene (
  request: Request,
  response: Response,
): Promise<void> {
  const { scene_id, project_id } = request.params;
  const organization_id = request.headers.organization as string;
  const projectScene = new ProjectSceneModule(project_id, organization_id);
  const projectsceneData = await projectScene.getScene(scene_id);
  const svg = new ProjectSVGModule(project_id, organization_id);

  const svgData = await svg.getSvgById(scene_id);
  if (projectsceneData) {
    response.status(200).json({ status: 1, data: {sceneData: projectsceneData, svgData: svgData} });
  } else {
    logger.error('Error in getScene');
    response.status(404).json({ status: 0, error: 'scene not found' });
  }
}

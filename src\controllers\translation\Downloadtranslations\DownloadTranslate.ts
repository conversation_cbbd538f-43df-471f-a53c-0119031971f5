import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { TranslationModule } from '../../../modules/translation';
import ExcelJS from 'exceljs';
import logger from '../../../config/logger';
import { Translations } from '../../../types/translation';

export const DownloadTranslations = async (
  req: ExtendedRequest,
  res: Response,
): Promise<void> => {
  try {
    logger.info('DownloadTranslations Called');

    const organizationId = req.organization_id;
    if (!organizationId) {
      res.status(400).json({ message: 'Organization ID is required' });
      return;
    }

    const translationModule = new TranslationModule(organizationId);
    const { translations } = await translationModule.GetAllTranslation();

    if (!translations || Object.keys(translations).length === 0) {
      res.status(404).json({ message: 'No translations found' });
      return;
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Translations');

    const firstTranslation = Object.values(translations)[0] as Translations;
    const availableLanguages = firstTranslation
      ? Object.keys(firstTranslation).filter((key) =>
        key !== '_id' && key !== '__v' && key !== 'name',
      )
      : [];

    const filteredLanguages = availableLanguages.filter((lang) => lang !== 'en');
    const headers = ['_id', 'English', ...filteredLanguages];
    worksheet.addRow(headers);

    Object.values(translations).forEach((translation: Translations) => {
      const rowData = [
        translation._id?.toString() || '',
        translation.en || '',
        ...filteredLanguages.map((lang) => {
          const value = translation[lang as keyof Translations];
          return typeof value === 'string' ? value : '';
        }),
      ];
      worksheet.addRow(rowData);
    });

    worksheet.columns.forEach((column) => {
      column.width = 20;
      column.alignment = { wrapText: true };
    });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader(
      'Content-Disposition', `attachment; filename=translations-${new Date().toISOString().split('T')[0]}.xlsx`,
    );

    const buffer = await workbook.xlsx.writeBuffer();

    // Send the buffer and end the response
    logger.info('DownloadTranslations Completed Successfully');
    res.status(200).send(buffer);

  } catch (error) {
    logger.error('Error in DownloadTranslations', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    res.status(500).json({
      message: 'Failed to download translations',
      error: 'An internal server error occurred',
    });
  }
};

import logger from '../../../config/logger';
import { UploadUnitplanFiles } from '../../../helpers/uploadFirebase';
import { ModelModule } from '../../../modules/glbModel';
import { glbmodel } from '../../../types/glbModels';
import { Request, Response } from 'express';
export async function updateModel (
  request: Request,
  response: Response,
): Promise<glbmodel | void> {
  const project_id = request.body.project_id;
  const organization_id = request.headers.organization as string;
  const model = new ModelModule(project_id, organization_id);
  const requestFiles = request.files;
  try {
    let updateModelData = request.body;
    if (requestFiles) {
      const urlObject = await UploadUnitplanFiles(requestFiles, model.storagepath + request.body.model_id);
      updateModelData = {
        thumbnail: urlObject.thumbnail,
        url: urlObject.file,
        ...request.body,
      };
    }

    const modelData = await model.updateModel(updateModelData);
    response.status(201).json({ status: 1, data: modelData });
  } catch (error) {
    logger.error('Error while updating model', {message: error});
    response.status(500).json({ status: 0, error: 'Error while updating model' + error });
  }
}

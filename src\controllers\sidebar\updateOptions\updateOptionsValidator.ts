import { Request, Response, NextFunction } from 'express';
import { validationResult, header, body } from 'express-validator';
import { Type } from '../../../types/sidebar';

const updateOptionsValidate = [
  header('organization', 'Organization  is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  body('id', 'ID is required').notEmpty(),
  body('project_id', 'Project ID is required').notEmpty(),
  body('type', 'Please select valid type').optional().isIn(Object.values(Type)),
  // Body('scene_id').custom((value, { req }) => {
  //   If (req.body.type !== 'gallery' && !value && req.body.type !== 'unitplan'
  //     && req.body.type !== 'inventory' && !value &&  req.body.type !== 'amenity' && req.body.type !== 'custom') {
  //     Throw new Error('Scene ID is required');
  //   }
  //   Return true;
  // }),
  // Body('link').custom((value, { req }) => {
  //   If (req.body.type === 'custom' && !req.body.scene_id && !value) {
  //     Throw new Error('Link is required when type is custom and scene_id is not provided');
  //   }
  //   Return true;
  // }),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default updateOptionsValidate;

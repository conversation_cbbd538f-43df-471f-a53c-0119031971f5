import { Response, Request} from 'express';
import { BreadCrumbModule } from '../../../modules/breadCrumb';
import { ProjectSceneModule } from '../../../modules/projectScene';
import logger from '../../../config/logger';
export default async function getBreadCrumbs (
  request: Request,
  response: Response,
): Promise<void> {
  const {project_id, currentScene}=request.body;
  const organization_id = request.headers.organization as string;
  const masterScene = new BreadCrumbModule(organization_id, project_id);
  if (!project_id){
    masterScene.findPreviousMasterScenes(currentScene, `${organization_id}_master_scenes`)
      .then((previousScenes) => {
        response.status(200).send({ status: 1, data: previousScenes });
      })
      .catch((error) => {
        logger.error('Error:', {message: error});
        response.status(400).send({ status: 0, message: error });
      });
  } else {
    const projectScene = new ProjectSceneModule(project_id, organization_id);
    const projectsceneData = await projectScene.getScene(currentScene);
    masterScene.findTopMostParentFromProjectScenes(currentScene,
      `${organization_id}_master_scenes`,
      `${project_id}_scenes`,
      projectsceneData?.root)
      .then((previousScenes) => {
        response.status(200).send({ status: 1, data: previousScenes });
      })
      .catch((error) => {
        logger.error('Error:', {message: error});
        response.status(400).send({ status: 0, message: error });
      });
  }

}

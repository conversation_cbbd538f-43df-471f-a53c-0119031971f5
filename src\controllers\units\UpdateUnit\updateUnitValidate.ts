import { Request, Response, NextFunction } from 'express';
import { validationResult, body, param } from 'express-validator';
import { measurementType, priceCurrency, unitStatus } from '../../../types/units';

const createUnitValidate = [
  body('project_id', 'Project Id is required').notEmpty(),
  param('unit_id', 'Unit Id is required').notEmpty(),
  body('name').optional(),
  body('unitplan_id').optional(),
  body('floor_id').optional(),
  body('building_id').optional(),
  body('currency').optional().isIn(Object.values(priceCurrency)),
  body('measurement').optional({ checkFalsy: true }).isNumeric(),
  body('balcony_measurement').optional({ checkFalsy: true }).isNumeric(),
  body('balcony_measurement_type').optional().isIn(Object.values(measurementType)),
  body('suite_area').optional({ checkFalsy: true }).isNumeric(),
  body('suite_area_type').optional().isIn(Object.values(measurementType)),
  body('status', 'Invalid Status. Please ensure that you are using a valid status value')
    .optional()
    .isIn(Object.values(unitStatus))
    .notEmpty(),
  body('cta_link').optional(),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default createUnitValidate;

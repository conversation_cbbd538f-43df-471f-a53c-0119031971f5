import { Request, Response } from 'express';
import fs from 'fs';
import fetch, {Head<PERSON>} from 'node-fetch';
import { ModelModule } from '../../../modules/glbModel';
import mongoose from 'mongoose';
import { glbmodel } from '../../../types/glbModels';
import { downloadFile } from '../../../helpers/downloadFile';
import path from 'path';
import logger from '../../../config/logger';

interface artifactData{
    url:string,
    type:string
}

interface blenderResponse{
  status:number,
  message:string,
  output:string
}

async function OptimizeBlender (project_id:string, organization_id:string, model_id:string, luma_url:string){
  return new Promise((resolve, reject) => {
    console.log(project_id, organization_id, model_id, luma_url);
    const myHeaders = new Headers();
    myHeaders.append('Content-Type', 'application/json');

    const raw = JSON.stringify({
      'name': 'chair',
      'lumaUrl': luma_url,
      'project_id': project_id,
      'organization_id': organization_id,
      'model_id': model_id,
    });

    const requestOptions = {
      method: 'POST',
      headers: myHeaders,
      body: raw,
    };

    fetch(process.env.LUMA_API_URL+'/convertgltf', requestOptions)
      .then((response) => {
        console.log('response');
        console.log(response);
        return response.json();
      }).then((responseData:blenderResponse) => {
        if (responseData.status===1){
          console.log(responseData.output);
          resolve(responseData.output);
        }
      })
      .catch((error) => {
        logger
          .error('Error', {message: error});
        reject();
      });
  });
}

// Create Capture
async function createCapture (name: string) {
  const myHeaders = new Headers();
  myHeaders.append('Authorization', process.env.LUMA_API_KEY as string);
  myHeaders.append('Content-Type', 'application/x-www-form-urlencoded');

  const urlencoded = new URLSearchParams();
  urlencoded.append('title', name);
  urlencoded.append('camModel', 'normal');

  const requestOptions = {
    method: 'POST',
    headers: myHeaders,
    body: urlencoded,
    redirect: 'follow' as RequestRedirect,
  };

  try {
    const response = await fetch(
      'https://webapp.engineeringlumalabs.com/api/v2/capture',
      requestOptions,
    );

    if (response.ok) {
      const result = await response.json();
      console.log('In CreateCapture', result);
      const source = result.signedUrls.source;
      const slug = result.capture.slug;
      return { source, slug };
    }
    throw new Error('Invalid response from createCapture');
  } catch (error) {
    logger
      .error('Error in capture', {message: error});
    throw error;
  }
}

// Upload Capture
async function UploadCapture (sourceURL: string, paths: string) {
  const myHeaders = new Headers();
  myHeaders.append('Content-Type', 'video/quicktime');
  const fileData = await fs.readFileSync(paths);
  const requestOptions = {
    method: 'PUT',
    headers: myHeaders,
    body: fileData,
    redirect: 'follow' as RequestRedirect,
  };

  try {
    const response = await fetch(sourceURL, requestOptions);
    if (response.ok) {
      return response.status;
    }
    throw new Error('Invalid response from UploadCapture');
  } catch (error) {
    logger
      .error('Error in UploadCapture', {message: error});

    throw error;
  }
}

// Trigger Capture
// Async function TriggerCapture (slug: string) {
//   Const myHeaders = new Headers();
//   MyHeaders.append('Authorization', process.env.LUMA_API_KEY as string);

//   Const requestOptions = {
//     Method: 'POST',
//     Headers: myHeaders,
//     Redirect: 'follow' as RequestRedirect,
//   };

//   Try {
//     Const response = await fetch(
//       `https://webapp.engineeringlumalabs.com/api/v2/capture/${slug}`,
//       RequestOptions,
//     );
//     If (response.ok) {
//       Const result = await response.json();
//       Return result;
//     }
//     Throw new Error('Invalid response from TriggerCapture');
//   } catch (error) {
//     Logger
//       .error('Error in TriggerCapture', {message: error});
//     Throw error;
//   }
// }

// Get Capture
async function getCapture (slug: string) {
  const myHeaders = new Headers();
  myHeaders.append('Authorization', process.env.LUMA_API_KEY as string);

  const requestOptions = {
    method: 'GET',
    headers: myHeaders,
    redirect: 'follow' as RequestRedirect,
  };

  try {
    const response = await fetch(
      `https://webapp.engineeringlumalabs.com/api/v2/capture/${slug}`,
      requestOptions,
    );
    if (response.ok) {
      const result = await response.json();
      return result;
    }
    throw new Error('Invalid response from GetCapture');

  } catch (error) {
    logger
      .error('Error in getCapture', {message: error});
    throw error;
  }
}

// Check Progress
async function CheckProgress (projectId:string, orgId:string, modelId:string, slug:string){
  console.log('check');
  const interval = setInterval(async () => {
    try {
      const outputFromGetCapture = await getCapture(slug);
      const progress = outputFromGetCapture.latestRun.progress;
      const status = outputFromGetCapture.latestRun.status;

      const project_id = projectId;
      const organization_id = orgId;
      const model = new ModelModule(project_id, organization_id);
      let lumaData = {
        model_id: modelId,
        progress: progress,
        status: 'Uploading',
        url: '',
        thumbnail: '',
      };
      await model.updateProgress(lumaData);
      if (progress>=100 || status==='finished'){
        clearInterval(interval);
        const artifacts:{[key:string]:artifactData} = outputFromGetCapture.latestRun.artifacts;
        const glbData = Object.values(artifacts).find((artifactData:artifactData) => {
          return artifactData && artifactData.type === 'textured_mesh_glb';
        });
        const thumbnail = Object.values(artifacts).find((artifactData:artifactData) => {
          return artifactData && artifactData.type === 'preview_360';
        });
        const url = glbData?glbData.url:'';
        const thumbnailUrl = thumbnail?thumbnail.url:'';
        console.log('---------------------------------URL-------------------------------');
        console.log(url);
        console.log('---------------------------------THUMB-------------------------------');
        console.log(thumbnail);
        OptimizeBlender(project_id, organization_id, modelId, url).then(async (data) => {
          console.log('data');
          console.log(data);
          lumaData = {
            model_id: modelId,
            progress: 100,
            status: status,
            url: url as string,
            thumbnail: thumbnailUrl as string,
          };
          console.log(lumaData);
          console.log('==========================FinishProgress=======================');
          await model.updateProgress(lumaData);
        });
      }
    } catch (error){
      logger
        .error('Error in CheckProgress', {message: error});
      clearInterval(interval);
    }
  }, 10000);
}

// Create Luma
export async function CreateLuma (
  request: Request,
  response: Response,
): Promise<void> {
  const { url, name } = request.body;

  try {

    // Check if the directory exists, and create it if it doesn't
    if (!fs.existsSync('output')) {
      fs.mkdirSync('output', { recursive: true });
    }
    const outputPath = path.join(process.cwd(), 'output', 'file.mov');
    const file:string = await downloadFile(url, outputPath);

    const { source: sourceURL, slug } = await createCapture(name);
    // Const file = request.files as UploadedFiles | undefined;

    if (file) {
      const uploadStatus = await UploadCapture(sourceURL, file);
      console.log('Upload Status:', uploadStatus);
    }

    // Const captureData = await TriggerCapture(slug);

    const project_id = request.body.project_id;
    const organization_id = request.headers.organization as string;
    const model = new ModelModule(project_id, organization_id);

    const lumaModelData = {
      _id: new mongoose.Types.ObjectId(),
      name: name,
      status: 'uploading',
      luma_id: slug,
      progress: 0,
      type: 'luma',
      url: '',
      description: '',
      thumbnail: '',
      meshes: {},
    };

    model.createLumaModel(lumaModelData).then(async (data:glbmodel | void) => {
      if (data && data._id){
        const modelId:string = data._id.toString();
        await CheckProgress(project_id, organization_id, modelId, slug);
        fs.unlink(file, (error) => {
          if (error){
            logger.error('Error deleting file', {message: error});
            return;
          }
          logger.error('File Deleted Successfully');
        });
        response.status(200).send({
          _id: data._id,
          status: 1,
          lumaId: slug,
        });
      }

    });

  } catch (error) {
    logger.error('Error in CreateLuma:', {message: error});
    response.status(500).send('Server Error');
  }

}

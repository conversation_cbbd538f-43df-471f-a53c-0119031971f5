import {  Request, Response } from 'express';
import { AmenityModule } from '../../../modules/amenity';
import logger from '../../../config/logger';

export async function GetAmenities (request: Request, response: Response):Promise<void> {
  const project_id = request.query.project_id as string;
  const organization_id = request.headers.organization as string;
  const category = request.query.category as string;
  const search = request.query.search as string;

  if (!organization_id){
    logger.error('No organization is found ');
    response.send({status: 0, error: 'no organization is found'});
    return ;
  }
  const amenity = new AmenityModule(project_id, organization_id);
  const amenities = await amenity.GetAmenities(category, search);
  if (amenities) {
    response.send({ status: 1, data: amenities });
  } else {
    response.send({ status: 1, data: [] });
  }
  return ;
}

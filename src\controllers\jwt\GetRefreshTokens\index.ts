
import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { JWTModule } from '../../../modules/jwt';
export async function getRefreshTokens (
  request: ExtendedRequest,
  response: Response,
):Promise<void>{
  try {
    const {organization} = request.params;
    const JWT = new JWTModule();
    const result = await JWT.getRefreshTokens(organization);
    response.status(200).send({status: 1, result: result});

  } catch (err){
    response.status(400).send({
      status: 0,
      message: (err instanceof Error) ? err.message : 'An unknown error occurred',
    });
  }
}

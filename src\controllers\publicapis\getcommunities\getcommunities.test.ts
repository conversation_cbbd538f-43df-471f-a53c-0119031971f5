import app  from '../../../app';
import request from 'supertest';
describe('Get Community Endpoint', () => {
  it('should return a data object with data', async () => {
    const res = await request(app)
      .get('/publicapis/getCommunities'
      +'?organization_id=8X3plU&project_id=65701718b5063acb09dbf6e4')
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).not.toEqual({});
  });

  //   It('should return an empty data object with no data', async () => {
  //     Const res = await request(app)
  //       .get('/publicapis/getCommunities?organization_id=8X3pl'
  // +'&project_id=65701718b5063acb09dbf6e4')   //invalid organization Id
  //       .send();
  //     Expect(res.statusCode).toEqual(200);
  //     Expect(res.body).toHaveProperty('data');
  //     Expect(res.body.data).toEqual([]);
  //   });

  it('should return an empty data object with no data', async () => {
    const res = await request(app)
      .get('/publicapis/getCommunities'
      +'?organization_id=8X3plU&project_id=65701718b5063acb09dbf6')   // Invalid project Id
      .send();
    expect(res.statusCode).toEqual(200);
    expect(res.body).toHaveProperty('data');
    expect(res.body.data).toEqual({});
  });
});

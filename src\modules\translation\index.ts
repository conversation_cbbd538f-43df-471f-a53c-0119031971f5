import { Translations, TransaltionResult, SupportedLanguages, TranslationDocument, TranslationModuleResponse } from '../../types/translation';
import { translationSchema } from '../../schema/translationSchema';
import mongoose from 'mongoose';
import { translateText } from '../../helpers/translation';
import logger from '../../config/logger';
import { Models } from '../../types/extras';

export class TranslationModule {
  private model: mongoose.Model<Translations>;

  constructor (organization_id:string){
    this.model = mongoose.model<Translations>(`${organization_id}${Models._TRANSLATION}`, translationSchema);
  }

  async Translate (sourceLanguageCode:SupportedLanguages, targetLanguageCode:SupportedLanguages, text:string)
  :Promise<Translations[]>{
    const textval = text.toLowerCase();
    logger.info('Translate Called', {sourceLanguageCode, targetLanguageCode, textval});

    return translateText(sourceLanguageCode, targetLanguageCode, text).then(async (translation:TransaltionResult) => {
      logger.info('Calling UpdateTranslation', {sourceLanguageCode, targetLanguageCode, translation});
      console.log('update');

      const updatedTranslation = await this.UpdateTranslation(targetLanguageCode, textval, translation as string);
      logger.info('UpdateTranslation Successfull', {updatedTranslation});

      return (updatedTranslation as Translations[]);
    });

  }
  async GetTranslation (text:string):Promise<Translations[]> {
    const textval = text.toLowerCase();
    logger.info('Get Translation Called', {text});

    const translated = await this.model.find({
      en: textval,
    });
    if (translated) {
      return (translated as Translations[]);
    }
    logger.info('GetTranslation Successfull', {translated});

    return [] as Translations[];
  }
  async UpdateTranslation (
    targetLanguageCode: SupportedLanguages,
    text: string,
    translation: string,
  ): Promise<Translations[]> {
    const textval = text.toLowerCase();
    logger.info('Update Translation Called', {targetLanguageCode, textval, translation});

    // Check if document exists
    const existingDocument = await this.model.findOne({ en: textval });

    // If document doesn't exist, create a new one with a specific `_id`
    if (!existingDocument) {
      logger.info('Document Does not exist');

      const newDocument = await this.model.create({
        _id: new mongoose.Types.ObjectId(),
        en: textval,
        [`${targetLanguageCode}`]: translation,
      });
      logger.info('UpdateTranslation Successfull - created new document', {newDocument});

      return [newDocument] as Translations[];
    }

    // If the document exists, update it
    const updatedTranslation = await this.model.findOneAndUpdate(
      { en: textval },
      {
        $set: {
          [`${targetLanguageCode}`]: translation,
        },
      },
      { new: true },
    );
    logger.info('UpdateTranslation Successfull', {updatedTranslation});

    return [updatedTranslation] as Translations[];
  }

  async AddNewLanguage (targetLanguageCode: SupportedLanguages): Promise<TransaltionResult | null> {
    try {
      logger.info('Add New Language Called', { targetLanguageCode });

      // Fetch the English texts from the database
      const translatedDocuments = await this.model.find().select({ 'en': 1, '_id': 0 });
      const texts: Array<string> = [];

      // Extract the English translations
      Object.values(translatedDocuments).map((en) => {
        return texts.push(en.en);
      });

      // Translate the texts into the target language
      const result: TransaltionResult = await translateText(SupportedLanguages.EN, targetLanguageCode, texts);

      // If translation result is valid, update the translations
      if (result) {
        texts.map((text, index) => {
          if (result) {
            this.UpdateTranslation(targetLanguageCode, text, result[index] as string);
          }
          return null;
        });
      } else {
        throw new Error('Translation result is empty or invalid');
      }

      logger.info('Add New Language Successful', { targetLanguageCode });

      // Return the result of the translation process (optional, depending on your need)
      return result;

    } catch (error) {
      logger.error('Error in AddNewLanguage', { error: error });
      // Return an error message or result as needed
      return null; // Or you can return a specific error result based on your requirements
    }
  }

  async GetAllTranslation (): Promise<TranslationModuleResponse> {
    logger.info('GetAllTranslation Called');

    // Use aggregation to retrieve translations and distinct language keys
    const aggregationResult = await this.model.aggregate([
      {
        $project: {
          key: '$en',
          translations: '$$ROOT',
        },
      },
      {
        $group: {
          _id: null,
          translations: {
            $push: {
              k: '$key',
              v: '$translations',
            },
          },
        },
      },
      {
        $project: {
          _id: 0,
          translations: {
            $arrayToObject: {
              $map: {
                input: '$translations',
                as: 'item',
                in: { k: '$$item.k', v: '$$item.v' },
              },
            },
          },
        },
      },
    ]);
    logger.info('GetAllTranslation Successful');
    return aggregationResult[0];
  }

  async UpdateTranslationById (
    translationId: string,
    targetLanguageCode: SupportedLanguages,
    translation: string,
  ): Promise<Translations[]> {
    logger.info('UpdateTranslationById Called', {translationId, targetLanguageCode, translation});

    const updatedTranslation = await this.model.findOneAndUpdate(
      { _id: translationId },
      {
        $set: {
          [`${targetLanguageCode}`]: translation,
        },
      },
      {
        new: true,
      },
    );
    logger.info('UpdateTranslationById Successfull', {updatedTranslation});

    return updatedTranslation as unknown as Translations[];
  }
  async RemoveLanguage (targetLanguageCode: SupportedLanguages): Promise<void> {
    logger.info('RemoveLanguage Called', {targetLanguageCode});

    await this.model.updateMany(
      {}, // Matches all documents in the collection
      {
        $unset: { [targetLanguageCode]: '' }, // Removes the target language field
      },
    );
    logger.info('RemoveLanguage Success');
  }
  public async DeleteTranslationById (translationId: string): Promise<Translations | void> {
    logger.info('DeleteTranslationById Called', {translationId});

    try {
      const deletedTranslation : unknown = await this.model.findOneAndDelete({_id: translationId});
      logger.info('DeleteTranslationById Successfull', {deletedTranslation});

      return deletedTranslation as Translations;
    } catch (error) {
      logger.error('Error in DeleteTranslationById', error);

      throw error; // Rethrow the error to reject the promise
    }
  }
  public async TranslateProjectData (translationId: string): Promise<Translations | void> {
    logger.info('DeleteTranslationById Called', {translationId});

    try {
      const deletedTranslation : unknown = await this.model.findOneAndDelete({_id: translationId});
      logger.info('DeleteTranslationById Successfull', {deletedTranslation});

      return deletedTranslation as Translations;
    } catch (error) {
      logger.error('Error in DeleteTranslationById', error);

      throw error; // Rethrow the error to reject the promise
    }
  }

  async AddTranslation (
    translation: object,
    sourceLanguageCode: SupportedLanguages,
    text: string,
  ): Promise<Translations> {
    logger.info('StoreTranslation Called', { translation });

    const translatedDocument: TranslationDocument = {};

    const translationPromises = Object.entries(translation).map(async ([key, value]) => {
      const languageKey = key as SupportedLanguages;
      if (key && value) {
        translatedDocument[languageKey] = value;
        return;
      } else if (key && !value) {
        const translatedText = await translateText(sourceLanguageCode, key as SupportedLanguages, text);

        if (translatedText !== null) {
          if (Array.isArray(translatedText)) {
            const firstValidTranslation = translatedText[0];
            if (firstValidTranslation) {
              translatedDocument[languageKey] = firstValidTranslation;
            }
          } else {
            translatedDocument[languageKey] = translatedText;
          }
        }
      }
    });

    await Promise.all(translationPromises);

    try {
      const obj = new this.model({ _id: new mongoose.Types.ObjectId(), ...translatedDocument });
      const storedTranslation = await obj.save();
      logger.info('Translation Stored Successfully', {
        storedTranslation,
      });

      return storedTranslation as Translations;
    } catch (error) {
      logger.error('Error in StoreTranslation', {
        error,
        translation,
      });
      console.log(error);
      throw new Error('Failed to store translation');
    }
  }
}

import { validationResult, check } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import logger from '../../../config/logger';

const UpdateAmenityValidate = (
  req: Request,
  res: Response,
  next: NextFunction,
): void => {

  // Const files = req.files as UploadedFiles | undefined;

  const requiredTextFields = [
    'project_id',
    'amenity_id',
  ];

  const missingTextFields = requiredTextFields.filter(
    (field) => !(field in req.body),
  );

  if (missingTextFields.length > 0) {
    logger.error(`Missing text fields: ${missingTextFields.join(', ')}`);
    res.status(400).json({
      error: `Missing text fields: ${missingTextFields.join(', ')}`,
    });
  } else {
    check('type', 'Media Type is required').optional().isString().run(req);
    check('link', 'Media Link should be a string ').optional().isString().run(req);
    check('file', 'Media Link should be a string').optional().isString().run(req);
    const errors = validationResult(req);

    if (!errors.isEmpty()) {
      logger.error('Errors:', {message: errors.array()});
    } else {
      next();
    }
  }
};
export default UpdateAmenityValidate;

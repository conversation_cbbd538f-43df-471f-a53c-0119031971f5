import { Request, Response } from 'express';
import { MasterSceneModule } from '../../../modules/masterScene';
import { MasterSVGModule } from '../../../modules/masterSVG';
import logger from '../../../config/logger';

export async function getAllScenes (
  request: Request,
  response: Response,
): Promise<void> {
  const organization_id = request.params.organization_id as string;
  if (organization_id === undefined || organization_id === ''){
    response.status(400).send({status: 0, error: 'organization_id not found'});
    return;
  }
  //   Pass organization_id in this ProjectModule
  const masterScene = new MasterSceneModule(organization_id);

  const masterScenes = await masterScene.getAllScenes();
  const svg = new MasterSVGModule(organization_id);
  if (masterScenes) {
    const keys = Object.keys(masterScenes);
    const promises = keys.map(async (key) => {
      const svgData = await svg.getSvgById(key);
      if (svgData === null || Object.keys(svgData).length === 0) {
        return;
      }
      masterScenes[key].svgData = svgData;
    });
    await Promise.all(promises).then(() => {
      response.status(200).json({ status: 1, data: masterScenes});
    });
  } else {
    logger.error('Error in getAllScenes');
    response.status(500).json({ status: 0, error: 'scene not found' });
  }
}

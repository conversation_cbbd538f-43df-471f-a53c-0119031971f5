import {httpRequestTimer} from '../utilis/metrics';
import { NextFunction, RequestHandler, Response } from 'express';
import { ExtendedRequest } from '../types/extras';

export const PrometheusRequestTimer:RequestHandler = function (
  request: ExtendedRequest,
  response: Response,
  next: NextFunction,
):void{
  const start = Date.now(); // Start timer
  // Define a function to be executed after sending response
  response.on('finish', () => {
    const responseTimeInMs = Date.now() - start; // Calculate response time

    // Assuming httpRequestTimer is a Prometheus timer
    httpRequestTimer.labels(request.method, request.path, response.statusCode.toString()).observe(responseTimeInMs);
  });

  next();
};

import { Request, Response } from 'express';
import { ProjectLandmarkModule } from '../../../modules/projectLandmark';
import { getAllRoutesInput } from '../../../types/projectLandmark';
import logger from '../../../config/logger';

export async function fetchLandmarks (
  request: Request,
  response: Response,
): Promise<void> {
  const project_id = request.body.project_id;
  const projectData: getAllRoutesInput = {
    projLat: request.body.lat,
    projLong: request.body.lng,
  };
  const organization_id = request.headers.organization as string;
  const projectLandmark = new ProjectLandmarkModule(project_id, organization_id);
  const projectLandmarkData = await projectLandmark.fetchProjectLandmarks(projectData);
  if (projectLandmarkData) {
    response.status(200).json({ status: 1, data: 'Landmarks fetched successfully'});
  } else {
    logger.error('Unable to fetch landmarks:');
    response.status(404).json({ status: 0, error: 'Unable to fetch landmarks' });
  }
}

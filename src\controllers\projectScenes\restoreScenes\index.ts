import { ExtendedRequest } from '../../../types/extras';
import { projectScene } from '../../../types/projectScene';
import { ProjectSceneModule } from '../../../modules/projectScene';
import { Response } from 'express';
import logger from '../../../config/logger';
export async function restoreScenes (
  request: ExtendedRequest,
  response: Response,
): Promise<projectScene | void> {
  const organization_id = request.organization_id as string;
  const { project_id } = request.params;
  const scene = new ProjectSceneModule(project_id, organization_id);
  const trash_id = request.body.trash_id;
  await scene
    .restoreScenes(organization_id, project_id, trash_id)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Scene got restored'});
    })
    .catch((error: Error) => {
      logger.error('Error in restoreScenes', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error while restoring scenes : '+ error });
    });
}

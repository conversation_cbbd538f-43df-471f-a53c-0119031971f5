import { UploadUnitplanFiles } from '../../helpers/uploadFirebase';
import { OrganizationModule } from '../../modules/organization';
import { OrgThumbnail } from '../../types/organization';
import { Request, Response } from 'express';

export async function HandleThumbnailOrg (
  request: Request,
  response: Response,
): Promise<OrgThumbnail | void> {
  const organization_id = request.headers.organization;
  const organization = new OrganizationModule();
  const requestFiles = request.files;
  if (requestFiles === undefined) {
    response.status(400).json({ error: 'Image fields are required.' });
    return;
  }
  if (typeof organization_id !== 'string') {
    response
      .status(400)
      .json({ error: 'Organization ID is missing or invalid.' });
    return;
  }
  UploadUnitplanFiles(requestFiles, 'OrgThumbnail').then(
    (urlObject: { [key: string]: string }) => {
      console.log(organization_id, urlObject.thumbnail);
      organization
        .uploadThumbnail(organization_id, urlObject.thumbnail)
        .then(() => {
          response
            .status(201)
            .json({ status: 1, msg: 'uploaded thumbnail to Org!' });
        })
        .catch((error: Error) => {
          response.status(500).json({
            status: 0,
            error: 'Error while creating the amenity' + error,
          });
        });
    },
  );
}

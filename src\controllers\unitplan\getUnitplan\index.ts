import { Request, Response } from 'express';
import { unitplanModule } from '../../../modules/unitplan';
import logger from '../../../config/logger';

export async function GetUnitplan (
  request: Request,
  response: Response,
): Promise<void> {
  const { unitplan_id, project_id } = request.params;
  const organization_id = request.headers.organization as string;
  const unitplan = new unitplanModule(project_id, organization_id);
  const unitplanData = await unitplan.getUnitplan(unitplan_id);

  if (unitplanData) {
    response.status(200).json({ status: 1, data: unitplanData });
  } else {
    logger.error('unitplans not found');
    response.status(404).json({ status: 0, error: 'unitplan not found' });
  }
}

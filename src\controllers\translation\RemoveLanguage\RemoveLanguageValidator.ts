import { Request, Response, NextFunction } from 'express';
import { validationResult, query } from 'express-validator';
import { SupportedLanguages } from '../../../types/projects';
import logger from '../../../config/logger';

const RemoveLanguageValidator = [
  query('targetLanguageCode', 'Target Language Code is required').notEmpty(),
  query('targetLanguageCode', 'Target Language Code is not supported').isIn(Object.values(SupportedLanguages)),
  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      logger.error('Error in RemoveLanguageValidator', errors);

      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default RemoveLanguageValidator;

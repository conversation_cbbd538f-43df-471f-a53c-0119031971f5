import { ObjectId } from 'mongodb';
import logger from '../../config/logger';
import { ProjectModule } from '../projects';
import { AmenityModule } from '../amenity';
import { GalleryModule } from '../gallery';
import { bulkGalleryUpdateType, bulkUpdateQuery, bulkUpdateType, galleryPayload } from '../../types/projects';

export class DataSyncUpModule {

  public async AmenitySyncUp (organization_id:string, project_id:string): Promise<string> {
    logger.info('Amenity SyncUp Called');

    try {
      // Fetch the existing project document
      const project = new ProjectModule(organization_id);
      const existingProject = await project.getProjectById(project_id);
      if (!existingProject) {
        throw new Error('Project not found');
      }

      const sourceCategory = new AmenityModule(project_id, organization_id);
      const categories = await sourceCategory.getCategories();
      if (!categories) {
        throw new Error('Failed to fetch categories');
      }

      const existingAmenities = (existingProject?.projectSettings?.amenity || {}) as Record<string, bulkUpdateQuery>;

      let updatedAmenities: Record<string, bulkUpdateQuery> = {};
      const updatedCategoryIds = new Set<string>();

      if (Object.keys(existingAmenities).length === 0) {
        updatedAmenities = categories.reduce((acc, category, index) => {
          const newId = new ObjectId().toHexString();
          acc[newId] = {
            id: newId,
            order: index + 1,
            name: { category: category.category, count: category.count },
          };
          return acc;
        }, {} as Record<string, bulkUpdateQuery>);
      } else {
        const amenityMap = new Map<number, bulkUpdateQuery>(); // Key: order, Value: amenity object
        Object.values(existingAmenities).forEach((amenity) => {
          if (amenity.order !== undefined) {
            amenityMap.set(amenity.order, amenity);
          }
        });

        for (let i = 0; i < categories.length; i++) {
          const category = categories[i]; // New category from getCategories
          const existingAmenity = Array.from(amenityMap.values()).find(
            (obj) => obj.name?.category === category.category,
          );

          if (existingAmenity) {
            updatedAmenities[existingAmenity.id] = {
              ...existingAmenity,
            };
            updatedCategoryIds.add(existingAmenity.id);
          } else {
            const newId = new ObjectId().toHexString();
            updatedAmenities[newId] = {
              id: newId,
              order: categories.length,
              name: { category: category.category, count: category.count },
            };
            updatedCategoryIds.add(newId);
          }
        }
      }

      const deletedAmenities = Object.keys(existingAmenities).filter(
        (id) => !updatedCategoryIds.has(id), // If it's not in the updated list, it was deleted
      );

      const updatedQuery:bulkUpdateType = {
        project_id: project_id,
        query: Object.values(updatedAmenities),
      };

      try {
        if (deletedAmenities.length !== 0) {
          project
            .deleteSettings(project_id, deletedAmenities, 'amenity')
            .then((value) => {
              console.log('Success in Deletion', value);
            })
            .catch((err) => {
              throw new Error(err); // Proper error handling
            });
        }
      } catch (error) {
        console.error('Error in Amenity deletion:', error);
      }

      const updatedProject:string = await project.updateAmenitySettings(updatedQuery);
      if (updatedProject) {
        logger.info('updateAmenitySettings Successful', { updatedProject });
        return 'Documents updated successfully';
      }
      logger.error('Error while update Amenity Sync Up');
      throw new Error('Error while update Amenity Sync Up');
    } catch (error) {
      logger.error('Error in Amenity SyncUp', { message: error });
      throw error;
    }
  }

  public async GallerySyncUp (organization_id: string, project_id: string): Promise<string>{
    logger.info('Gallery SyncUp Called');
    console.log(organization_id, project_id, 'inside Module');

    try {
      // Fetch the existing project document
      const project = new ProjectModule(organization_id);
      const existingProject = await project.getProjectById(project_id);
      if (!existingProject) {
        throw new Error('Project not found');
      }

      const sourceCategory = new GalleryModule(project_id, organization_id);
      const categories = await sourceCategory.getCategories();
      if (!categories) {
        throw new Error('Failed to fetch categories');
      }

      const existingGalleries = (existingProject?.projectSettings?.gallery || {}) as Record<string, galleryPayload>;
      let updatedGalleries: Record<string, galleryPayload> = {};

      const updatedCategoryIds = new Set<string>();

      if (Object.keys(existingGalleries).length === 0) {
        updatedGalleries = categories.reduce((acc, category, index) => {
          const newId = new ObjectId().toHexString();
          acc[newId] = {
            id: newId,
            order: index + 1,
            name: category,
          };
          return acc;
        }, {} as Record<string, galleryPayload>);
      } else {
        const galleryMap = new Map<number, galleryPayload>(); // Key: order, Value: amenity object
        Object.values(existingGalleries).forEach((gallery) => {
          if (gallery.order !== undefined) {
            galleryMap.set(gallery.order, gallery);
          }
        });
        for (let i = 0; i < categories.length; i++) {
          const category = categories[i]; // New category from getCategories
          const existingGallery = Array.from(galleryMap.values()).find(
            (obj) => obj.name === category,
          );

          if (existingGallery) {
            updatedGalleries[existingGallery.id] = {
              ...existingGallery,
            };
            updatedCategoryIds.add(existingGallery.id);
          } else {
            const newId = new ObjectId().toHexString();
            updatedGalleries[newId] = {
              id: newId,
              order: categories.length,
              name: category,
            };
            updatedCategoryIds.add(newId);
          }
        }
      }

      const deletedGalleries = Object.keys(existingGalleries).filter(
        (id) => !updatedCategoryIds.has(id), // If it's not in the updated list, it was deleted
      );

      const updatedQuery:bulkGalleryUpdateType = {
        project_id: project_id,
        query: Object.values(updatedGalleries),
      };

      try {
        if (deletedGalleries.length !== 0) {
          project
            .deleteSettings(project_id, deletedGalleries, 'gallery')
            .then((value) => {
              console.log('Success in Gallery Deletion', value);
            })
            .catch((err) => {
              throw new Error(err); // Proper error handling
            });
        }
      } catch (error) {
        console.error('Error in Gallery deletion:', error);
      }

      // Update the database with the synchronized amenity settings
      const updatedProject = await project.updateGallerySettings(updatedQuery);

      if (updatedProject) {
        logger.info('update Gallery Settings Successful', { updatedProject });
        return 'Gallery Documents updated successfully';
      }
      logger.error('Error while update Gallery Sync Up');
      throw new Error('Error while update Gallery Sync Up');

    } catch (error) {
      logger.error('Error in Gallery SyncUp', { message: error });
      throw error;
    }
  }
}

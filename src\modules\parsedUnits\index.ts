import { damacUnitStatus } from '../../types/damacUnitsSyncUp';
import logger from '../../config/logger';
import { measurementType, priceCurrency } from '../../types/units';
import { unitplanType } from '../../types/unitplan';
import { Building } from '../../types/building';
import axios from 'axios';

type UnitplanRecord = {
  status: number,
  data: Record<string, unitplanType>
};

type unitResponse = {
  status: number,
  data: object,
  totalCount: number,
  searchCount: number
}

type BuildingRecord = {
  status: number,
  data: Record<string, Building>
};

type parsedUnits = {
    unitNumber: string;
    area: number;
    towerName:string;
    floorName: number;
    status: damacUnitStatus;
    unitType: string;
    bedroomType:string;
    floorPlanImage?:string;
    floorPlan: string;
    price:number;
    currency: priceCurrency;
    unitImage: string;
    facing?:string;
    mirrored?: number;
    areaUnit?: measurementType;
    unitplanType:string;
};

type FilterDataPoints = {
  status: number,
  data:{
    buildingData?: {
      [key: string]: {
        id: string;
        name: string;
      }
    };
    unitplanData?: {
      uniqueBedrooms?: string[];
      minMeasurement?: number;
      maxMeasurement?: number;
      measurementType?: string;
    };
    unitData?: {
      minFloor?: number;
      maxFloor?: number;
      minPrice?: number;
      maxPrice?: number;
      currencyType?: string;
    };
  }
}

function getAvailabilityFilterOptionsFromData (units: parsedUnits[]) {
  // Use an object to track unique statuses
  const uniqueStatuses: Record<string, boolean> = {};

  // Collect all unique status values from the units
  units.forEach((unit) => {
    if (unit.status) {
      uniqueStatuses[unit.status] = true;
    }
  });

  // Convert the object keys to an options array
  const availabilityOptions = Object.keys(uniqueStatuses).map((status) => status);

  return availabilityOptions;
}

function getUniqueUnitTypes (unitplanList: Record<string, unitplanType>) {
  // Use an object to track unique unit types
  const uniqueTypes: Record<string, boolean> = {};

  // Check each unitplan for commercial designation
  Object.values(unitplanList).forEach((unitplan) => {
    // Check if unitplan has is_commercial property at runtime
    const isCommercial = 'is_commercial' in unitplan ? unitplan.is_commercial === true : false;

    // Add the appropriate type to our unique types
    if (isCommercial) {
      uniqueTypes.Commercial = true;
    } else {
      uniqueTypes.Residential = true;
    }
  });

  // If no unit types found from unitplans, default to at least "Residential"
  const unitTypeOptions = Object.keys(uniqueTypes);
  if (unitTypeOptions.length === 0) {
    return ['Residential'];
  }

  return unitTypeOptions;
}

export class ParsedUnitModule {
  private async getListOfUnits (organization:string, projectID:string): Promise<object> {
    try {
      const response = await axios.get(
        process.env.BASE_URL+`publicapis/organization/${organization}/project/${projectID}/getListofUnits`,
      );
      logger.info('Initial token obtained successfully');
      return response.data;
    } catch (error) {
      logger.error('Error getting initial token', { error });
      throw new Error('Failed to get initial token');
    }
  }
  private async getListOfUnitplan (organization:string, projectID:string): Promise<object> {
    try {
      const response = await axios.get(
        process.env.BASE_URL+`publicapis/organization/${organization}/project/${projectID}/getListOfUnitplan`,
      );
      logger.info('Initial token obtained successfully');
      return response.data;
    } catch (error) {
      logger.error('Error getting initial token', { error });
      throw new Error('Failed to get initial token');
    }
  }
  private async GetListOfBuildings (organization:string, projectID:string): Promise<object> {
    try {
      const response = await axios.get(
        process.env.BASE_URL+`publicapis/organization/${organization}/project/${projectID}/getListOfBuildings`,
      );
      logger.info('Initial token obtained successfully');
      return response.data;
    } catch (error) {
      logger.error('Error getting initial token', { error });
      throw new Error('Failed to get initial token');
    }
  }
  private async getFilterData (organization:string, projectID:string): Promise<object> {
    try {
      const response = await axios.get(
        process.env.BASE_URL+`publicapis/organization/${organization}/project/${projectID}/filterDataPoints`,
      );
      logger.info('Initial token obtained successfully');
      return response.data;
    } catch (error) {
      logger.error('Error getting initial token', { error });
      throw new Error('Failed to get initial token');
    }
  }
  public async getParsedUnits (
    projectID: string,
    organization: string,
    query: Record<string, string>,
  ): Promise<object | null> {
    try {
      const data = await this.getListOfUnits(organization, projectID) as unitResponse;
      const unitplan = await this.getListOfUnitplan(organization, projectID) as UnitplanRecord;
      const building = await this.GetListOfBuildings(organization, projectID) as BuildingRecord;
      const publicapi = await this.getFilterData(organization, projectID) as FilterDataPoints;
      const unitplanList = unitplan.data;
      const buildingList = building.data;
      const filterDataPoints = publicapi.data;
      if (!data || !unitplanList || !buildingList) {
        logger.error('Missing required data for transformation');
        return null;
      }

      const transformedUnits: parsedUnits[] = [];
      const units = Object.values(data.data);
      units.forEach((unit) => {
        const unitplanData = unit.unitplan_id ? unitplanList[unit.unitplan_id] : null;

        const buildingData = unit.building_id ? buildingList[unit.building_id] : null;

        const transformedUnit: parsedUnits = {
          unitNumber: unit.name,
          area: unitplanData?.measurement || 0,
          towerName: buildingData?.name || '',
          floorName: parseInt(unit.floor_id),
          status: unit.status,
          unitType: unitplanData?.is_commercial === true ? 'Commercial' : 'Residential',
          unitplanType: unitplanData?.type || '',
          bedroomType: unit.bedroom || unitplanData?.bedrooms || null,
          floorPlanImage: 'N/A',
          floorPlan: '',
          price: parseFloat(unit.price),
          currency: (unit.currency).toUpperCase() as priceCurrency,
          unitImage: unitplanData?.image_url || '',
          facing: unit?.metadata?.facing || null,
          mirrored: unit?.metadata?.mirrored || null,
          areaUnit: unitplanData?.measurement_type || undefined,
          ...unit.metadata,
        };
        transformedUnits.push(transformedUnit);
      });

      const uniqueFacingOptions: string[] = [];
      transformedUnits.forEach((unit) => {
        if (unit.facing && !uniqueFacingOptions.includes(unit.facing)) {
          uniqueFacingOptions.push(unit.facing);
        }
      });

      const uniqueMirroredOptions: number[] = [];
      transformedUnits.forEach((unit) => {
        if (
          (unit.mirrored === 0 || unit.mirrored === 1) &&
          !uniqueMirroredOptions.includes(unit.mirrored)
        ) {
          uniqueMirroredOptions.push(unit.mirrored);
        }
      });

      const uniqueUnitplanTypeOptions: string[] = [];
      transformedUnits.forEach((unit) => {
        if (unit.unitplanType && !uniqueUnitplanTypeOptions.includes(unit.unitplanType)) {
          uniqueUnitplanTypeOptions.push(unit.unitplanType);
        }
      });

      const metadataFilters: Record<
        string,
        { type: 'string' | 'number'; values: (string | number)[] }
      > = {};

      Object.values(data.data).forEach((unit) => {
        const metadata = unit.metadata || {};

        Object.entries(metadata).forEach(([key, value]) => {
          if (key === 'facing' || key === 'mirrored') {
            return;
          }

          const valueType = typeof value;
          if (valueType === 'string' || valueType === 'number') {
            if (!metadataFilters[key]) {
              metadataFilters[key] = {
                type: valueType,
                values: [],
              };
            }

            if (
              (typeof value === 'string' || typeof value === 'number') &&
              !metadataFilters[key].values.includes(value as string | number)
            ) {
              metadataFilters[key].values.push(value as string | number);
            }
          }
        });
      });

      const filters = [
        {
          filterName: 'Availability',
          key: 'status',
          type: 'button',
          parameters: {
            options: getAvailabilityFilterOptionsFromData(units),
            'gridSize': 2,
          },
        },
        {
          filterName: 'Tower',
          key: 'towerName',
          type: 'button',
          parameters: {
            options: filterDataPoints?.buildingData ?
              Object.values(filterDataPoints?.buildingData).map((value) => value.name) : [],
            gridSize: 2,
          },
        },
        {
          filterName: 'Unit Type',
          key: 'unitType',
          type: 'button',
          parameters: {
            options: getUniqueUnitTypes(unitplanList),
            gridSize: 3,
          },
        },
        {
          filterName: 'Bedrooms',
          key: 'bedroomType',
          type: 'button',
          parameters: {
            options: filterDataPoints?.unitplanData?.uniqueBedrooms,
            gridSize: 2,
          },
        },
        {
          filterName: 'Area',
          key: 'areaUnit',
          type: 'slider',
          parameters: {
            min: filterDataPoints?.unitplanData?.minMeasurement,
            max: filterDataPoints?.unitplanData?.maxMeasurement,
            stepSize: 100,
            unit: filterDataPoints?.unitplanData?.measurementType,
          },
        },
        {
          filterName: 'Floor',
          key: 'floorName',
          type: 'slider',
          parameters: {
            min: filterDataPoints?.unitData?.minFloor,
            max: filterDataPoints?.unitData?.maxFloor,
            stepSize: 1,
            unit: '',
          },
        },
        {
          filterName: 'Price',
          key: 'price',
          type: 'slider',
          parameters: {
            min: filterDataPoints?.unitData?.minPrice,
            max: filterDataPoints?.unitData?.maxPrice,
            stepSize: 1000,
            unit: filterDataPoints?.unitData?.currencyType,
          },
        },
        {
          filterName: 'UnitplanType',
          key: 'unitplanType',
          type: 'button',
          parameters: {
            options: uniqueUnitplanTypeOptions,
            gridSize: 2,
          },
        },
        {
          filterName: 'Facing',
          key: 'facing',
          type: 'button',
          parameters: {
            options: uniqueFacingOptions,
          },
        },
        {
          filterName: 'Mirrored',
          key: 'mirrored',
          type: 'button',
          parameters: {
            options: uniqueMirroredOptions,
          },
        },
      ];

      Object.entries(metadataFilters).forEach(([key, { type, values }]) => {
        const filterName = key.charAt(0).toUpperCase() + key.slice(1);

        if (type === 'string') {
          filters.push({
            filterName,
            key,
            type: 'button',
            parameters: {
              options: values as string[],
              gridSize: 2,
            },
          });
        } else if (type === 'number') {
          const sorted = values.sort((a, b) => Number(a) - Number(b));
          filters.push({
            filterName,
            key,
            type: 'slider',
            parameters: {
              min: sorted[0] as number,
              max: sorted[sorted.length - 1] as number,
              stepSize: 1,
              unit: '',
            },
          });
        }
      });

      const filteredFilters = Object.keys(query).length === 0
        ? filters : filters.filter((filter) => query[filter.key]);

      return {
        Inventory: {
          filters: filteredFilters,
          Units: transformedUnits,
        },
      };
    } catch (e) {
      logger.error('Sync Data Error:', e);
      return null;
    }
  }
}

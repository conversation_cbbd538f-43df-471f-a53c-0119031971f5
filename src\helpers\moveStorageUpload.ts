import { admin, bucketName} from '../config/firebase';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import os from 'os';

export async function CopyFirebaseItem (sourceUrl:string, destinationPath:string): Promise <string> {
  const bucket = admin.storage().bucket(bucketName);

  const fileName = path.basename(new URL(sourceUrl).pathname);

  const firebaseFilePath = path.posix.join(destinationPath, fileName);

  const tempFilePath = path.join(os.tmpdir(), fileName);

  // Download the file from the source URL
  const response = await axios({
    url: sourceUrl,
    method: 'GET',
    responseType: 'stream',
  });

  // Save the file locally
  const writer = fs.createWriteStream(tempFilePath);
  response.data.pipe(writer);

  await new Promise<void>((resolve, reject) => {
    writer.on('finish', resolve);
    writer.on('error', reject);
  });

  // Upload the file to Firebase Storage
  await bucket.upload(tempFilePath, {
    destination: firebaseFilePath,
    metadata: {
      contentType: response.headers['content-type'] || 'application/octet-stream',
    },
  });

  // Generate the permanent download URL
  const downloadUrl =
        `https://firebasestorage.googleapis.com/v0/b/${bucketName}/o/${encodeURIComponent(firebaseFilePath)}?alt=media`;

  return downloadUrl;
}

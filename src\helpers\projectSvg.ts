import puppeteer from 'puppeteer';
import axios from 'axios';
import { coordinatesObjectReference } from '../types/projectSVG';
interface StyleObject {
  styles: string;
}
interface ClassStyleMap {
  [className: string]: StyleObject;
}
export async function GetCoordinatesFromSvgUrl (svgurl: string): Promise<coordinatesObjectReference> {
  try {
    const response = await axios.get(svgurl, { responseType: 'text' });
    const svgData = response.data;

    const browser = await puppeteer.launch({
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
      headless: true,  // Use 'new' mode to ensure compatibility
    });
    const page = await browser.newPage();

    // Load the SVG data into the headless browser page
    await page.setContent(svgData);

    // Check for style tags and update styles inline
    await page.evaluate(() => {
      // Function to extract unique class styles from a style context
      function extractTheUniqueClassAndStyles (styleContext: string): ClassStyleMap {
        const classStyleRegex = /(\.[-\w]+(?:\s*,\s*\.[-\w]+)*)\s*\{([^}]*)\}/g;

        const classStyleObjects: ClassStyleMap = {};
        let match: RegExpExecArray | null;

        // Find all class styles
        while ((match = classStyleRegex.exec(styleContext)) !== null) {
          const selectors = match[1].split(',').map((s) => s.trim().substring(1)); // Remove the dot
          const styles = match[2].replace(/\s+/g, ' ').trim(); // Clean up styles
          selectors.forEach((className) => {
            if (classStyleObjects[className]) {
              classStyleObjects[className].styles += '; ' + styles; // Append existing styles
            } else {
              classStyleObjects[className] = { styles: styles }; // New
            }
          });
        }

        // Clean up styles
        for (const className in classStyleObjects) {
          if (Object.prototype.hasOwnProperty.call(classStyleObjects, className)) {
            const stylesArray = classStyleObjects[className].styles
              .split(';')
              .map((s) => s.trim())
              .filter((s) => s !== ''); // Remove empty styles and trim
            const uniqueStyles = Array.from(new Set(stylesArray)); // Remove duplicates
            classStyleObjects[className].styles = uniqueStyles.join('; ') + ';';
          }
        }

        return classStyleObjects;
      }

      const styleTag = document.querySelector('style');
      if (styleTag) {
        const styles = extractTheUniqueClassAndStyles(styleTag.textContent || '');
        const svgElements = document.querySelectorAll('svg *');

        svgElements.forEach((item) => {
          const classList = Array.from(item.classList);
          classList.forEach((className) => {
            if (styles[className]) {
              item.setAttribute('style', styles[className].styles);
              item.removeAttribute('class'); // Remove the class
              item.setAttribute('data-name', className); // Cross reference
            }
          });
        });

        // Remove the style tag after applying styles
        styleTag.remove();
      }
    });

    // Inject a script to manipulate the SVG
    const coordinatesObject: coordinatesObjectReference = await page.evaluate(() => {
      const svgElement = document.querySelector('svg');
      if (!svgElement) {
        throw new Error('No <svg> element found in the SVG data.');
      }

      const coordinatesObj: any = {};
      const svgChildren = Array.from(svgElement.children);

      svgChildren.forEach((item: any) => {
        if (item.tagName === 'g') {
          const bbox = item.getBBox();
          const id = item.id;
          const newGroup = `<g transform='translate(-${bbox.x}, -${bbox.y})'>${item.innerHTML}</g>`;
          coordinatesObj[id] = {
            g: newGroup,
            x: bbox.x,
            y: bbox.y,
            width: bbox.width,
            height: bbox.height,
          };
        }
      });

      return coordinatesObj;
    });

    await browser.close();
    return coordinatesObject;
  } catch (error) {
    throw new Error(`Error processing SVG: ${error}`);
  }
}

import {Request, Response} from 'express';
import { deep_zoom_status } from '../../../types/projectScene';
import { MasterSceneModule } from '../../../modules/masterScene/index';
export default async function updateSceneAPI (request:Request, response:Response): Promise<void> {
    interface updateScene {
        organization_id: string,
        project_id: string,
        scene_id: string
        type?: string,
        name?: string,
        active?: boolean,
        parent?: number,
        info_text?: string,
        building_id?: string,
        root?: boolean,
        clouds?: boolean,
        category?:string,
        position?:object,
        polar_angle?:object,
        distance?:object,
        auto_rotate?:boolean,
        highRes?:string,
        deep_zoom_status?:{
          type:string,
          enum:deep_zoom_status
        },
        deep_zoom_failed_info?:string,
        earth_position?: {
          x_axis:number,
          y_axis:number,
          z_axis:number
        };
      }
    const reqbody:updateScene = request.body;
    const masterScene = new MasterSceneModule(reqbody.organization_id);
    masterScene.updateScene(reqbody.scene_id, request.body).then(async (res) => {
      response.send({status: 1, data: res});
    }).catch((error) => {
      response.send({status: 0, message: error});
    });
}

import { Types } from 'mongoose';
export enum tourCategory {
    INTERIOR = 'interior',
    EXTERIOR = 'exterior'
  }
export enum tourType {
    CUSTOM = 'custom',
    EXTERNAL = 'external',
    MATTERPORT = 'matterport',
    MLE = 'MLE'
  }
export type coordinate = {
    x: string,
    y: string,
    z: string
}
export type Link = {
    _id: string,
    position: coordinate,
    text: string,
    destination_img_id: string
}
export type Image = {
    id: string,
    name: string,
    thumbnail: string,
    url: string,
    rotation: string,
    links: { [key: string]: Link },
    groupId: string,
    subGroupId: string,
    order: number
}

export type SubGroup= {
    _id: string,
    name: string,
    icon: string,
    order: number,
    images?: Record<string, Image>
}
export type Group = {
    _id: string,
    name: string,
    icon: string,
    order: number,
    subgroups: { [key: string]: SubGroup },
    images?: Record<string, Image>
}

export enum HotSpotType {
    SCENE = 'scene',
    INFO = 'info',
  }
export type VirtualTour = {
    _id: string;
    name: string ;
    description: string;
    organization: string;
    project_id: string;
    category: tourCategory;
    type: tourType;
    images:  { [key: string]: Image };
    groups:  { [key: string]: Group };
    created_at: string;
    updated_at: string;
    unitplan_id: string;
    space_id: string | null;
    link: string | null;
    initial_rotation:string
}
export type createVirtualTourData = {
    _id: Types.ObjectId;
    name: string ;
    description: string;
    organization: string;
    project_id: string;
    category: tourCategory;
    type: tourType;
    created_at: string;
    updated_at: string;
    unitplan_id: string;
    space_id: string | null;
    link: string | null;
}
export type addImageType = {
    id: string,
    name: string,
    thumbnail: string,
    url: string,
    rotation: string,
    order: number
}

export type updateImageInp = {
    name: string,
    thumbnail: string,
    url: string,
    rotation: string,
    order: number,
    groupId: string,
    subGroupId: string,
    tiles_url: string,
    tile_rendering_status: string,
    tile_rendering_failed_info: string
}
export type EnvVar = {
    name: string;
    value: string;
};
export type labels = {
    name:string,
    camera_name:string,
    position:{
        x: number,
        y: number,
        z: number
    },
    camera_position:{
        x: number,
        y: number,
        z: number
    },
    controls_target:{
        x: number,
        y: number,
        z: number
    },
    order:number,
    label_id?:string

}

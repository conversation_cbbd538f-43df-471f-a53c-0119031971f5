import { Request, Response, NextFunction } from 'express';
import { validationResult, header, query} from 'express-validator';
import { LeadSource, LeadStatus } from '../../../types/leads';

const getLeadAnalyticsValidator = [
  header('organization', 'Organization ID is required').notEmpty(),
  header('accesstoken', 'Access Token is required').notEmpty(),
  query('lead_source', 'Lead Source is Invalid').optional().isIn(Object.values(LeadSource)),
  query('status', 'Lead Status is Invalid').optional().isIn(Object.values(LeadStatus)),

  (req: Request, res: Response, next: NextFunction): void => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }
    next();
  },
];

export default getLeadAnalyticsValidator;

import { NextFunction, Response  } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { validationResult } from 'express-validator';
import logger from '../../../config/logger';

const CreateHotSpotValidate = (
  req: ExtendedRequest,
  res: Response,
  next: NextFunction,
): void => {
  const ValidateHotspotFields = [
    'project_id',
    'minimap_id',
    'text',
    'x',
    'y',
    'destination',
  ];

  const missingTextFields = ValidateHotspotFields.filter(
    (field) => !(field in req.body),
  );

  if (missingTextFields.length > 0) {
    logger.error(`Missing text fields: ${missingTextFields.join(', ')}`);
    res.status(400).send({status: 0, error: `Missing text fields: ${missingTextFields.join(', ')}`});
  } else {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log(errors);
    } else {
      next();
    }
  }
};

export default CreateHotSpotValidate;

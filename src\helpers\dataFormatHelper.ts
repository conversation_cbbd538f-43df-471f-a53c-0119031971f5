import { Types } from 'mongoose';
export interface UnknownObject {
    [key: string]: string | object | Types.ObjectId | number;
}

export function arrayToObject (arrayOfObjects: Array<UnknownObject>, index:string = '_id'): object {
  const resultObject:UnknownObject  = {};

  arrayOfObjects.forEach((obj:UnknownObject) => {
    if (obj[index]) {
      const key: string = obj[index].toString();
      resultObject[key] = obj;
    }
  });

  return resultObject;
}

export function convertDurationToMinutes (durationText: string): number {
  let totalMinutes = 0;
  const timeParts = durationText.split(' ');

  for (let i = 0; i < timeParts.length; i++) {
    if (timeParts[i].includes('hour')) {
      totalMinutes += parseInt(timeParts[i - 1]) * 60;
    } else if (timeParts[i].includes('min')) {
      totalMinutes += parseInt(timeParts[i - 1]);
    }
  }

  return totalMinutes;
}

export function convertDistance (distanceStr: string): number {
  const parts = distanceStr.split(' ');
  const distanceNumber = parseFloat(parts[0]) || 0;
  return distanceNumber;
}

export const ToLowerCase = (input: string): string => input.toLowerCase();

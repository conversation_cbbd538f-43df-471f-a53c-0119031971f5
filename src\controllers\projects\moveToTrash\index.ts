import { ExtendedRequest } from '../../../types/extras';

import { Response } from 'express';
import logger from '../../../config/logger';
import { ProjectModule } from '../../../modules/projects';
export async function moveToTrash (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const { project_id } = request.params;
  const organization_id = request.organization_id as string;
  const project = new ProjectModule(organization_id);
  const timeStamp = request.body.timeStamp;

  await project
    .moveToTrash(project_id, organization_id, timeStamp)
    .then(() => {
      response.status(201).json({ status: 1, message: 'Data moved to trash' });
    })
    .catch((error: Error) => {
      logger.error('Error in moveToTrash', {message: error});
      response
        .status(500)
        .json({ status: 0, error: 'Error moving project to trash: '+ error });
    });
}

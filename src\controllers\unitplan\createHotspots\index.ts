import { Request, Response } from 'express';
import { unitplanModule } from '../../../modules/unitplan';
import logger from '../../../config/logger';

export default async function CreateHotspots (
  request: Request,
  response: Response,
): Promise<void> {
  const { project_id, unitplan_id, hotspots } = request.body;
  const organization_id = request.headers.organization as string;

  const createHotspot = {
    text: hotspots.text,
    x: hotspots.x,
    y: hotspots.y,
    scale: hotspots.scale,
    type: hotspots.type,
    ...(hotspots.image_id && { image_id: hotspots.image_id}),
    ...(hotspots.label_id && { label_id: hotspots.label_id}),
    ...(hotspots.group_id && { group_id: hotspots.group_id}),
    ...(hotspots.subGroup_id && { subGroup_id: hotspots.subGroup_id}),
  };

  const unitplan = new unitplanModule(project_id, organization_id);
  unitplan.createHotspots(unitplan_id, createHotspot).then((res) => {
    response.status(200).json({status: 1, data: res});
  }).catch((error: Error) => {
    logger.error('Error in create hotspot', {message: error});
    response.status(500).json({status: 0, error: 'Error create hotspots '+ error});
  });
}

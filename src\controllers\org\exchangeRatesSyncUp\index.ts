import { Response } from 'express';
import { ExtendedRequest } from '../../../types/extras';
import { CurrencyFetchModule } from '../../../modules/damac/currencyIndex';
import { UpdateOrganizationInput } from '../../../types/organization';
import { OrganizationModule } from '../../../modules/organization';
import logger from '../../../config/logger';

export default async function exchangeRatesSyncUp (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const currencyModule = new CurrencyFetchModule();

  const organizationId = request.headers.organization as string;

  if (!organizationId) {
    response.send({ status: 0, error: 'No organization ID provided' });
    return;
  }

  try {
    const resultData = await currencyModule.fetchCurrencyRates();

    const updatePayload: UpdateOrganizationInput = {
      baseCurrency: resultData.baseCurrency,
      exchangeRatio: resultData.exchangeRatio,
    };

    const organization = new OrganizationModule();
    const updateResult = await organization.UpdateOrganization(organizationId, updatePayload);

    if (updateResult) {
      response.send({ status: 1, data: 'Sync completed successfully.' });
    } else {
      logger.error('Error syncing exchange rates');
      response.send({ status: 0, error: 'Error syncing exchange rates' });
    }
  } catch (error) {
    logger.error('An error occurred while syncing exchange rates.');
    response.send({
      status: 0,
      error: `Error syncing exchange rates: ${error}`,
    });
  }

}

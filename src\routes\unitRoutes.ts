import express from 'express';
import { CreateUnits } from '../controllers/units/CreateUnits';
import { BulkCreateUnits } from '../controllers/units/BulkCreateUnits';
import { GetListofUnits } from '../controllers/units/GetListofUnits';
import { UpdateUnit } from '../controllers/units/UpdateUnit';
import { DeletedUnit } from '../controllers/units/DeleteUnit';
import { getFilteredUnits } from '../controllers/units/GetFilteredUnits';
import { authMiddleware } from '../middlewares/DashboardAccess';
import { organizationAccessMiddleware } from '../middlewares/OrganizationAccess';
import { accessControlMiddleware } from '../middlewares/AccessControl';
import createUnitValidate from '../controllers/units/CreateUnits/CreateUnitValidate';
import updateUnitValidate from '../controllers/units/UpdateUnit/updateUnitValidate';
import moveToTrashValidate from '../controllers/units/moveToTrash/moveToTrashValidator';
import { moveToTrash } from '../controllers/units/moveToTrash';
import restoreUnitValidate from '../controllers/units/restoreUnits/restoreUnitValidator';
import { restoreUnit } from '../controllers/units/restoreUnits';
import { getDamacUnitsList } from '../controllers/Damac/DamacGetUnits';
import damacValidator from '../controllers/Damac/DamacGetUnits/damacValidator';
import { damacUnitSyncup } from  '../controllers/damacUnitSyncup';
import { verifyGoogleToken } from '../middlewares/GoogleTokenChecker';
const router = express.Router();

router.post('/BulkCreateUnit', authMiddleware, organizationAccessMiddleware, accessControlMiddleware(['admin']),
  BulkCreateUnits);

router.post('/CreateUnit', authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']), createUnitValidate, CreateUnits);

router.get('/getListofUnits/:project_id', GetListofUnits);

router.get('/getFilteredUnits/:project_id', getFilteredUnits);

router.post('/updateUnit/:unit_id', authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']), updateUnitValidate, UpdateUnit);

router.post('/deleteUnit/:unit_id', authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']), DeletedUnit);

router.post(
  '/moveToTrash/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  moveToTrashValidate,
  moveToTrash,
);
router.post(
  '/restoreUnit/:project_id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  restoreUnitValidate,
  restoreUnit,
);

router.get(
  '/getDamacUntis/:id',
  authMiddleware,
  organizationAccessMiddleware,
  accessControlMiddleware(['admin']),
  damacValidator,
  getDamacUnitsList,
);

router.get(
  '/syncDamacUnits',
  verifyGoogleToken,
  damacUnitSyncup,
);
export default router;

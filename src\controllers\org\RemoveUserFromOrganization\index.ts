import { Response } from 'express';

import { getUserByEmail } from '../../../helpers/authUser';
import { OrganizationModule } from '../../../modules/organization';
import { ExtendedRequest } from '../../../types/extras';
import { sendMessageToChannel } from '../../../helpers/slackMessenger';
import logger from '../../../config/logger';
export default async function RemoveUserFromOrganization (
  request: ExtendedRequest,
  response: Response,
): Promise<void> {
  const email = request.body.email;
  if (!email) {
    response.send({ status: 0, error: 'Missing Email id' });
    return;
  }
  const organization = new OrganizationModule();
  const organization_id = request.organization_id;
  if (!organization_id) {
    response.send({ status: 0, error: 'no organization is found' });
    return;
  }
  const webhookUrl = process.env.SLACK_WEBHOOKURL as string;
  sendMessageToChannel(
    webhookUrl, 'removed user',
    JSON.stringify(
      {headers: request.headers, body: request.body, organization_id: request.organization_id},
    ));
  getUserByEmail(email)
    .then((UserRecord) => {
      if (UserRecord) {
        if (UserRecord.email) {
          organization
            .RemoveUserFromOrganization(organization_id, UserRecord.email)
            .then(() => {

              response.send({ status: 1, data: true });
            })
            .catch((error) => {
              logger.error('Error ', {message: error});
              response.send({ status: 0, error: error.message });
            });
        } else {
          response.send({ status: 0, error: 'user record not found' });
        }
      } else {

        response.send({ status: 0, error: 'user record not found' });
      }
    })
    .catch((error) => {
      logger.error('Error while getting user record ', {message: error});
      response.send({
        status: 0,
        error: 'Error while getting user record' + error,
      });
    });
}
